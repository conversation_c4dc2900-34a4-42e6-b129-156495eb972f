package kl.npki.base.core.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import kl.npki.base.core.annotation.Base64ValueConstraint;

import java.util.Base64;

/**
 * Base64校验器
 *
 * <AUTHOR>
 */
public class Base64ValueValidator implements ConstraintValidator<Base64ValueConstraint, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        try {
            // 不能使用org.apache.commons.codec.binary.Base64.decodeBase64()，该方法会正常解析，不会抛出异常
            Base64.getDecoder().decode(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}