package kl.npki.base.core.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.nbase.i18n.locale.LocaleContext;
import kl.npki.base.core.annotation.FieldRule;
import kl.npki.base.core.configs.FieldRuleConfig;
import kl.npki.base.core.configs.FieldValidationRuleConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.utils.ValidatorConstraintUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 根据正则配置规则，进行字段校验
 *
 * <AUTHOR> by niugang on 2025-07-02 14:04
 */
public class FieldRuleValidator implements ConstraintValidator<FieldRule, CharSequence> {


    public static final String KL_NPKI_BASE_CORE_ANNOTATION_FIELD_RULE_MESSAGE = "{kl.npki.base.core.annotation.FieldRule.message}";


    private static final String PARAM_ERROR_I18N_KEY = "kl.npki.base.core.i18n_param_error";

    private static final Logger LOGGER = LoggerFactory.getLogger(FieldRuleValidator.class);


    private String message;
    private String description;

    @Override
    public void initialize(FieldRule annotation) {
        this.message = annotation.message();
        description = annotation.description();
    }

    @Override
    public boolean isValid(CharSequence value, ConstraintValidatorContext context) {
        if (value == null || StringUtils.isBlank(value.toString())) {
            return true;
        }

        String fieldName = ValidatorConstraintUtil.extractFieldName(context);
        FieldValidationRuleConfig ruleConfig = BaseConfigWrapper.getFieldValidationRuleConfig();
        if (ruleConfig == null || CollectionUtils.isEmpty(ruleConfig.getRules())) {
            return true;
        }

        List<FieldRuleConfig> applicableRules = getApplicableRules(fieldName, ruleConfig.getRules());
        if (applicableRules.isEmpty()) {
            return true;
        }

        for (FieldRuleConfig config : applicableRules) {
            if (!matchesRegex(value.toString(), config.getRule())) {
                handleValidationError(context, fieldName, config);
                return false;
            }
        }

        return true;
    }

    /**
     * 获取适用于当前字段的所有规则
     */
    private List<FieldRuleConfig> getApplicableRules(String fieldName, List<FieldRuleConfig> allRules) {
        return allRules.stream()
            .filter(r -> StringUtils.isNotBlank(r.getTarget()))
            .filter(r -> {
                Set<String> targets = Stream.of(r.getTarget().split(","))
                    .map(String::trim)
                    .collect(Collectors.toSet());
                return targets.contains(fieldName);
            }).collect(Collectors.toList());
    }

    /**
     * 处理校验失败时的错误提示
     */
    private void handleValidationError(ConstraintValidatorContext context, String fieldName, FieldRuleConfig ruleConfig) {
        context.disableDefaultConstraintViolation();

        String errorMsg = buildErrorMessage(fieldName, ruleConfig);

        context.buildConstraintViolationWithTemplate(errorMsg)
            .addConstraintViolation();
    }

    /**
     * 构建错误信息
     */
    private String buildErrorMessage(String fieldName, FieldRuleConfig ruleConfig) {
        String specificMessage = getCommonMessage(ruleConfig);
        String commonSuffix = I18nUtil.tr(PARAM_ERROR_I18N_KEY);

        if (StringUtils.isNotBlank(description)) {
            return StringUtils.isNotBlank(specificMessage)
                ? I18nUtil.tr(description) + specificMessage
                : I18nUtil.tr(description) + commonSuffix;
        }

        if (!Objects.equals(message, KL_NPKI_BASE_CORE_ANNOTATION_FIELD_RULE_MESSAGE)) {
            return I18nUtil.tr(message);
        }

        return StringUtils.isNotBlank(specificMessage)
            ? fieldName + specificMessage
            : fieldName + commonSuffix;
    }

    private String getCommonMessage(FieldRuleConfig ruleConfig) {
        String language = LocaleContext.get().getLanguage();
        if (language.equalsIgnoreCase(Locale.US.getLanguage())) {
            return " " + ruleConfig.getEnMsg();
        }
        return ruleConfig.getZhMsg();
    }

    /**
     * 使用给定的正则表达式校验字符串
     */
    private static boolean matchesRegex(String value, String regex) {
        if (StringUtils.isBlank(regex)) {
            return true;
        }
        try {
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(value);
            return matcher.matches();
        } catch (Exception e) {
            LOGGER.error("Failed to compile regex pattern:{}  fieldValue:{}", regex, value);
            throw BaseValidationError.PARAM_ERROR.toException();
        }

    }
}
