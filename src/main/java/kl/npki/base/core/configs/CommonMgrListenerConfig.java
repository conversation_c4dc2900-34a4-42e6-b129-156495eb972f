package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.listener.CommonMgrListener;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static kl.npki.base.core.constant.BaseConstant.CONFIG_COMMON_MGR_PREFIX;

/**
 * <AUTHOR> href="mailto:<EMAIL>">dingqi</a>
 * @since 2025/07/07 10:23
 */
public class CommonMgrListenerConfig extends SwitchableRefreshableConfigAdaptor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 版本映射，key为mgr的id，value为版本对应的序号
     */
    private Map<String, Integer> versionMap = new ConcurrentHashMap<>();

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return CONFIG_COMMON_MGR_PREFIX;
    }

    @Override
    public ConfigRefreshListener configRefreshListener() {
        return new CommonMgrListener();
    }

    public Map<String, Integer> getVersionMap() {
        return versionMap;
    }

    public void setVersionMap(Map<String, Integer> versionMap) {
        this.versionMap = versionMap;
    }
}
