package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

import java.util.List;

/**
 * 数据库密码配置类
 * 工行需要从配置中读取数据库密码
 */
public class DbPasswdConfig  extends SwitchableRefreshableConfigAdaptor {

    private List<DbPasswdInfo> dbPasswdInfoList;

    public List<DbPasswdInfo> getDbPasswdInfoList() {
        return dbPasswdInfoList;
    }

    public void setDbPasswdInfoList(List<DbPasswdInfo> dbPasswdInfoList) {
        this.dbPasswdInfoList = dbPasswdInfoList;
    }

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.passwdSeparate.db";
    }
}
