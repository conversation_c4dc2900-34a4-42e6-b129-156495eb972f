package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * Web 配置类，用于管理与 Web 相关的配置项，例如 IP 白名单等
 *
 * <AUTHOR>
 * @create 2025/7/1 下午3:16
 */
public class WebConfig extends SwitchableRefreshableConfigAdaptor {

    /**
     * 是否启用客户端IP校验，如果关闭校验调用getClientIpv4whitelist和getClientIpv6whitelist方法将返回默认值，
     */
    private boolean clientIpWhitelistEnabled = true;

    /**
     * 客户端IPv4白名单
     * 默认值为0.0.0.0/0，表示允许所有IPv4地址访问。
     */
    private String clientIpv4whitelist;

    /**
     * 客户端IPv6白名单
     * 默认值为::/0，表示允许所有IPv6地址访问。
     */
    private String clientIpv6whitelist;

    /**
     * 客户端与服务器允许的最大时间偏差。默认值为10分钟（10 * 60 * 1000 毫秒）。
     */
    private Long maxAllowedTimeDriftMs = 10 * 60 * 1000L;

    /**
     * PKI中间件的URL列表，多个URL用空格分隔，不建议配置多个，配置多个AppScan漏洞扫描可能会提示 “过多的回退机制” 中危漏洞
     */
    private String pkiMiddlewareUrls = "https://127.0.0.1:16080 https://127.0.0.1:16081";

    /**
     * 默认值 0.0.0.0/0，表示允许所有IPv4地址访问。
     */
    private static final String DEFAULT_IPV4_WHITELIST = "0.0.0.0/0";

    /**
     * 默认值 ::/0，表示允许所有IPv6地址访问。
     */
    private static final String DEFAULT_IPV6_WHITELIST = "::/0";

    public boolean isClientIpWhitelistEnabled() {
        return clientIpWhitelistEnabled;
    }

    public WebConfig setClientIpWhitelistEnabled(boolean clientIpWhitelistEnabled) {
        this.clientIpWhitelistEnabled = clientIpWhitelistEnabled;
        return this;
    }

    public String getClientIpv4whitelist() {
        if (!clientIpWhitelistEnabled) {
            return DEFAULT_IPV4_WHITELIST;
        }
        return clientIpv4whitelist;
    }

    public WebConfig setClientIpv4whitelist(String clientIpv4whitelist) {
        this.clientIpv4whitelist = clientIpv4whitelist;
        return this;
    }

    public String getClientIpv6whitelist() {
        if (!clientIpWhitelistEnabled) {
            return DEFAULT_IPV6_WHITELIST;
        }
        return clientIpv6whitelist;
    }

    public WebConfig setClientIpv6whitelist(String clientIpv6whitelist) {
        this.clientIpv6whitelist = clientIpv6whitelist;
        return this;
    }

    public Long getMaxAllowedTimeDriftMs() {
        return maxAllowedTimeDriftMs;
    }

    public WebConfig setMaxAllowedTimeDriftMs(Long maxAllowedTimeDriftMs) {
        this.maxAllowedTimeDriftMs = maxAllowedTimeDriftMs;
        return this;
    }

    public String getPkiMiddlewareUrls() {
        return pkiMiddlewareUrls;
    }

    public WebConfig setPkiMiddlewareUrls(String pkiMiddlewareUrls) {
        this.pkiMiddlewareUrls = pkiMiddlewareUrls;
        return this;
    }

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_WEB_PREFIX;
    }
}