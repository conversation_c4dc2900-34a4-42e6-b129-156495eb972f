package kl.npki.base.core.configs;

import kl.nbase.emengine.conf.*;
import kl.nbase.emengine.conf.file.FileEngineConfig;
import kl.nbase.emengine.conf.fusion.impl.CommonFusionConfigImpl;
import kl.nbase.emengine.conf.fusion.impl.EmulatorFusionConfigImpl;
import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.npki.base.core.constant.EmTypeEnum;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.utils.EngineUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;
import static kl.npki.base.core.constant.I18nExceptionInfoConstant.THE_KEY_DATA_DIRECTORY_DOES_NOT_EXIST_I18N_KEY;

/**
 * 简化的 {@link kl.nbase.emengine.conf.EngineConfig} 配置类
 *
 * <AUTHOR>
 * @since 2024/1/11
 */
public class EmConfig implements Serializable {

    private static final long serialVersionUID = -8849126514966345539L;
    /**
     * 加密机名称
     */
    private String engineName;

    /**
     * 加密机类型{@link EmTypeEnum}
     */
    private String engineType;
    /**
     * 加密机IP地址
     */
    private String ip;
    /**
     * 加密机端口
     */
    private Integer port;
    /**
     * 加密机用户名
     */
    private String username;
    /**
     * 加密机密码凭证
     */
    private String engineCred;
    /**
     * 文件加密机数据目录路径
     */
    private String path;

    /**
     * 签名密钥索引
     * key为签名密钥算法（如：SM2,RSA_2048）
     * value为索引信息列表,用","分隔每个索引信息，用"-"分割索引和私钥授权访问码,不配私钥授权访问码将默认使用defaultIndexCred
     * 例如：1-12346578,2-12346578,3,4,5
     */
    private Map<String, String> signIndexes;

    /**
     * 加密密钥索引
     * key为加密密钥算法（如：SM2,RSA_2048）
     * value为索引信息列表,用","分隔每个索引信息，用"-"分割索引和私钥授权访问码,不配私钥授权访问码将默认使用defaultIndexCred
     * 例如：1-12346578,2-12346578,3,4,5
     */
    private Map<String, String> encIndexes;

    /**
     * 默认私钥授权访问码
     */
    private String defaultIndexCred;

    private EngineExtConfig extConfig;

    /**
     * SDF配置文件所在路径，不同厂商的文件各不相同，如格尔sdf.cnf、三未swsds.ini等。该配置项非必填，也不向前端页面展示。
     * 当遇见需要修改SDF配置文件内容而又无法通过前、后端来修改的情况，此时可以直接对SDF配置文件进行修改，然后此处指定其路径。
     * （做这个配置项主要是为了应对现场的意外，如果是在公司内部开发期间遇见需要直接修改SDF配置的情况，可联系KJCC侧提需求寻求修正）
     */
    private String sdfConfigPath;

    public String getEngineName() {
        return engineName;
    }

    public void setEngineName(String engineName) {
        this.engineName = engineName;
    }

    public String getEngineType() {
        return engineType;
    }

    public void setEngineType(String engineType) {
        this.engineType = engineType;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEngineCred() {
        return engineCred;
    }

    public void setEngineCred(String engineCred) {
        this.engineCred = engineCred;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Map<String, String> getSignIndexes() {
        return signIndexes;
    }

    public void setSignIndexes(Map<String, String> signIndexes) {
        this.signIndexes = signIndexes;
    }

    public Map<String, String> getEncIndexes() {
        return encIndexes;
    }

    public void setEncIndexes(Map<String, String> encIndexes) {
        this.encIndexes = encIndexes;
    }

    public String getDefaultIndexCred() {
        return defaultIndexCred;
    }

    public void setDefaultIndexCred(String defaultIndexCred) {
        this.defaultIndexCred = defaultIndexCred;
    }

    public EngineExtConfig getExtConfig() {
        return extConfig;
    }

    public void setExtConfig(EngineExtConfig extConfig) {
        this.extConfig = extConfig;
    }

    public String getSdfConfigPath() {
        return sdfConfigPath;
    }

    public void setSdfConfigPath(String sdfConfigPath) {
        this.sdfConfigPath = sdfConfigPath;
    }

    public EngineConfig toEngineConfig() {
        EngineConfig engineConfig = new EngineConfig();
        engineConfig.setEngineName(this.engineName);
        EmTypeEnum emType = EmTypeEnum.getEmTypeEnumByEngineType(this.engineType);
        engineConfig.setEngineType(emType.getEngineTypeEnum());

        // 修改密码机中的keyStore路径配置为绝对路径，解决war包部署无法加载根目录下的keyStore文件
        // 注意：此处不修改配置文件中的配置是因为未来要支持集群部署的时候，如果将配置文件中的路径修改为绝对路径会导致新复制的节点找不到文件启动失败
        String engineAbsolutePath = getEmengineAbsolutePath();
        if (emType.getFusionDeviceTypeEnum() != null) {
            engineConfig.setAdapterEngineConfig(buildAdapterEngineConfig(emType));
        } else if (EmTypeEnum.FILE_ENGINE == emType) {
            engineConfig.setAdapterEngineConfig(FileEngineConfig.builder()
                .cred("koaljkscred".getBytes())
                .encryptAlgo(BlockSymAlgo.SM4_ECB_PADDING5)
                .keyStorePath(engineAbsolutePath)
                .build());
        }
        List<KeyIndex> indexes = new ArrayList<>();
        setIndexes(indexes);
        engineConfig.setAsymKeyList(indexes);
        engineConfig.setExtConfig(this.extConfig);
        engineConfig.setPath(engineAbsolutePath);
        return engineConfig;
    }

    private String getEmengineAbsolutePath() {
        if (StringUtils.isBlank(path)) {
            // 如果路径为空，则设置为默认的keystore路径为绝对路径
            return WEB_ROOT + File.separator + "keystore";
        } else if (!new File(path).isAbsolute()) {
            return WEB_ROOT + File.separator  + path;
        }
        return path;
    }

    private void setIndexes(List<KeyIndex> indexes) {
        if (this.signIndexes != null) {
            this.signIndexes.forEach((asymAlgo, indexWithCreds) -> {
                List<IndexPair> keyIndexList = parseToKeyIndexes(indexWithCreds);
                if (keyIndexList == null) return;
                KeyIndex keyIndex = new KeyIndex();
                keyIndex.setSignKeyIndex(keyIndexList);
                keyIndex.setKeyAlgo(new AsymAlgo(asymAlgo));
                indexes.add(keyIndex);
            });
        }
        if (this.encIndexes != null) {
            this.encIndexes.forEach((asymAlgo, indexWithCreds) -> {
                List<IndexPair> keyIndexList = parseToKeyIndexes(indexWithCreds);
                if (keyIndexList == null) return;
                KeyIndex keyIndex = new KeyIndex();
                keyIndex.setEncKeyIndex(keyIndexList);
                keyIndex.setKeyAlgo(new AsymAlgo(asymAlgo));
                indexes.add(keyIndex);
            });
        }
    }

    public List<IndexPair> parseToKeyIndexes(String indexWithCreds) {
        List<IndexPair> keyIndexList = new ArrayList<>();
        if (StringUtils.isBlank(indexWithCreds)) {
            return Collections.emptyList();
        }
        String[] indexWithCredsArray = indexWithCreds.split(",");
        for (String indexWithCred : indexWithCredsArray) {
            Pair<Integer, String> indexCredPair = parseIndexWithCred(indexWithCred);
            String cred = indexCredPair.getValue();
            if (StringUtils.isBlank(cred)) {
                cred = this.defaultIndexCred;
            }
            keyIndexList.add(new IndexPair(indexCredPair.getKey(), cred));
        }
        return keyIndexList;
    }

    private static Pair<Integer, String> parseIndexWithCred(String indexWithCred) {
        // indexWithCred: index-私钥授权访问码，例如：1-12346578
        // 只分割一次，防止私钥授权访问码中包含“-”字符
        String[] indexWithCredArray = indexWithCred.split("-", 2);
        Integer index = Integer.parseInt(indexWithCredArray[0]);
        String cred = indexWithCredArray.length > 1 ? indexWithCredArray[1] : null;
        return Pair.of(index, cred);
    }

    public IAdapterEngineConfig buildAdapterEngineConfig(EmTypeEnum emTypeEnum) {
        if (EmTypeEnum.EMULATOR_ENGINE == emTypeEnum) {
            String dirName = String.format(EngineUtil.EMULATOR_ENGINE_DB_DIR_FORMAT, this.engineName);
            File file = new File(dirName);
            String absolutePath = file.getAbsolutePath();
            String engineAlias = EmTypeEnum.EMULATOR_ENGINE.getEngineAlias();
            CheckUtils.isTrue(file.exists(), BaseInternalError.ENGINE_INIT_ERROR.toException(
                THE_KEY_DATA_DIRECTORY_DOES_NOT_EXIST_I18N_KEY, engineAlias, this.engineName, absolutePath));
            return new EmulatorFusionConfigImpl().setDbPath(absolutePath);
        }
        CommonFusionConfigImpl commonFusionConfig = new CommonFusionConfigImpl(emTypeEnum.getFusionDeviceTypeEnum());
        commonFusionConfig.setConfigPath(StringUtils.isNotBlank(sdfConfigPath) ? sdfConfigPath : null);
        commonFusionConfig.setServerInfoList(Collections.singletonList(new CommonFusionConfigImpl.ServerInfo(ip, port, username, engineCred)));
        return commonFusionConfig;
    }

}
