package kl.npki.base.core.configs;

import com.fasterxml.jackson.annotation.JsonIgnore;
import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.biz.cert.service.ManageCertMgr;
import kl.npki.base.core.biz.crl.parser.constant.CrlConstant;
import kl.npki.base.core.constant.BaseConstant;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;

/**
 * 系统证书公共配置项
 * <p>
 * 包含公共的身份证书、站点证书、身份证书链、站点证书链以及对应的证书请求
 * </p>
 *
 * <AUTHOR>
 * @date 2022/12/20
 */
public class SysCertConfig extends SwitchableRefreshableConfigAdaptor {

    private static final long serialVersionUID = 3317038329509704523L;

    /**
     * 本级身份证书
     */
    private String idCert;

    /**
     * 本级证书请求
     */
    private String idCertReq;

    /**
     * 本级站点证书
     */
    private String sslCert;

    /**
     * 站点证书请求
     */
    private String sslCertReq;

    /**
     * 站点加密证书
     */
    private String sslEncCert;

    /**
     * AIA颁发者分发点地址，示例为：http://127.0.0.1:8080
     */
    private String mgrRootDownloadAddress;

    /**
     * 根证书和CRL下载路径，示例为：download
     */
    private String mgrRootDownloadPath;

    /**
     * 管理根BASE64编码的证书值
     */
    private String mgrRootCert;

    /**
     * 管理根BASE64编码的CRL值
     */
    private String mgrRootCrl;

    /**
     * 根证书CRL编号，自动生成
     */
    private Long mgrRootCrlNumber;

    /**
     * 是否使用PEM格式下载证书和CRL文件
     */
    private boolean enablePemDownloadFormat;

    /**
     * 标识RefreshableConfig对象和哪个配置文件所绑定
     */
    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    /**
     * RefreshableConfig对象字段与配置文件之间的统一前缀
     */
    @Override
    public String prefix() {
        return "kl.base.cert";
    }


    public String getIdCert() {
        return idCert;
    }

    public void setIdCert(String idCert) {
        this.idCert = idCert;
    }

    public String getSslCert() {
        return sslCert;
    }

    public void setSslCert(String sslCert) {
        this.sslCert = sslCert;
    }

    public String getIdCertReq() {
        return idCertReq;
    }

    public void setIdCertReq(String idCertReq) {
        this.idCertReq = idCertReq;
    }

    public String getSslCertReq() {
        return sslCertReq;
    }

    public void setSslCertReq(String sslCertReq) {
        this.sslCertReq = sslCertReq;
    }

    public String getSslEncCert() {
        return sslEncCert;
    }

    public void setSslEncCert(String sslEncCert) {
        this.sslEncCert = sslEncCert;
    }

    public String getMgrRootDownloadAddress() {
        return mgrRootDownloadAddress;
    }

    public void setMgrRootDownloadAddress(String mgrRootDownloadAddress) {
        this.mgrRootDownloadAddress = mgrRootDownloadAddress;
    }

    public String getMgrRootDownloadPath() {
        return mgrRootDownloadPath;
    }

    public void setMgrRootDownloadPath(String mgrRootDownloadPath) {
        this.mgrRootDownloadPath = mgrRootDownloadPath;
    }

    public String getMgrRootCert() {
        return mgrRootCert;
    }

    public void setMgrRootCert(String mgrRootCert) {
        this.mgrRootCert = mgrRootCert;
    }

    public String getMgrRootCrl() {
        return mgrRootCrl;
    }

    public void setMgrRootCrl(String mgrRootCrl) {
        this.mgrRootCrl = mgrRootCrl;
    }

    public Long getMgrRootCrlNumber() {
        return mgrRootCrlNumber;
    }

    public void setMgrRootCrlNumber(Long mgrRootCrlNumber) {
        this.mgrRootCrlNumber = mgrRootCrlNumber;
    }

    public boolean isEnablePemDownloadFormat() {
        return enablePemDownloadFormat;
    }

    public void setEnablePemDownloadFormat(boolean enablePemDownloadFormat) {
        this.enablePemDownloadFormat = enablePemDownloadFormat;
    }

    @JsonIgnore
    public String getMgrRootCertDownloadUrl() {
        if (StringUtils.isBlank(mgrRootDownloadAddress) || StringUtils.isBlank(mgrRootDownloadPath)) {
            return null;
        }
        return mgrRootDownloadAddress + "/" + mgrRootDownloadPath + "/" + ManageCertMgr.MGR_ROOT_CERT_NAME;
    }

    @JsonIgnore
    public String getMgrRootCrlDownloadUrl() {
        if (StringUtils.isBlank(mgrRootDownloadAddress) || StringUtils.isBlank(mgrRootDownloadPath)) {
            return null;
        }
        return mgrRootDownloadAddress + "/" + mgrRootDownloadPath + "/" + ManageCertMgr.MGR_ROOT_CRL_NAME;
    }

    @JsonIgnore
    public BigInteger getCrlNumberBigInteger() {
        // 不存在给0，使用时会加1进行处理
        return mgrRootCrlNumber == null ? BigInteger.ZERO : BigInteger.valueOf(mgrRootCrlNumber);
    }

    @JsonIgnore
    public byte[] getMgrRootCertBytes() {
        if (StringUtils.isBlank(mgrRootCert)) {
            return new byte[0];
        }

        if (enablePemDownloadFormat) {
            return PEMUtil.formatCert(mgrRootCert).getBytes();
        } else {
            return Base64.decode(mgrRootCert);
        }
    }

    @JsonIgnore
    public byte[] getMgrRootCrlBytes() {
        if (StringUtils.isBlank(mgrRootCrl)) {
            return new byte[0];
        }

        if (enablePemDownloadFormat) {
            return PEMUtil.format(mgrRootCrl, CrlConstant.PEM_CRL_BEGIN, CrlConstant.PEM_CRL_END).getBytes();
        } else {
            return Base64.decode(mgrRootCrl);
        }
    }
}
