package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * <AUTHOR>
 */
public class MultipartConfig extends RefreshableConfigAdaptor {

    /**
     * 单个文件大小,单位 MB
     */
    private Long maxFileSize = 5L;

    /**
     * 总上传数据大小, 单位 MB
     */
    private Long maxRequestSize = 10L;

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_MULTIPART_PREFIX;
    }

    public Long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(Long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public Long getMaxRequestSize() {
        return maxRequestSize;
    }

    public void setMaxRequestSize(Long maxRequestSize) {
        this.maxRequestSize = maxRequestSize;
    }
}
