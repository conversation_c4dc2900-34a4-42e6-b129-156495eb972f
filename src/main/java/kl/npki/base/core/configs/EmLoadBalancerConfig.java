package kl.npki.base.core.configs;

import kl.nbase.emengine.conf.strategy.loadbalancer.ILoadBalancerConfig;
import kl.nbase.emengine.conf.strategy.loadbalancer.RoundRobinLoadBalancerConfig;
import kl.nbase.emengine.conf.strategy.loadbalancer.WeightedLoadBalancerConfig;
import kl.npki.base.core.constant.EngineLoadBalancerEnum;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.*;
/**
 * 定义构造密码设备负载策略所需的配置项，并根据配置构造对应的ILoadBalancerConfig实现对象
 *
 * <AUTHOR>
 * @Date 2024/10/16
 * @see ILoadBalancerConfig
 */
public class EmLoadBalancerConfig implements Serializable {

    /**
     * 负载类型
     */
    private String type;

    /**
     * 当负载类型为权重时该配置有效，key是engineName，value是对应的权重值
     */
    private Map<String, Integer> weight;

    public ILoadBalancerConfig toLoadBalancerConfig(Set<String> allEngineName) {
        if (StringUtils.isBlank(this.type) || EngineLoadBalancerEnum.ROUND_ROBIN.name().equalsIgnoreCase(this.type)) {
            // 默认使用轮询策略
            return new RoundRobinLoadBalancerConfig();
        } else if (EngineLoadBalancerEnum.WEIGHT.name().equalsIgnoreCase(this.type)) {
            // 权重策略
            return new WeightedLoadBalancerConfig(getEffectiveWeight(allEngineName));
        }
        throw BaseInternalError.ENGINE_LB_CONFIG_ERROR.toException(UNDEFINED_LOAD_TYPE_I18N_KEY, this.type);
    }

    /**
     * 获取有效的权重配置，比如weightMap中包含了不存在的engineName则说明该项是无效的
     */
    private Map<String, Integer> getEffectiveWeight(Set<String> allEngineName) {
        if (this.weight == null) {
            throw BaseInternalError.ENGINE_LB_CONFIG_ERROR.toException(WEIGHT_CONFIGURATION_IS_NULL_I18N_KEY);
        }
        Map<String, Integer> effectiveWeight = new HashMap<>();
        int weightSum = 0;
        for (String engineName : allEngineName) {
            Integer engineWeight = this.weight.get(engineName);
            if (Objects.isNull(engineWeight)) {
                throw BaseInternalError.ENGINE_LB_CONFIG_ERROR.toException(THE_DEVICE_IS_MISSING_WEIGHT_VALUES_I18N_KEY, engineName);
            }
            if (engineWeight < 0) {
                throw BaseInternalError.ENGINE_LB_CONFIG_ERROR.toException(THE_DEVICE_WEIGHT_VALUE_IS_LESS_THAN_0_I18N_KEY, engineName);
            }
            effectiveWeight.put(engineName, engineWeight);
            weightSum += engineWeight;
        }
        if (weightSum <= 0) {
            throw BaseInternalError.ENGINE_LB_CONFIG_ERROR.toException(THE_TOTAL_WEIGHT_OF_THE_DEVICE_IS_LESS_THAN_OR_EQUAL_TO0_I18N_KEY, allEngineName);
        }
        return effectiveWeight;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, Integer> getWeight() {
        return weight;
    }

    public void setWeight(Map<String, Integer> weight) {
        this.weight = weight;
    }

}
