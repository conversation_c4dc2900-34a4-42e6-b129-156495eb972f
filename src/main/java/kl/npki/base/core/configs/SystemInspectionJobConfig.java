package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.constant.BaseJobEnum;

/**
 * 系统巡检服务定时任务配置
 *
 * <AUTHOR>
 * @date 08/05/2025 11:16
 **/
public class SystemInspectionJobConfig extends RefreshableConfigAdaptor {

    /**
     * 是否启用
     */
    private boolean enabled;

    /**
     * cron表达式
     */
    private String cron;

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_BASE_PREFIX + BaseJobEnum.SYSTEM_INSPECTION.getId();
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnable(boolean enabled) {
        this.enabled = enabled;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

}
