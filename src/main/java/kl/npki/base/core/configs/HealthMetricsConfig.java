package kl.npki.base.core.configs;

import kl.nbase.config.FieldMapping;
import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

import java.io.Serializable;

/**
 * 健康监控配置
 *
 * <AUTHOR>
 */
public class HealthMetricsConfig extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = 7801135933117031939L;

    /**
     * 监控数据库连接池最大等待时间（毫秒）
     */
    private Long dbMaxWaitMillis;

    private DataSourceMetricsConfig datasource;

    @FieldMapping("measure-execution-time")
    private ExecutionTimeMetricsConfig executionTime;



    public Long getDbMaxWaitMillis() {
        return dbMaxWaitMillis;
    }

    public void setDbMaxWaitMillis(Long dbMaxWaitMillis) {
        this.dbMaxWaitMillis = dbMaxWaitMillis;
    }

    public DataSourceMetricsConfig getDatasource() {
        return datasource;
    }

    public void setDatasource(DataSourceMetricsConfig datasource) {
        this.datasource = datasource;
    }

    public ExecutionTimeMetricsConfig getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(ExecutionTimeMetricsConfig executionTime) {
        this.executionTime = executionTime;
    }

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.health.metrics";
    }


public static class DataSourceMetricsConfig implements Serializable {
    private static final long serialVersionUID = 8031044335791608926L;
    private boolean enabled;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}

    public static class ExecutionTimeMetricsConfig implements Serializable {
        private static final long serialVersionUID = 1329640111928661457L;
        private boolean enabled;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }
}
