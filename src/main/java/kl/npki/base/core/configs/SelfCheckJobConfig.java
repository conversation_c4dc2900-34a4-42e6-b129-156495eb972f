package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.constant.BaseJobEnum;

/**
 * 服务自检定时任务配置类
 *
 * @<PERSON> <PERSON>
 * @Date 2024/2/28
 */
public class SelfCheckJobConfig extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = 6414561411444250268L;
    /**
     * 是否启用
     */
    private boolean enable;

    /**
     * cron表达式
     */
    private String cron;

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_BASE_PREFIX + BaseJobEnum.SELF_CHECK.getId();
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

}
