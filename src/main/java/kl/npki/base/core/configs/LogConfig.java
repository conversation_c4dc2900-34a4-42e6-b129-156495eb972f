package kl.npki.base.core.configs;

import java.io.Serializable;

/**
 * 日志配置
 *
 * <AUTHOR>
 * @Date 2022/8/24
 */
public class LogConfig implements Serializable {

    private static final long serialVersionUID = 8765385072523821294L;
    /**
     * 日志级别
     */
    private String level;

    /**
     * 日志最大容量
     */
    private String totalSizeCap;
    /**
     * 日志保存天数
     */
    private int maxHistory;

    /**
     * syslog启用
     */
    private String syslogEnable;

    /**
     * syslog ip
     */
    private String syslogIp;
    /**
     * syslog port
     */
    private String syslogPort;
    /**
     * syslog 服务协议 UDP或者TCP
     */
    private String syslogProtocol;

    /**
     * 日志文件数量限制: 超过限制时，数据库将删除旧的日志文件
     */
    private Integer logFileLimitSize = 30;

    /**
     * excel文件行数限制: 超过限制时将生成新的excel
     */
    private Integer logFileExcelLimitSize = 100000;


    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getTotalSizeCap() {
        return totalSizeCap;
    }

    public void setTotalSizeCap(String totalSizeCap) {
        this.totalSizeCap = totalSizeCap;
    }

    public int getMaxHistory() {
        return maxHistory;
    }

    public void setMaxHistory(int maxHistory) {
        this.maxHistory = maxHistory;
    }

    public String getSyslogEnable() {
        return syslogEnable;
    }

    public void setSyslogEnable(String syslogEnable) {
        this.syslogEnable = syslogEnable;
    }

    public String getSyslogIp() {
        return syslogIp;
    }

    public void setSyslogIp(String syslogIp) {
        this.syslogIp = syslogIp;
    }

    public String getSyslogPort() {
        return syslogPort;
    }

    public void setSyslogPort(String syslogPort) {
        this.syslogPort = syslogPort;
    }

    public String getSyslogProtocol() {
        return syslogProtocol;
    }

    public void setSyslogProtocol(String syslogProtocol) {
        this.syslogProtocol = syslogProtocol;
    }

    public Integer getLogFileLimitSize() {
        return logFileLimitSize;
    }

    public void setLogFileLimitSize(Integer logFileLimitSize) {
        this.logFileLimitSize = logFileLimitSize;
    }

    public Integer getLogFileExcelLimitSize() {
        return logFileExcelLimitSize;
    }

    public void setLogFileExcelLimitSize(Integer logFileExcelLimitSize) {
        this.logFileExcelLimitSize = logFileExcelLimitSize;
    }
}
