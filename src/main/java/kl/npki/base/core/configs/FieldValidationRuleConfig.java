package kl.npki.base.core.configs;


import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

import java.util.List;

/**
 * 字段验证规则
 * <AUTHOR> by niuga<PERSON> on 2025-07-02 13:55
 */
public class FieldValidationRuleConfig  extends SwitchableRefreshableConfigAdaptor {

    private static final long serialVersionUID = 1L;

    /**
     * 字段规则
     */
    private List<FieldRuleConfig> rules;

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_FIELD_VALIDATION_PREFIX;
    }

    public List<FieldRuleConfig> getRules() {
        return rules;
    }

    public void setRules(List<FieldRuleConfig> rules) {
        this.rules = rules;
    }
}
