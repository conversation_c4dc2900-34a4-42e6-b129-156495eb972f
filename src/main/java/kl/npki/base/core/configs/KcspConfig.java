package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * KCSP 相关配置类
 *
 * <AUTHOR>
 * @date 2024/5/17
 */
public class KcspConfig extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = 4596352803900482075L;

    /**
     * 是否开启商密监管日志上报
     */
    private boolean enableAlgoUsageLog;

    /**
     * 监管日志上报周期
     */
    private long algoUsageLogPeriod;

    /**
     * 管理网关代理服务标识
     */
    private String mgrGatewayIdentifier;

    /**
     * 服务网关代理服务标识
     */
    private String svcGatewayIdentifier;

    public boolean isEnableAlgoUsageLog() {
        return enableAlgoUsageLog;
    }

    public void setEnableAlgoUsageLog(boolean enableAlgoUsageLog) {
        this.enableAlgoUsageLog = enableAlgoUsageLog;
    }

    public long getAlgoUsageLogPeriod() {
        return algoUsageLogPeriod;
    }

    public void setAlgoUsageLogPeriod(long algoUsageLogPeriod) {
        this.algoUsageLogPeriod = algoUsageLogPeriod;
    }

    public String getMgrGatewayIdentifier() {
        return mgrGatewayIdentifier;
    }

    public void setMgrGatewayIdentifier(String mgrGatewayIdentifier) {
        this.mgrGatewayIdentifier = mgrGatewayIdentifier;
    }

    public String getSvcGatewayIdentifier() {
        return svcGatewayIdentifier;
    }

    public void setSvcGatewayIdentifier(String svcGatewayIdentifier) {
        this.svcGatewayIdentifier = svcGatewayIdentifier;
    }

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.kcsp";
    }

}
