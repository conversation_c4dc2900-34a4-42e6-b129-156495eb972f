package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;


/**
 * 时间日期格式化配置类
 *
 * <AUTHOR>
 * @since 2025/2/17 10:53
 */
public class DateFormatConfig extends SwitchableRefreshableConfigAdaptor {

    private static final long serialVersionUID = 8169676727236202588L;


    /**
     * 年月日 日期格式
     */
    private String dayPattern = "yyyy-MM-dd";

    /**
     * 年月日时分秒 日期格式
     */
    private String dateTimePattern = "yyyy-MM-dd HH:mm:ss";

    /**
     * 年月日时分秒、毫秒 日期格式
     */
    private String dateTimeMsPattern = "yyyy-MM-dd HH:mm:ss.SSS";


    /**
     * 文件日期格式： 年月日时分秒 日期格式
     */
    private String  fileDateTimePattern = "yyyyMMddHHmmss";

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.dateFormat";
    }

    public String getDayPattern() {
        return dayPattern;
    }

    public void setDayPattern(String dayPattern) {
        this.dayPattern = dayPattern;
    }

    public String getDateTimePattern() {
        return dateTimePattern;
    }

    public void setDateTimePattern(String dateTimePattern) {
        this.dateTimePattern = dateTimePattern;
    }

    public String getDateTimeMsPattern() {
        return dateTimeMsPattern;
    }

    public void setDateTimeMsPattern(String dateTimeMsPattern) {
        this.dateTimeMsPattern = dateTimeMsPattern;
    }

    public String getFileDateTimePattern() {
        return fileDateTimePattern;
    }

    public void setFileDateTimePattern(String fileDateTimePattern) {
        this.fileDateTimePattern = fileDateTimePattern;
    }

}
