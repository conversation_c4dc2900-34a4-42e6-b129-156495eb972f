package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;


/**
 * <AUTHOR>
 * @date 2022/11/2 12:07
 * @desc 登录配置
 */
public class LoginConfig extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = 7206574971617266954L;
    /**
     * 接口前缀
     */
    private String apiPrefix = "/mgmt/v1";

    /**
     * 访问令牌生命周期
     */
    private long accessTokenLifecycle = 1800000L;

    /**
     * token剩余有效期小于等于该值时，刷新token
     */
    private long refreshTokenThreshold = 900000L;

    /**
     * 刷新token后，旧token的宽限期
     */
    private long gracePeriod = 30000L;

    /**
     * 访问权限控制
     */
    private boolean accessControlEnabled = false;

    private boolean captchaCheckEnabled = true;

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.login";
    }

    public String getApiPrefix() {
        return apiPrefix;
    }

    public LoginConfig setApiPrefix(String apiPrefix) {
        this.apiPrefix = apiPrefix;
        return this;
    }

    public long getRefreshTokenThreshold() {
        return refreshTokenThreshold;
    }

    public void setRefreshTokenThreshold(long refreshTokenThreshold) {
        this.refreshTokenThreshold = refreshTokenThreshold;
    }

    public long getAccessTokenLifecycle() {
        return accessTokenLifecycle;
    }

    public LoginConfig setAccessTokenLifecycle(long accessTokenLifecycle) {
        this.accessTokenLifecycle = accessTokenLifecycle;
        return this;
    }

    public boolean isAccessControlEnabled() {
        return accessControlEnabled;
    }

    public LoginConfig setAccessControlEnabled(boolean accessControlEnabled) {
        this.accessControlEnabled = accessControlEnabled;
        return this;
    }

    public boolean isCaptchaCheckEnabled() {
        return captchaCheckEnabled;
    }

    public void setCaptchaCheckEnabled(boolean captchaCheckEnabled) {
        this.captchaCheckEnabled = captchaCheckEnabled;
    }

    public long getGracePeriod() {
        return gracePeriod;
    }

    public void setGracePeriod(long gracePeriod) {
        this.gracePeriod = gracePeriod;
    }

    @Override
    public String toString() {
        return "LoginConfig{" +
            "apiPrefix='" + apiPrefix + '\'' +
            ", accessTokenLifecycle=" + accessTokenLifecycle +
            ", refreshTokenThreshold=" + refreshTokenThreshold +
            ", gracePeriod=" + gracePeriod +
            ", accessControlEnabled=" + accessControlEnabled +
            ", captchaCheckEnabled=" + captchaCheckEnabled +
            '}';
    }
}

