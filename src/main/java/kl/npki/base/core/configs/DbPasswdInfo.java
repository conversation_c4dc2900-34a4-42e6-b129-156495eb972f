package kl.npki.base.core.configs;

import kl.nbase.helper.annotions.FieldEncrypt;

import java.io.Serializable;

public class DbPasswdInfo implements Serializable {
    /**
     * 数据库名
     */
    private String dbName;

    /**
     * 数据库密码
     */
    @FieldEncrypt
    private String dbPasswd;

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getDbPasswd() {
        return dbPasswd;
    }

    public void setDbPasswd(String dbPasswd) {
        this.dbPasswd = dbPasswd;
    }
}
