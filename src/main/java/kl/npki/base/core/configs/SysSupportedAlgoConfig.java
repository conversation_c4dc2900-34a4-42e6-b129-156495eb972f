package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.constant.BaseConstant;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guoq
 * @Date: 2023/6/30
 * @description: 支持的算法配置(会包括业务层面和密码机层支持的算法)
 */
public class SysSupportedAlgoConfig extends SwitchableRefreshableConfigAdaptor {

    private static final long serialVersionUID = -8122016446229954804L;

    /**
     * 业务层面配置支持的非对称算法
     * 示例值： RSA_2048,SM2
     */
    private String asymmetric;

    /**
     * 业务层面配置支持的对称算法
     * 示例值： SM4,SM1
     */
    private String symmetric;

    /**
     * 业务层面配置支持的哈希算法
     * 示例值： SM3,SHA256
     */
    private String hash;

    /**
     * 业务层面配置支持的抗量子密码算法
     * 示例值： KYBER_512,KYBER_768,KYBER_1024
     */
    private String postQuantum;

    /**
     * 系统证书（管理根证书、站点证书、身份证书、管理员证书）算法类型，默认使用RSA
     * 目前仅支持RSA、SM2
     */
    private String systemCertAlgo;

    /**
     * 业务层过滤的算法配置支持项,会根据密码层支持的算法进行过滤匹配,所以这里配置的理论是密码层已支持的算法,这里的算法集合是密码层算法的子集
     */
    private CanSupport support = new CanSupport();

    public String getAsymmetric() {
        return asymmetric;
    }

    public void setAsymmetric(String asymmetric) {
        this.asymmetric = asymmetric;
    }

    public List<String> extractAsymmetricList() {
        return splitAlgosList(getAsymmetric());
    }

    public static List<String> splitAlgosList(String algos) {
        if (StringUtils.isBlank(algos)) {
            return Collections.emptyList();
        }
        String[] algoArray = algos.split(BaseConstant.ALGORITHMIC_SPLIT_SYMBOL);
        return Arrays.stream(algoArray).collect(Collectors.toList());
    }

    public String getSymmetric() {
        return symmetric;
    }

    public void setSymmetric(String symmetric) {
        this.symmetric = symmetric;
    }

    public List<String> extractSymmetricList() {
        return splitAlgosList(getSymmetric());
    }

    public String getPostQuantum() {
        return postQuantum;
    }

    public void setPostQuantum(String postQuantum) {
        this.postQuantum = postQuantum;
    }

    public List<String> extractPostQuantumList() {
        return splitAlgosList(getPostQuantum());
    }

    public String getSystemCertAlgo() {
        return systemCertAlgo;
    }

    public SysSupportedAlgoConfig setSystemCertAlgo(String systemCertAlgo) {
        this.systemCertAlgo = systemCertAlgo;
        return this;
    }

    public AsymAlgo getSystemCertAsymAlgo() {
        return AsymAlgo.getAsymAlgo(systemCertAlgo);
    }

    /**
     * 提取非对称算法和抗量子算法的合集
     *
     * @return 非对称算法和抗量子算法的合集列表
     */
    public List<String> extractAsymmetricAndPostQuantumList() {
        List<String> asymmetricList = extractAsymmetricList();
        List<String> postQuantumList = extractPostQuantumList();

        List<String> combinedList = new ArrayList<>(asymmetricList);
        combinedList.addAll(postQuantumList);

        return combinedList;
    }

    /**
     * 提取非对称算法和抗量子算法的合集
     *
     * @return 非对称算法和抗量子算法的合集列表
     */
    public Set<String> extractAsymmetricAndPostQuantumSet() {
        return new HashSet<>(extractAsymmetricAndPostQuantumList());
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public List<String> extractHashList() {
        return splitAlgosList(getHash());
    }

    public CanSupport getSupport() {
        return support;
    }

    public void setSupport(CanSupport support) {
        this.support = support;
    }

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_ALGO_PREFIX;
    }

    /**
     * 使用业务层配置过滤系统支持的对称算法(由于对称算法的特殊性,其对称算法名称+分组模式+填充模式存在笛卡尔积,故JCC不会列举出所有的对称算法，所以目前通过算法名称和块模式进行匹配）
     *
     * @param engineSupportAlgoList 密码层支持的对称算法
     * @return
     */
    public List<String> filterSupportedSymAlgorithms(List<String> engineSupportAlgoList) {
        return engineSupportAlgoList.stream()
            .filter(algo -> getSupport().extractSymmetricList().stream().anyMatch(algo::contains))
            .collect(Collectors.toList());
    }

    /**
     * 使用业务层配置过滤系统支持的非对称算法
     *
     * @param engineSupportAlgoList 密码层支持的非对称算法
     * @return
     */
    public List<String> filterSupportedAsymAlgorithms(List<String> engineSupportAlgoList) {
        return engineSupportAlgoList.stream()
            .filter(algo -> getSupport().extractAsymmetricList().stream().anyMatch(algo::contains))
            .collect(Collectors.toList());
    }

    /**
     * 使用业务层配置过滤系统支持的哈希算法
     *
     * @param engineSupportAlgoList 密码层支持的哈希算法
     * @return
     */
    public List<String> filterSupportedHashAlgorithms(List<String> engineSupportAlgoList) {
        return engineSupportAlgoList.stream()
            .filter(algo -> getSupport().extractHashList().stream().anyMatch(algo::contains))
            .collect(Collectors.toList());
    }

    /**
     * 使用业务层配置过滤系统支持的抗量子密码算法
     *
     * @param engineSupportAlgoList 密码层支持的抗量子密码算法
     * @return
     */
    public List<String> filterSupportedPQCAlgorithms(List<String> engineSupportAlgoList) {
        return engineSupportAlgoList.stream()
            .filter(algo -> getSupport().extractPostQuantumList().stream().anyMatch(algo::contains))
            .collect(Collectors.toList());
    }

    /**
     * 使用业务层配置过滤系统支持的系统证书算法
     *
     * @param engineSupportAlgoList 密码层支持的系统证书算法
     * @return
     */
    public List<String> filterSupportedSystemCertAlgorithms(List<String> engineSupportAlgoList) {
        return engineSupportAlgoList.stream()
            .filter(algo -> getSupport().extractSystemCertAlgoList().stream().anyMatch(algo::contains))
            .collect(Collectors.toList());
    }

    /**
     * 业务层过滤的算法配置支持项(支持模糊匹配)
     */
    public static class CanSupport implements Serializable {

        /**
         * 业务层面可以支持的非对称算法
         */
        private String asymmetric = "SM2,RSA_2048,RSA_3072,RSA_4096";

        /**
         * 业务层面可以支持的对称算法
         */
        private String symmetric = "SM1/CBC/PKCS5Padding,SM4/GCM/NoPadding";

        /**
         * 业务层面可以支持的哈希算法
         */
        private String hash = "SM3,SHA,SHA3";

        /**
         * 业务层面可以支持的抗量子密码算法
         */
        private String postQuantum = "KYBER,DILITHIUM";

        /**
         * 业务系统使用的证书算法
         */
        private String systemCertAlgo = AsymAlgo.SM2.getAlgoName() + "," + AsymAlgo.RSA_2048.getAlgoName();

        public String getAsymmetric() {
            return asymmetric;
        }

        public void setAsymmetric(String asymmetric) {
            this.asymmetric = asymmetric;
        }

        public List<String> extractAsymmetricList() {
            return splitAlgosList(getAsymmetric());
        }

        public String getSymmetric() {
            return symmetric;
        }

        public void setSymmetric(String symmetric) {
            this.symmetric = symmetric;
        }

        public List<String> extractSymmetricList() {
            return splitAlgosList(getSymmetric());
        }

        public String getPostQuantum() {
            return postQuantum;
        }

        public void setPostQuantum(String postQuantum) {
            this.postQuantum = postQuantum;
        }

        public List<String> extractPostQuantumList() {
            return splitAlgosList(getPostQuantum());
        }

        public String getHash() {
            return hash;
        }

        public void setHash(String hash) {
            this.hash = hash;
        }

        public List<String> extractHashList() {
            return splitAlgosList(getHash());
        }

        public String getSystemCertAlgo() {
            return systemCertAlgo;
        }

        public void setSystemCertAlgo(String systemCertAlgo) {
            this.systemCertAlgo = systemCertAlgo;
        }

        public List<String> extractSystemCertAlgoList() {
            return splitAlgosList(getSystemCertAlgo());
        }
    }
}
