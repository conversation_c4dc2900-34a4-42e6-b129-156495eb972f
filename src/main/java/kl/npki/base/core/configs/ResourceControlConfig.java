package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * 资源控制开关
 *
 * <AUTHOR>
 * @since 2025/4/8 10:04
 */
public class ResourceControlConfig extends SwitchableRefreshableConfigAdaptor {
    private static final long serialVersionUID = 8169676727256202588L;


    /**
     * 资源控制开关，默认关闭，打开时隐藏对应资源
     */
    private Boolean controlHideEnable;

    /**
     * 资源控制集合
     */
    private String controlList;

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.resource";
    }

    public Boolean getControlHideEnable() {
        return controlHideEnable;
    }

    public void setControlHideEnable(Boolean controlHideEnable) {
        this.controlHideEnable = controlHideEnable;
    }

    public String getControlList() {
        return controlList;
    }

    public void setControlList(String controlList) {
        this.controlList = controlList;
    }
}

