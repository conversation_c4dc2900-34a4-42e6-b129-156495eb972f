package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

import static kl.npki.base.core.constant.BaseConstant.APP_ROOT;

/**
 * 系统通用配置
 * <AUTHOR>
 * @Date 2023/10/13
 */
public class SysConfig extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = -9190586118869500959L;
    /**
     * 是否显示微软CSP设备
     */
    private boolean showCSP;

    /**
     * 是否开启完整性检查
     */
    private boolean openDataFull;

    /**
     * 是否加密请求体数据
     */
    private boolean encryptRequestBody;

    /**
     * 性能测试配置
     */
    private boolean performanceTest;

    /**
     * PKI中间件版本号
     */
    private String pkiClientVersion;

    /**
     * 临时文件夹，用于存放临时文件，如导出证书等。
     * 除非很清楚当前要配置的目录为临时目录，否则不要修改
     */
    private String tempDir = APP_ROOT + "/temp/";

    /**
     * 临时文件过期时间，默认3天，对于超出时间的文件会被清理，清理是每导出<code>exportCountToTriggerCleanup</code>次才会触发清理一次.
     */
    private long tempFileExpirationTime = 3L * 24 * 60 * 60 * 1000;

    /**
     * 默认每导出100次文件就检查一次是否需要删除历史文件
     */
    private long exportCountToTriggerCleanup = 100;

    /**
     * 系统间通讯认证口令
     */
    private String systemAuthToken;

    public boolean isShowCSP() {
        return showCSP;
    }

    public void setShowCSP(boolean showCSP) {
        this.showCSP = showCSP;
    }

    public boolean isOpenDataFull() {
        return openDataFull;
    }

    public SysConfig setOpenDataFull(boolean openDataFull) {
        this.openDataFull = openDataFull;
        return this;
    }

    public boolean isEncryptRequestBody() {
        return encryptRequestBody;
    }

    public void setEncryptRequestBody(boolean encryptRequestBody) {
        this.encryptRequestBody = encryptRequestBody;
    }

    public boolean isPerformanceTest() {
        return performanceTest;
    }

    public void setPerformanceTest(boolean performanceTest) {
        this.performanceTest = performanceTest;
    }

    public String getPkiClientVersion() {
        return pkiClientVersion;
    }

    public void setPkiClientVersion(String pkiClientVersion) {
        this.pkiClientVersion = pkiClientVersion;
    }

    public String getTempDir() {
        return tempDir;
    }

    public SysConfig setTempDir(String tempDir) {
        this.tempDir = tempDir;
        return this;
    }

    public String getSystemAuthToken() {
        return systemAuthToken;
    }

    public SysConfig setSystemAuthToken(String systemAuthToken) {
        this.systemAuthToken = systemAuthToken;
        return this;
    }

    public long getTempFileExpirationTime() {
        return tempFileExpirationTime;
    }

    public SysConfig setTempFileExpirationTime(long tempFileExpirationTime) {
        this.tempFileExpirationTime = tempFileExpirationTime;
        return this;
    }

    public long getExportCountToTriggerCleanup() {
        return exportCountToTriggerCleanup;
    }

    public SysConfig setExportCountToTriggerCleanup(long exportCountToTriggerCleanup) {
        this.exportCountToTriggerCleanup = exportCountToTriggerCleanup;
        return this;
    }

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_SYSINFO_PREFIX;
    }
}
