package kl.npki.base.core.repository;

import kl.npki.base.core.biz.log.model.SystemLogFileEntity;

import java.util.List;

/**
 * 系统日志eRepository
 *
 * <AUTHOR>
 * @since 2025/7/8 15:01
 */
public interface ISystemLogFileRepository extends BaseRepository {

    /**
     * 将上传文件导入到数据仓库中
     *
     * @param systemLogFileEntity 上传文件
     * @return 上传文件在数据仓库中的唯一标识
     */
    Long importFile(SystemLogFileEntity systemLogFileEntity);

    /**
     * 将上传文件导入到数据仓库中
     *
     * @param systemLogFileEntity 上传文件
     * @return 上传文件在数据仓库中的唯一标识
     */
    boolean update(SystemLogFileEntity systemLogFileEntity);

    /**
     * 删除文件id
     *
     * @param id
     */
    void delete(Long id);


    /**
     * 超出数量后，删除日志文件
     *
     * @param fileType   文件类型
     * @param limitCount 数量限制
     */
    void deleteOverLimitRecord(Integer fileType, Integer limitCount);


    /**
     * 根据标识查询上传文件实体
     *
     * @param id
     * @return SystemLogFileEntity
     */
    SystemLogFileEntity search(Long id);

    /**
     * 根据标识查询上传文件实体状态
     *
     * @param id id
     * @return SystemLogFileEntity
     */
    SystemLogFileEntity searchStatus(Long id);

    /**
     * 根据标识查询上传文件实体
     *
     * @param fileType 文件类型
     * @param processStatus 文件状态
     * @return List<SystemLogFileEntity>
     */
    List<SystemLogFileEntity> searchSystemLogFileList(Integer fileType, Integer processStatus);

    /**
     * 更新上传文件处理状态
     * @param fileType  文件类型
     * @param processStatus 处理状态
     */
    void updateProcessStatus(Integer fileType, Integer processStatus);

}
