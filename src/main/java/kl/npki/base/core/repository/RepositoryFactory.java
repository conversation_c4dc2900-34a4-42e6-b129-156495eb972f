package kl.npki.base.core.repository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Repository工厂类，核心包里的Repository只定义接口，需要调用方实现各自的实现类
 * 使用Reflections包里的反射工具查找项目下Repository的实现类，并将其实例化后注入RepositoryFactory中
 *
 * Created by CaoLei on 2022/02/11 10:31
 */
public class RepositoryFactory {

    private static final Map<Class<?>, Object> map = new HashMap<>();
    private static final Logger log = LoggerFactory.getLogger(RepositoryFactory.class);

    private RepositoryFactory() {

    }

    public static void init(RepositoryCollector collector) {
        collector.collect().forEach((k, v) -> {
            try {
                map.put(k, v);
                log.debug("RepositoryFactory initialization {} successful", k.getSimpleName());
            } catch (Exception e) {
                log.error("RepositoryFactory initialization {} failed", k.getSimpleName());
                log.error(e.getMessage(), e);
            }
        });
        if (log.isInfoEnabled()) {
            Set<Class<?>> keySet = map.keySet();
            log.info("RepositoryFactory initialization completed, initializing {} Repository instances in total", keySet.size());
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T get(Class<T> clazz) {
        return (T) map.get(clazz);
    }
}
