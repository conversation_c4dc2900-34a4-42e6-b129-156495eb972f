package kl.npki.base.core.repository;

import kl.npki.base.core.biz.log.model.ApiLogCountServiceResult;
import kl.npki.base.core.biz.log.model.ApiLogInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/12/14
 */
public interface IApiLogRepository extends ILogBaseRepository<ApiLogInfo> {

    /**
     * 存储服务日志
     *
     * @param apiLogInfo
     * @return
     */
    void saveApiLog(ApiLogInfo apiLogInfo);

    /**
     * 以请求者名称，请求结果，业务名称分组进行统计
     * @param callerNames  请求者名称集合
     * @param startTime 业务统计起始时间
     * @param endTime 业务统计截至时间
     * @return
     */
    List<ApiLogCountServiceResult> countGroupByCallerAndResultAndBiz(List<String> callerNames, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 以请求结果，业务名称分组进行统计
     * @param bizNames 业务名称集合
     * @param startTime 业务统计起始时间
     * @param endTime 业务统计截至时间
     * @return
     */
    List<ApiLogCountServiceResult> countGroupByResultAndBiz(List<String> bizNames, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据业务名称获取到第一条日志记录
     *
     * @param bizName 业务名称
     * @return
     */
    Optional<ApiLogInfo> getFirstEntityByBizName(String bizName);
}
