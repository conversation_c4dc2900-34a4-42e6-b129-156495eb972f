package kl.npki.base.core.repository;

import kl.nbase.db.support.mybatis.query.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/2/13 上午10:22
 */
public interface ILogBaseRepository<T> extends BaseRepository {

    /**
     * 查询日志记录
     *
     * @param pageInfo        分页条件
     * @param searchCondition 查询条件
     * @return 查询数据集合
     */
    List<T> page(PageInfo pageInfo, Object searchCondition);
}