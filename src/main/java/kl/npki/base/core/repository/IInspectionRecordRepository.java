package kl.npki.base.core.repository;

import kl.npki.base.core.biz.inspection.model.InspectionRecordEntity;

import java.util.Optional;

/**
 * 巡检记录仓库接口
 *
 * <AUTHOR>
 * @date 07/05/2025 16:36
 *
 **/
public interface IInspectionRecordRepository extends BaseRepository {

    /**
     * 增加巡检记录
     *
     * @param inspectionRecordEntity 巡检记录
     * @return 新增的巡检记录标识
     */
    Long addRecord(InspectionRecordEntity inspectionRecordEntity);

    /**
     * 根据ID获取巡检记录
     *
     * @param id 巡检记录标识
     * @return 巡检记录实体
     */
    Optional<InspectionRecordEntity> getRecordById(Long id);
}
