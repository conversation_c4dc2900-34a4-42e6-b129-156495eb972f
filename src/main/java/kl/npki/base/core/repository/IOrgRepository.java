package kl.npki.base.core.repository;

import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.base.core.biz.org.model.OrgEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构数据仓库接口定义
 *
 * <AUTHOR>
 * @create 2024/3/13 10:59
 */
public interface IOrgRepository extends BaseRepository {

    /**
     * 根据机构ID查询机构实体
     *
     * @param id 机构id
     * @return 机构实体对象
     */
    OrgEntity searchById(Long id);

    /**
     * 根据机构名称查询机构实体
     *
     * @param orgName 机构名称
     * @return 机构实体对象
     */
    OrgEntity searchByOrgName(String orgName);

    /**
     * 根据机构编码查询机构实体
     *
     * @param orgCode 机构编码
     * @return 机构实体对象
     */
    OrgEntity searchByOrgCode(String orgCode);

    /**
     * 获取所有的子、孙等机构
     *
     * @param fullId   完整机构id
     * @param pageInfo 分页条件
     * @return
     */
    List<OrgEntity> searchAllSubOrgList(String fullId, PageInfo pageInfo);

    /**
     * 根据机构名称查询当前机构的所有子机构实体信息
     *
     * @param parentId 父机构id
     * @param pageInfo 分页信息
     * @return 机构实体对象
     */
    List<OrgEntity> searchSubOrgEntityById(Long parentId, PageInfo pageInfo);

    /**
     * 根据机构名称查询当前机构的所有子机构实体信息
     *
     * @param parentOrgName 父机构名称
     * @param pageInfo      分页信息
     * @return 机构实体对象
     */
    List<OrgEntity> searchSubOrgEntityByOrgName(String parentOrgName, PageInfo pageInfo);

    /**
     * 根据机构名称查询当前机构的所有子机构实体信息
     *
     * @param parentOrgCode 父机构编码
     * @param pageInfo      分页信息
     * @return 机构实体对象
     */
    List<OrgEntity> searchSubOrgEntityByOrgCode(String parentOrgCode, PageInfo pageInfo);

    /**
     * 根据更新时间查询机构实体集合
     *
     * @param updatedAt 更新时间
     * @param pageInfo  分页条件
     * @return 满足时间和分页条件的机构集合
     */
    List<OrgEntity> searchSubOrgListByUpdateAt(LocalDateTime updatedAt, PageInfo pageInfo);

    /**
     * 获取所有机构信息
     *
     * @param pageInfo 分页信息
     * @return 机构实体对象
     */
    List<OrgEntity> searchAll(PageInfo pageInfo);

    /**
     * 获取所有机构信息
     *
     * @return 机构实体对象
     */
    List<OrgEntity> searchAll();

    /**
     * 添加机构实体
     *
     * @param orgEntity 机构实体信息
     */
    Long save(OrgEntity orgEntity);

    /**
     * 添加或更新机构实体，内部需要做一次查询，如果存在则更新，不存在则添加，查询可以考虑使用缓存实现
     *
     * @param orgEntity 机构实体集合
     */
    boolean saveOrUpdate(OrgEntity orgEntity);

    /**
     * 更新机构实体
     *
     * @param orgEntity 机构实体信息
     */
    boolean update(OrgEntity orgEntity);

    /**
     * 注销机构实体
     *
     * @param id 机构id
     */
    boolean revoke(Long id);

    /**
     * 逻辑删除机构实体
     *
     * @param id 机构id
     */
    boolean delete(Long id);

    /**
     * 批量逻辑删除机构实体
     *
     * @param ids
     * @return
     */
    boolean batchDelete(List<Long> ids);
}
