package kl.npki.base.core.repository;

import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.constant.TrustCertLevel;
import kl.npki.base.core.constant.TrustCertType;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
public interface ITrustCertRepository extends BaseRepository {
    /**
     * 获取所有根证书
     *
     * @return
     */
    Collection<TrustCertEntity> getAllSuperCert();

    /**
     * 获取所有根证书
     *
     * @return
     */
    Collection<TrustCertEntity> getAllSuperCert(TrustCertType certType);

    /**
     * 批量保存信任证书
     *
     * @param trustCertList
     */
    void batchSaveTrustCert(Collection<TrustCertEntity> trustCertList);

    /**
     * 获取管理根证书
     * @return
     */
    TrustCertEntity getManageCert();

    /**
     * 获取管理根证书
     * @return
     */
    TrustCertEntity getCert(TrustCertType certType);

    /**
     * 保存信任证书
     * @param trustCertEntity
     * @return
     */
    Long save(TrustCertEntity trustCertEntity);

    /**
     * 逻辑删除上级信任链证书，系统管理根不支持删除，只能更新
     * @param id
     * @return
     */
    boolean deleteTrustCert(Long id);

    /**
     * 逻辑删除信任链证书
     * @param id
     * @return
     */
    boolean delete(Long id);

    /**
     * 根据ID查询信任证书
     * @param id
     * @return
     */
    TrustCertEntity getCert(Long id);

    /**
     * 根据hexSn查询信任证书
     * @param hexSn
     * @return
     */
    TrustCertEntity getCert(String hexSn);

    /**
     * 获取所有的TrustCert
     * @return
     */
    List<TrustCertEntity> getAll();

    /**
     * 根据证书类型获取所有的TrustCert
     * @return
     */
    List<TrustCertEntity> getAll(TrustCertType certType);

    /**
     * 根据证书级别获取证书
     * @param certLevel
     * @return
     */
    List<TrustCertEntity> queryCertByLevel(TrustCertLevel certLevel);

    /**
     * 根据证书级别获取证书
     * @param certLevel
     * @param certType
     * @return
     */
    List<TrustCertEntity> queryCertByLevelAndType(TrustCertLevel certLevel, TrustCertType certType);


    /**
     * 校验证书是否存在
     * @param hexSn
     * @return
     */
    boolean checkCertExist(String hexSn);

    /**
     * 校验证书是否存在
     * @param hexSn
     * @return
     */
    boolean checkCertExist(String hexSn, String issuerCn);


    /**
     * 保存管理证书请求
     * @param dn dn
     * @param b64CertReq 管理根证书的证书请求值
     * @param registerInfo 注册信息
     */
    void saveOrUpdateManageCertReq(String dn, String b64CertReq, String registerInfo);

    /**
     * 更新
     * @param trustCertEntity
     */
    void updateById(TrustCertEntity trustCertEntity);
}
