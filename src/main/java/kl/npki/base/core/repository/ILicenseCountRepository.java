package kl.npki.base.core.repository;

import kl.npki.base.core.biz.license.model.LicenseCountEntity;

import java.util.List;

/**
 * license授权数据存储
 *
 * <AUTHOR> by niugang on 2025-05-29 16:58
 */
public interface ILicenseCountRepository extends BaseRepository {


    /**
     * 根据License业务标识业务标识查询
     *
     * @param cusKey License业务标识
     * @return LicenseCountEntity
     */
    LicenseCountEntity searchByCusKey(String cusKey);


    /**
     * 根据License业务标识业务标识批量查询
     *
     * @param cusKeys License业务标识
     * @return LicenseCountEntity
     */
    List<LicenseCountEntity> batchSearchByCusKey(List<String> cusKeys);

    /**
     * 批量保存或更新
     * isCheckMaxLimit=true时，用于增加某项授权数据，需要二次校验，防止超过最大授权数据
     * isCheckMaxLimit=false时，用于服务启动，重置授权数据
     *
     * @param licenseCountEntities licenseCountEntities
     * @param isCheckMaxLimit      是否二次校验最大授权数据，true需要校验，false不需要校验
     */
    void batchSaveOrUpdate(List<LicenseCountEntity> licenseCountEntities, boolean isCheckMaxLimit);


}
