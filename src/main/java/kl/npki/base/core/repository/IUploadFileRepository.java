package kl.npki.base.core.repository;

import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.core.constant.UploadFileProcessStatusEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午3:36
 */
public interface IUploadFileRepository extends BaseRepository {

    /**
     * 将上传文件导入到数据仓库中
     *
     * @param uploadFileEntity 上传文件
     * @return 上传文件在数据仓库中的唯一标识
     */
    Long importFile(UploadFileEntity uploadFileEntity);

    /**
     * 删除数据仓库中的文件
     *
     * @param id 唯一标识
     */
    boolean delete(Long id);

    /**
     * 将上传文件导入到数据仓库中
     *
     * @param uploadFileEntity 上传文件
     * @return 上传文件在数据仓库中的唯一标识
     */
    boolean update(UploadFileEntity uploadFileEntity);

    /**
     * 根据标识查询上传文件实体
     *
     * @param id
     * @return
     */
    UploadFileEntity search(Long id);

    /**
     * 根据标识查询上传文件实体
     *
     * @param interfaceName 接口名称
     * @param pageInfo 分页条件
     * @return
     */
    List<UploadFileEntity> search(String interfaceName, PageInfo pageInfo);

    /**
     * 根据上传文件用途（接口名称）和处理状态查询实体
     *
     * @param interfaceName 上传文件用途，接口名称
     * @param processStatus 处理状态
     * @return
     */
    UploadFileEntity searchByInterfaceNameProcessComplete(String interfaceName,
                                                          UploadFileProcessStatusEnum... processStatus);
}