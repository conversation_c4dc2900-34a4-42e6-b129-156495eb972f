package kl.npki.base.core.tenantholders.common;

import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.tenantholders.ITenantEscrow;
import kl.npki.base.core.tenantholders.TenantContextHolder;

/**
 * <AUTHOR>
 * @Date 2024/1/30 17:56
 * @Description: 通用多租户Holder，适配多租户环境下缓存场景<br>
 *   所以{@link CommonTenantHolder}需要在{@link ConfigHolder}之后调用 #initEscrowedObj(java.lang.String)
 */
public class CommonTenantHolder implements ITenantEscrow {
    public static CommonTenantObjects get() {
        return (CommonTenantObjects) TenantContextHolder.INSTANCE.getEscrowedObj(CommonTenantHolder.class);
    }

    public static CommonTenantObjects get(String tenantId) {
        return (CommonTenantObjects) TenantContextHolder.INSTANCE.getEscrowedObj(tenantId, CommonTenantHolder.class);
    }

    /**
     * 使用默认配置（内存缓存）初始化
     * @return ICacheClient
     */
    public static CommonTenantObjects init(String tenantName) {
        return new CommonTenantObjects();
    }

    @Override
    public Object initEscrowedObj(String tenantName) {
        return init(tenantName);
    }
}
