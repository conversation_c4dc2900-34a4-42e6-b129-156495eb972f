package kl.npki.base.core.tenantholders.common;

/**
 * <AUTHOR>
 * @Date 2024/1/31 10:21
 * @Description:
 */
public interface ICommonTenant {

    /**
     * 首次加载配置
     */
    void reload();

    void notifyChanges();

    /**
     * 判断缓存是否加载成功，例如：集群管理根证书未初始化时，需要多次加载其他服务是否初始化
     * @return true: 已经加载成功，false: 未加载成功
     */
    boolean alreadyLoaded();

    /**
     * 强制重新加载配置并通知其他节点，适用修改配置的场景
     */
    void forceReload();

    /**
     * 刷新配置，接受来自外部的配置变更
     * 适用于配置变更后需要更新缓存的场景
     */
    void refreshInternal();


    String id();
}
