package kl.npki.base.core.tenantholders;


import kl.nbase.config.RefreshableConfigWrapper;
import kl.nbase.config.holder.ConfigHolderInitializer;
import kl.nbase.config.holder.RefreshableConfigHolder;
import kl.nbase.config.utils.BeanCopyUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ConfigHolder implements ITenantEscrow {

    private static List<RefreshableConfigWrapper> configWrapperList = new ArrayList<>();

    public static RefreshableConfigHolder get() {
        return (RefreshableConfigHolder) TenantContextHolder.INSTANCE.getEscrowedObj(ConfigHolder.class);
    }

    public static List<RefreshableConfigHolder> getAll() {
        Set<RefreshableConfigHolder> allEscrowedObj = (Set<RefreshableConfigHolder>) TenantContextHolder.INSTANCE.getAllEscrowedObj(ConfigHolder.class);
        if (CollectionUtils.isEmpty(allEscrowedObj)) {
            return new ArrayList<>();
        }
        // 类型转换
        return allEscrowedObj.stream().map(RefreshableConfigHolder.class::cast).collect(Collectors.toList());
    }

    public static RefreshableConfigHolder get(String tenantName) {
        return (RefreshableConfigHolder) TenantContextHolder.INSTANCE.getEscrowedObj(tenantName, ConfigHolder.class);
    }

    @Override
    public Object initEscrowedObj(String tenantName) {
        ConfigHolderInitializer configHolderInitializer = new ConfigHolderInitializer();
        configHolderInitializer.init(tenantName);
        initWrapper(tenantName, configHolderInitializer);
        return new RefreshableConfigHolder(tenantName);
    }

    /**
     * 此方法只能在系统启动初始化时调用，系统运行时禁止调用
     */
    public static void registConfigWrappers(List<RefreshableConfigWrapper> wrappers) {
        configWrapperList = wrappers;
    }

    private void initWrapper(String tenantName, ConfigHolderInitializer initializer) {
        if (configWrapperList.isEmpty()) {
            return;
        }
        configWrapperList.forEach(wrapper -> {
            if (wrapper.globalConfig()) {
                initializer.initGlobalConfigWrapper(wrapper);
                return;
            }
            // 拷贝一份，避免多租户共享同一个wrapper
            RefreshableConfigWrapper clone = (RefreshableConfigWrapper) BeanCopyUtils.deepClone(wrapper);
            initializer.initWrapper(tenantName, clone);
        });
    }


}
