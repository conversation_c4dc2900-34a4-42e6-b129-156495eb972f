package kl.npki.base.core.tenantholders.common;

import kl.npki.base.core.exception.BaseInternalError;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.reflections.scanners.Scanners.SubTypes;

/**
 * <AUTHOR>
 * @Date 2024/1/30 17:56
 * @Description:
 */
public class CommonTenantObjects {
    private final Map<String, ICommonTenant> commonTenantObjMap = new HashMap<>();

    private static final Map<String, Class<? extends ICommonTenant>> id2CommonTenantObjMap = new HashMap<>();
    private static final String configClassLocation = "kl.npki";

    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(CommonTenantObjects.class);

    static {
        Reflections reflections = new Reflections(configClassLocation, SubTypes);
        Set<Class<?>> mgrClasses = reflections.get(SubTypes.of(ICommonTenant.class).asClass().filter(r ->
            !r.isInterface() // 排除掉顶级接口
                && !Modifier.isAbstract(r.getModifiers()) // 排除掉抽象类
        ));
        for (Class<?> mgrClass : mgrClasses) {
            if (ICommonTenant.class.isAssignableFrom(mgrClass)) {
                try {
                    ICommonTenant commonTenantObj = (ICommonTenant) mgrClass.getDeclaredConstructor().newInstance();
                    id2CommonTenantObjMap.put(commonTenantObj.id(), (Class<? extends ICommonTenant>) mgrClass);
                } catch (Exception e) {
                    logger.error("Failed to instantiate common tenant object: {}", mgrClass.getName(), e);
                }
            }
        }
    }

    public ICommonTenant get(String className) {
        return commonTenantObjMap.get(className);
    }

    public static Class<? extends ICommonTenant> getById(String id) {
        Class<? extends ICommonTenant> clazz = id2CommonTenantObjMap.get(id);
        if (clazz == null) {
            throw BaseInternalError.CLASS_INIT_METHOD_EMPTY.toException("No common tenant object found for id: " + id);
        }
        return clazz;
    }

    public void put(String className, ICommonTenant commonTenantObj) {
        commonTenantObjMap.put(className, commonTenantObj);
    }
}
