package kl.npki.base.core.tenantholders;

import kl.nbase.config.holder.RefreshableConfigHolder;
import kl.nbase.emengine.conf.ClusterEngineConfig;
import kl.nbase.emengine.conf.GroupEngineConfig;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.exception.BaseException;
import kl.npki.base.core.configs.ClusterEmConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Set;

/**
 * 加密机服务持有类
 *
 * <AUTHOR>
 */
public class EngineHolder implements ITenantEscrow {

    private static final Logger logger = LoggerFactory.getLogger(EngineHolder.class);

    public static ClusterEngine get() {
        return (ClusterEngine) TenantContextHolder.INSTANCE.getEscrowedObj(EngineHolder.class);
    }

    public static ClusterEngine get(String tenantId) {
        return (ClusterEngine) TenantContextHolder.INSTANCE.getEscrowedObj(tenantId, EngineHolder.class);
    }

    public static Set<ClusterEngine> getAll() {
        return (Set<ClusterEngine>) TenantContextHolder.INSTANCE.getAllEscrowedObj(EngineHolder.class);
    }

    /**
     * 如果不存在则放入，参考{@link java.util.Map#putIfAbsent(Object, Object)}
     *
     * @param clusterEngine {@link ClusterEngine} 对象
     * @return 如果存在则返回已存在的对象，否则返回null
     */
    public static Object putIfAbsent(ClusterEngine clusterEngine) {
        return TenantContextHolder.INSTANCE.putEscrowedObjIfAbsent(EngineHolder.class, clusterEngine);
    }

    /**
     * 更新密码机引擎
     * @param tenantId 租户id
     * @param clusterEngine 对应mmj配置
     * @return
     */
    public static Object update(String tenantId, ClusterEngine clusterEngine) {
        ClusterEngine oldEngine = get(tenantId);
        try {
            if (oldEngine != null) {
                oldEngine.close();
            }
        } catch (BaseException e) {
            // ignore
        }
        return TenantContextHolder.INSTANCE.updateEscrowedObj(EngineHolder.class, clusterEngine, tenantId);
    }

    public static ClusterEngine init(String tenantName, ClusterEmConfig clusterEmConfig) {
        ClusterEngineConfig clusterEngineConfig = clusterEmConfig.toClusterEngineConfig();
        GroupEngineConfig groupEngine = clusterEngineConfig.getGroupEngine();
        if (Objects.nonNull(groupEngine)) {
            ClusterEngine clusterEngine = new ClusterEngine(clusterEngineConfig);
            logger.info("Key pair generation completed");
            logger.debug("Encryption machine service initialized successfully according to configuration, tenant: {}, cluster name: {}", tenantName, clusterEmConfig.getClusterName());
            return clusterEngine;
        } else {
            logger.debug("The encryption machine service failed to initialize according to configuration, tenant: {}, cluster name: {}, reason for failure: configuration is empty", tenantName, clusterEmConfig.getClusterName());
            return null;
        }
    }

    @Override
    public Object initEscrowedObj(String tenantName) {
        RefreshableConfigHolder refreshableConfigHolder = ConfigHolder.get(tenantName);
        if (refreshableConfigHolder == null) {
            return null;
        }
        ClusterEmConfig clusterEmConfig = refreshableConfigHolder.get(ClusterEmConfig.class);
        ClusterEngine clusterEngine = init(tenantName, clusterEmConfig);
        // 注册关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> this.shutdown(tenantName)));
        return clusterEngine;
    }

    public void shutdown(String tenantName) {
        ClusterEngine engine = get();
        if (engine != null) {
            engine.close();
            logger.info("Tenant [{}] 's clusterEngine closed Successfully", tenantName);
        }
    }

}
