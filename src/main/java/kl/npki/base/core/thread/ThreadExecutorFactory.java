package kl.npki.base.core.thread;

import kl.nbase.helper.thread.BusinessExecutorService;
import kl.nbase.i18n.locale.LocaleContext;
import kl.npki.base.core.configs.ThreadPoolConf;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.TenantContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 统一封装线程执行器
 *
 * <AUTHOR> by niugang on 2025-04-28 13:07
 */
public class ThreadExecutorFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThreadExecutorFactory.class);

    private static final ConcurrentHashMap<String, ExecutorService> RESOURCES_MANAGER = new ConcurrentHashMap<>(8);
    private static final ConcurrentMap<String, Object> LOCK_MAP = new ConcurrentHashMap<>();
    private static final String TRACE_ID = "trace_id";
    private static final String SPAN_ID = "spanId";
    private static final String BIZ_ID = "biz_id";
    public static final String DEFAULT_EXECUTOR_NAME = "DEFAULT_EXECUTOR_NAME";
    /**
     * 防止重复关闭
     */
    private static volatile boolean isShutdown = false;

    static {
        Runtime.getRuntime().addShutdownHook(new Thread(ThreadExecutorFactory::shutdownAll));
    }

    /**
     * 停止所有线程池
     */
    public static void shutdownAll() {
        if (!isShutdown) {
            synchronized (ThreadExecutorFactory.class) {
                if (!isShutdown) {
                    Iterator<Map.Entry<String, ExecutorService>> iterator = RESOURCES_MANAGER.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, ExecutorService> entry = iterator.next();
                        String executorServiceName = entry.getKey();
                        ExecutorService executorService = entry.getValue();
                        try {
                            shutdownThreadPool(executorService, executorServiceName);
                        } finally {
                            // 无论是否成功关闭，都从集合中移除，防止重复操作
                            iterator.remove();
                        }
                    }
                    isShutdown = true;
                }

            }
        }
    }

    /**
     * 停止指定线程池
     *
     * @param threadExecutorName 线程执行器名称
     */
    public static void shutdown(String threadExecutorName) {
        if (StringUtils.isBlank(threadExecutorName)) {
            return;
        }
        Object lock = LOCK_MAP.computeIfAbsent(threadExecutorName, k -> new Object());
        synchronized (lock) {
            ExecutorService removedExecutor = RESOURCES_MANAGER.remove(threadExecutorName);
            if (removedExecutor != null) {
                shutdownThreadPool(removedExecutor, threadExecutorName);
            }
        }

    }


    private static void shutdownThreadPool(ExecutorService executor, String executorServiceName) {
        if (executor == null) {
            return;
        }
        LOGGER.info("start try to shutdown ThreadExecutorFactory.ExecutorService, name[{}]", executorServiceName);
        executor.shutdown();
        int retry = 3;
        while (retry > 0) {
            retry--;
            try {
                if (executor.awaitTermination(1, TimeUnit.SECONDS)) {
                    return;
                }
                LOGGER.info("Retrying shutdown for ThreadExecutorFactory.ExecutorService, name[{}], retries left: {}", executorServiceName, retry);
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
                return;
            } catch (Exception ex) {
                LOGGER.error("ThreadExecutorFactory error during shutdown of {}", executorServiceName, ex);
            }
        }
        executor.shutdownNow();
    }

    public static ExecutorService getDefaultExecutorService() {
        return getExecutorService(DEFAULT_EXECUTOR_NAME);
    }

    public static ExecutorService getExecutorService(String threadExecutorName) {
        return RESOURCES_MANAGER.computeIfAbsent(threadExecutorName, name -> {
            // 获取线程池配置避免重复调用
            final ThreadPoolConf threadPoolConf = BaseConfigWrapper.getThreadPoolConf();
            return new NgpkiBusinessExecutorService(name, 
                    threadPoolConf.getCorePoolSize(), 
                    threadPoolConf.getMaxPoolSize(), 
                    threadPoolConf.getKeepAliveTime(), 
                    TimeUnit.SECONDS, 
                    new ArrayBlockingQueue<>(threadPoolConf.getQueueCapacity()), 
                    new ThreadPoolExecutor.CallerRunsPolicy());
        });
    }

    public static ExecutorService getExecutorService(String threadExecutorName, int corePoolSize) {
        return RESOURCES_MANAGER.computeIfAbsent(threadExecutorName, name -> {
            // 获取线程池配置避免重复调用
            final ThreadPoolConf threadPoolConf = BaseConfigWrapper.getThreadPoolConf();
            return new NgpkiBusinessExecutorService(name, 
                    Math.min(corePoolSize, threadPoolConf.getMaxPoolSize()), 
                    threadPoolConf.getMaxPoolSize(), 
                    threadPoolConf.getKeepAliveTime(), 
                    TimeUnit.SECONDS, 
                    new ArrayBlockingQueue<>(threadPoolConf.getQueueCapacity()), 
                    new ThreadPoolExecutor.CallerRunsPolicy());
        });
    }

    private static class NgpkiBusinessExecutorService extends BusinessExecutorService {

        public NgpkiBusinessExecutorService(String businessName, int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, RejectedExecutionHandler handler) {
            super(businessName, corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
        }

        @NotNull
        @Override
        public Future<?> submit(@NotNull Runnable task) {
            Locale locale = LocaleContext.get();
            String tenantId = TenantContextHolder.getTenantId();
            String traceId = MDC.get(TRACE_ID);
            String spanId = MDC.get(SPAN_ID);
            String bizId = MDC.get(BIZ_ID);
            return super.submit(new NgpkiRunnable(locale, tenantId, traceId, spanId, bizId, task));
        }

        @Override
        public void execute(@NotNull Runnable command) {
            Locale locale = LocaleContext.get();
            String tenantId = TenantContextHolder.getTenantId();
            String traceId = MDC.get(TRACE_ID);
            String spanId = MDC.get(SPAN_ID);
            String bizId = MDC.get(BIZ_ID);
            super.execute(new NgpkiRunnable(locale, tenantId, traceId, spanId, bizId, command));
        }
    }

    private static class NgpkiRunnable implements Runnable {
        private final Locale locale;
        private final String tenantId;
        private final String traceId;
        private final String spanId;
        private final String bizId;
        private final Runnable runnable;

        public NgpkiRunnable(Locale locale, String tenantId, String traceId, String spanId, String bizId, Runnable runnable) {
            this.locale = locale;
            this.tenantId = tenantId;
            this.traceId = traceId;
            this.spanId = spanId;
            this.bizId = bizId;
            this.runnable = runnable;
        }

        @Override
        public void run() {
            try {
                if (locale != null) {
                    LocaleContext.set(locale);
                }
                if (traceId != null) {
                    MDC.put(TRACE_ID, traceId);
                }
                if (tenantId != null) {
                    TenantContextHolder.setTenantId(tenantId);
                }
                if (spanId != null) {
                    MDC.put(SPAN_ID, spanId);
                }
                if (bizId != null) {
                    MDC.put(BIZ_ID, bizId);
                }
                runnable.run();
            } catch (Throwable e) {
                LOGGER.error("ThreadExecutorFactory error during execute", e);
            } finally {
                if (locale != null) {
                    LocaleContext.remove();
                }
                if (tenantId != null) {
                    TenantContextHolder.clear();
                }
                if (traceId != null) {
                    MDC.remove(TRACE_ID);
                }
                if (spanId != null) {
                    MDC.remove(SPAN_ID);
                }
                if (bizId != null) {
                    MDC.remove(BIZ_ID);
                }
            }
        }
    }
}
