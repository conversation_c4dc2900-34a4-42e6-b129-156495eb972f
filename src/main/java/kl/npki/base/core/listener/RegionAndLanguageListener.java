package kl.npki.base.core.listener;

import kl.nbase.config.RefreshableConfig;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.npki.base.core.configs.RegionAndLanguageConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.utils.RegionAndLanguageUtil;
import kl.npki.base.core.utils.SystemUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.SYSTEM_LANGUAGE_CANNOT_BE_MODIFIED_AFTER_DEPLOYMENT_I18N_KEY;

/**
 * 系统语言配置变更监听
 * 部署时语言配置变更后需要将语言配置同步到系统变量
 *
 * <AUTHOR>
 * @date 2025/1/10 11:57
 */
public class RegionAndLanguageListener implements ConfigRefreshListener {

    /**
     * 当前系统配置的默认语言
     */
    public static final String LANGUAGE = "language";
    public static final String REGION = "region";


    private static final Logger logger = LoggerFactory.getLogger(RegionAndLanguageListener.class);


    @Override
    public void onChange(RefreshableConfig before, RefreshableConfig after, Set<String> changedFields) {
        RegionAndLanguageConfig beforeRegionAndLanguageConfig = (RegionAndLanguageConfig) before;
        RegionAndLanguageConfig afterRegionAndLanguageConfig = (RegionAndLanguageConfig) after;

        for (String changedField : changedFields) {
            // 部署完成后不能修改系统语言
            if (LANGUAGE.equals(changedField) || REGION.equals(changedField)) {
                if (SystemUtil.isDeployed()) {
                    // 需要将语言配置进行回退
                    RegionAndLanguageConfig regionalLanguageConfig = BaseConfigWrapper.getRegionalLanguageConfig();
                    regionalLanguageConfig.setRegion(beforeRegionAndLanguageConfig.getRegion());
                    regionalLanguageConfig.setLanguage(beforeRegionAndLanguageConfig.getLanguage());
                    BaseConfigWrapper.getHolder().save(regionalLanguageConfig);
                    throw BaseInternalError.SYSTEM_LANGUAGE_ERROR.toException(SYSTEM_LANGUAGE_CANNOT_BE_MODIFIED_AFTER_DEPLOYMENT_I18N_KEY);
                }
                // 未部署时，系统语言修改后，重新加载系统语言
                RegionAndLanguageUtil.setTimeZone();
                RegionAndLanguageUtil.setDateFormat();
                // 修改系统默认语言
                logger.warn("Initialize the default language of the system: {}_{}", afterRegionAndLanguageConfig.getLanguage(), afterRegionAndLanguageConfig.getRegion());
            }
        }
    }

}
