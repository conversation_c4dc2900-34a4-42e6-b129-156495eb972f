package kl.npki.base.core.listener;

import kl.nbase.config.RefreshableConfig;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.configs.CommonMgrListenerConfig;
import kl.npki.base.core.tenantholders.common.CommonTenantObjects;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> href="mailto:<EMAIL>">dingqi</a>
 * @since 2025/07/07 11:43
 */
public class CommonMgrListener implements ConfigRefreshListener {
    @Override
    public void onChange(RefreshableConfig before, RefreshableConfig after, Set<String> changedFields) {
        CommonMgrListenerConfig newConfig = (CommonMgrListenerConfig) after;
        CommonMgrListenerConfig oldConfig = (CommonMgrListenerConfig) before;
        if (newConfig == null || newConfig.getVersionMap() == null) {
            return;
        }
        if (oldConfig != null && newConfig.getVersionMap().equals(oldConfig.getVersionMap())) {
            return;
        }
        // 重新加载版本信息
        Map<String, Integer> newVersionMap = newConfig.getVersionMap();
        Map<String, Integer> oldVersionMap = oldConfig.getVersionMap();
        if (newVersionMap != null && oldVersionMap != null) {
            newVersionMap.forEach((key, value) -> {
                if (!value.equals(oldVersionMap.get(key))) {
                    // 版本号发生变化，执行相应的逻辑
                    MgrHolder.getCommonTenantObj(CommonTenantObjects.getById(key)).refreshInternal();
                }
            });
        } else if (newVersionMap != null) {
            // 如果旧版本信息不存在，则直接刷新所有新版本
            newVersionMap.forEach((key, value) -> {
                // 版本号发生变化，执行相应的逻辑
                MgrHolder.getCommonTenantObj(CommonTenantObjects.getById(key)).refreshInternal();
            });
        }
    }
}
