package kl.npki.base.core.common;

import kl.nbase.cache.lock.ILock;

/**
 * @Author: guoq
 * @Date: 2024/7/25
 * @description: 通用锁
 */
public enum CommonLock implements ICommonCache{

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "pki:lock";


    public ILock lock(String lockId){
        return getClient().getLock(wrapCacheKey(lockId));
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }
}
