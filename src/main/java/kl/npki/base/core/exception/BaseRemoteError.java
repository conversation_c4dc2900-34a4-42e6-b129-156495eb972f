package kl.npki.base.core.exception;

import kl.nbase.exception.interfaces.IRemoteError;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * Base远程调用错误枚举
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public enum BaseRemoteError implements IRemoteError, ErrorCode {

    UPLOAD_LOG_TO_KCSP_FAILED("001", "上报日志到KCSP失败"),
    ;

    private final String code;
    private final String desc;

    BaseRemoteError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
