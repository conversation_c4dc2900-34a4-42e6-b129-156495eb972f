package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * <AUTHOR>
 * @date 2023/1/8 17:50
 * @Description: 测试管理库 正式管理库 环境枚举类
 */
public enum EnvironmentEnum implements EnumI18n {
    /**
     * 测试环境
     */
    DEMO("demo", "测试管理环境"),

    /**
     * 正式环境 单租户情况下的默认租户
     */
    DEFAULT("default", "正式管理环境");

    private String id;

    private String desc;

    EnvironmentEnum(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public String getId() {
        return id;
    }

    public String getDesc() {
        return tr();
    }

    public static boolean isDeployed(String envId) {
        return DEFAULT.getId().equals(envId);
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
