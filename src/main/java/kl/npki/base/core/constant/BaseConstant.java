package kl.npki.base.core.constant;

import kl.nbase.helper.utils.ResourceUtils;

import java.io.File;

/**
 * @Author: guoq
 * @Date: 2023/2/28
 * @description: 基础常量
 */
public class BaseConstant {

    private BaseConstant() {
        throw new IllegalStateException("Constant class");
    }

    public static final String PROJECT_NAME = "NGPKI";
    /**
     * 管理配置
     */
    public static final String CONFIG_FILE_NAME = "application.properties";

    /**
     * bootstrap.yml
     */
    public static final String BOOTSTRAP_YML = "bootstrap.yml";
    /**
     * 多版本配置
     */
    public static final String PKI_CONFIG_FILE_NAME = "npki.properties";

    /**
     * base config prefix
     */
    public static final String CONFIG_BASE_PREFIX = "kl.base.";
    public static final String CONFIG_MAIN_KEY = "mainKey";
    public static final String CONFIG_ALGO_PREFIX = CONFIG_BASE_PREFIX + "algo";
    public static final String CONFIG_SYSINFO_PREFIX = CONFIG_BASE_PREFIX + "sys";
    public static final String CONFIG_HOMEPAGE_PREFIX = CONFIG_BASE_PREFIX + "homePage";
    public static final String CONFIG_TRANSACTION_PREFIX = CONFIG_BASE_PREFIX + "transaction";
    public static final String CONFIG_COMMON_MGR_PREFIX = CONFIG_BASE_PREFIX + "mgr";
    public static final String CONFIG_WEB_PREFIX = CONFIG_BASE_PREFIX + "web";
    public static final String CONFIG_SYN_ORG_IAM_PREFIX = "kl.ra.syn.org.iam";
    public static final String CONFIG_SYN_ORG_UUM_PREFIX = "kl.ra.syn.org.uum";
    public static final String CONFIG_ORG_PREFIX = "kl.ra.org";
    public static final String CONFIG_MULTIPART_PREFIX = "kl.base.multipart";
    public static final String CONFIG_FIELD_VALIDATION_PREFIX = CONFIG_BASE_PREFIX + "field.validation";
    /**
     * 密评登录模式
     */
    public static final String SECURITY_LOGIN_MODE = "security";
    /**
     * 测试登录模式
     */
    public static final String TEST_LOGIN_MODE = "test";
    /**
     * 安审登录模式
     */
    public static final String AUDIT_LOGIN_MODE = "audit";

    /**
     * 逗号分隔符号
     */
    public static final String COMMA = ",";

    /**
     * 下划线分隔符号
     */
    public static final String UNDERSCORE = "_";

    /**
     * 冒号分隔符号
     */
    public static final String COLON = ":";

    /**
     * 算法分割符号
     */
    public static final String ALGORITHMIC_SPLIT_SYMBOL = COMMA;

    /**
     * koal自定义国密协议
     */
    public static final String GMSSL_PROTOCOL = "GMVPNv1.1";
    /**
     * 国际协议，RSA和ECC算法
     */
    public static final String TLS_PROTOCOL = "TLSv1.2";

    public static final String BASE_PACKAGE = "kl.npki";

    /**
     * 对称算法分隔符
     */
    public static final String SYM_ALGORITHM_SEPARATOR = "/";


    public static final int METRICS_INVALID = 0;
    public static final int METRICS_VALID = 1;

    public static final long METRICS_UNKNOWN_TIME = -1L;


    /**
     * 进制转换基数 10
     */
    public static final int RADIX_DECIMAL = 10;

    /**
     * 进制转换基数 16
     */
    public static final int RADIX_HEXADECIMAL = 16;

    /**
     * 格式化十进制数，至少2位，不足时补0
     */
    public static final String FORMAT_DECIMAL = "%02d";

    /**
     * 格式化十六进制数，至少2位，不足时补0
     */
    public static final String FORMAT_HEXADECIMAL = "%02x";

    /**
     * 下一代PKI 应用简称
     */
    public static final String NEXT_GENERATION_PKI_SHORT_NAME = "NKI";

    /**
     * NPKI-KM 应用简称
     */
    public static final String NPKI_KM_SHORT_NAME = "NGPKI-KM";

    /**
     * NPKI-RA 应用简称
     */
    public static final String NPKI_RA_SHORT_NAME = "NGPKI-RA";

    /**
     * NPKI-CA 应用简称
     */
    public static final String NPKI_CA_SHORT_NAME = "NGPKI-CA";

    /**
     * 证书操作key
     */
    public static final String CERT_OPERATE_KEY = "certOperate";

    /**
     * 应用根目录
     *
     * <li>war包运行部署则是webapps下的应用目录</li>
     * <li>zip包运行部署则是解压后的目录</li>
     * <li>IDEA运行部署则是项目根目录</li>
     */
    public static final String APP_ROOT = getAppRoot();

    /**
     * WEB-INF目录
     */
    public static final String WEB_ROOT = APP_ROOT + "/WEB-INF";

    /**
     * 获取当前项目根路径
     *
     * @return 当前项目根目录
     */
    private static String getAppRoot() {
        String currentClassPath = BaseConstant.class.getProtectionDomain().getCodeSource().getLocation().getPath();
        File currentClassFile = new File(currentClassPath);
        if (currentClassPath.endsWith(ResourceUtils.JAR_SUFFIX) &&
                currentClassFile.getParent().endsWith(ResourceUtils.LIB_NAME)) {
            String webRoot = currentClassFile.getParent().replace(ResourceUtils.LIB_NAME, "");
            // 因为获取到的是lib所在目录，既WEB-INF，所以根目录需要从当前父目录获取
            return new File(webRoot).getParent();
        } else {
            return System.getProperty("user.dir");
        }
    }

}
