package kl.npki.base.core.constant;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * I18nExceptionInfoConstant
 */
public final class I18nExceptionInfoConstant {

    // region kl.npki.base.core.biz.kcsp.service.impl.KcspLogUploadServiceImpl
    /**
     * 管理员操作日志上传失败
     */
    public static final String AN_ERROR_OCCURRED_WHILE_UPLOADING_THE_ADMINISTRATOR_OPERATION_LOG_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "an_error_occurred_while_uploading_the_administrator_operation_log";
    // endregion

    // region kl.npki.base.core.biz.cert.model.CertRequestInfo
    /**
     * 当前使用的抗量子加密密钥类型为{0}
     */
    public static final String THE_CURRENT_TYPE_OF_ANTI_QUANTUM_ENCRYPTION_KEY_USED_IS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_current_type_of_anti_quantum_encryption_key_used_is";
    // endregion

    // region kl.npki.base.core.constant.UserStatus
    /**
     * 当前状态不是待审核状态
     */
    public static final String THE_CURRENT_STATUS_IS_NOT_PENDING_APPROVAL_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_current_status_is_not_pending_approval";
    /**
     * 锁定账户失败，未知用户状态码[{0}]
     */
    public static final String ACCOUNT_LOCKING_FAILED_UNKNOWN_USER_STATUS_CODE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "account_locking_failed_unknown_user_status_code";
    /**
     * 当前状态不是锁定状态
     */
    public static final String THE_CURRENT_STATE_IS_NOT_LOCKED_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_current_state_is_not_locked";
    /**
     * 解锁账户失败，未知用户状态码[{0}]
     */
    public static final String UNLOCKING_ACCOUNT_FAILED_UNKNOWN_USER_STATUS_CODE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "unlocking_account_failed_unknown_user_status_code";
    // endregion

    // region kl.npki.base.core.utils.ColumnUtil
    /**
     * 获取字段名失败
     */
    public static final String FAILED_TO_OBTAIN_FIELD_NAME_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "failed_to_obtain_field_name";
    // endregion

    // region kl.npki.base.core.biz.cert.model.TrustCertEntity
    /**
     * 信任证书公钥转换失败
     */
    public static final String TRUST_CERT_PUBLIC_KEY_CONVERT_ERROR_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "trust_cert_public_key_convert_error";
    /**
     * 请先生成证书请求
     */
    public static final String PLEASE_GENERATE_CERT_REQUEST_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "please_generate_cert_request";
    // endregion

    // region kl.npki.base.core.asn1.custom.EnvelopedKeyPairDataFactory
    /**
     * 不支持的密钥类型，类型为{0}
     */
    public static final String UNSUPPORTED_KEY_TYPE_TYPE_IS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "unsupported_key_type_type_is";
    // endregion

    // region kl.npki.base.core.asn1.gb.SkfEnvelopedKeyBlobFactory
    /**
     * 获取加密密钥对的公钥失败
     */
    public static final String GET_PUBLIC_KEY_FAILED_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "get_public_key_failed";
    // endregion

    // region kl.npki.base.core.constant.MultiSignLoginCertCountEnum
    /**
     * 登录类型为空
     */
    public static final String LOGIN_TYPE_IS_NULL_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "login_type_is_null";
    // endregion

    // region kl.npki.base.core.biz.cert.service.CertSignService
    /**
     * 有效天数小于1
     */
    public static final String THE_VALIDDAYS_IS_LESS_THAN_1_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_validDays_is_less_than_1";
    /**
     * 证书类型{0}不支持
     */
    public static final String THE_CERT_TYPE_IS_NOT_SUPPORTED_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_cert_type_is_not_supported";
    /**
     * 密钥对为空
     */
    public static final String THE_KEY_PAIR_IS_NULL_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_key_pair_is_null";
    // endregion

    // region kl.npki.base.core.utils.EngineUtil
    /**
     * 无法创建Keystore目录{0}
     */
    public static final String COULD_NOT_CREATE_KEYSTORE_DIRECTORY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "could_not_create_keystore_directory";
    // endregion

    // region kl.npki.base.core.configs.EmLoadBalancerConfig
    /**
     * 未定义的加载类型[{0}]
     */
    public static final String UNDEFINED_LOAD_TYPE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "undefined_load_type";
    /**
     * 权重配置为空
     */
    public static final String WEIGHT_CONFIGURATION_IS_NULL_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "weight_configuration_is_null";
    /**
     * 设备[{0}]缺少权重值
     */
    public static final String THE_DEVICE_IS_MISSING_WEIGHT_VALUES_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_device_is_missing_weight_values";
    /**
     * 设备[{0}]权重值小于0
     */
    public static final String THE_DEVICE_WEIGHT_VALUE_IS_LESS_THAN_0_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_device_weight_value_is_less_than_0";
    /**
     * [{0}]设备总权重小于等于0
     */
    public static final String THE_TOTAL_WEIGHT_OF_THE_DEVICE_IS_LESS_THAN_OR_EQUAL_TO0_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_total_weight_of_the_device_is_less_than_or_equal_to0";
    // endregion

    // region kl.npki.base.core.biz.license.service.impl.LicenseServiceImpl
    /**
     * 授权信息为空
     */
    public static final String LICENSE_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "license_is_empty";
    /**
     * 授权内容无效
     */
    public static final String LICENSE_CONTENT_IS_INVALID_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "license_content_is_invalid";
    /**
     * 授权内容无效，状态为{0}
     */
    public static final String LICENSE_CONTENT_IS_INVALID_WITH_STATUS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "license_content_is_invalid_with_status";
    /**
     * 包含非法字符
     */
    public static final String CONTAINS_ILLEGAL_CHARACTERS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "contains_illegal_characters";
    // endregion

    // region kl.npki.base.core.biz.mainkey.service.MainKeyManager
    /**
     * 主密钥ID为{0}
     */
    public static final String THE_MASTER_KEY_ID_IS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_master_key_id_is";
    // endregion

    // region kl.npki.base.core.biz.cert.service.ManageCertMgr
    /**
     * 身份证书签发失败
     */
    public static final String THE_ID_CERT_ISSUE_ERROR_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_id_cert_issue_error";
    /**
     * SSL通信证书签发失败
     */
    public static final String THE_SSL_CERT_ISSUE_ERROR_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_ssl_cert_issue_error";
    /**
     * 可信证书链为空，请先签发或导入证书链
     */
    public static final String THE_TRUSTED_CERTIFICATE_LIST_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_trusted_certificate_list_is_empty";
    /**
     * 待验证证书不能为空
     */
    public static final String THE_CERTIFICATE_TO_BE_VERIFIED_CANNOT_BE_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_certificate_to_be_verified_cannot_be_empty";
    // endregion

    // region kl.npki.base.core.utils.CertUtil
    /**
     * 证书数据格式异常
     */
    public static final String CERTIFICATE_DATA_FORMAT_ABNORMALITY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "certificate_data_format_abnormality";
    /**
     * 证书编码异常
     */
    public static final String CERTIFICATE_ENCODING_EXCEPTION_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "certificate_encoding_exception";
    /**
     * 证书解析失败提示
     */
    public static final String CERTIFICATE_PARSE_ERROR_TIPS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX +  "certificate_parse_error_tips";
    /**
     * 提取SANs时编码或解码失败
     */
    public static final String ENCODING_OR_DECODING_FAILED_WHILE_EXTRACTING_SANS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "encoding_or_decoding_failed_while_extracting_sans";
    // endregion

    // region kl.npki.base.core.biz.config.service.impl.FileConfigServiceImpl
    /**
     * 文件配置实体为空
     */
    public static final String FILE_CONFIG_ENTITY_IS_NULL_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "file_config_entity_is_null";
    /**
     * 文件名为空
     */
    public static final String FILE_NAME_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "file_name_is_empty";
    /**
     * 文件路径为空
     */
    public static final String FILE_PATH_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "file_Path_is_empty";
    /**
     * 文件内容Base64为空
     */
    public static final String FILE_CONTENT_BASE64_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "file_Content_Base64_is_empty";
    // endregion

    // region kl.npki.base.core.listener.I18nConfigListener
    /**
     * 系统语言不能在部署后修改
     */
    public static final String SYSTEM_LANGUAGE_CANNOT_BE_MODIFIED_AFTER_DEPLOYMENT_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "system_language_cannot_be_modified_after_deployment";
    // endregion

    // region kl.npki.base.core.biz.cert.service.IdCertMgr
    /**
     * 身份证书公钥转换失败
     */
    public static final String ID_CERT_PUBLIC_KEY_CONVERT_ERROR_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "id_cert_public_key_convert_error";
    // endregion

    // region kl.npki.base.core.biz.cert.service.impl.TrustCertServiceImpl
    /**
     * 信任证书ID{0}
     */
    public static final String TRUST_CERT_ID_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "trust_cert_id";
    /**
     * 根证书已存在
     */
    public static final String THE_ROOT_CERT_ALREADY_EXIST_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_root_cert_already_exist";
    /**
     * 请先导入根证书
     */
    public static final String PLEASE_IMPORT_ROOT_CERT_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "please_import_root_cert";
    /**
     * 签发管理根证书失败
     */
    public static final String ISSUE_MANAGE_ROOT_CERT_ERROR_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "issue_manage_root_cert_error";
    /**
     * 根证书不存在
     */
    public static final String THE_ROOT_CERT_NOT_EXIST_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_root_cert_not_exist";
    /**
     * 十六进制证书序列号{0}
     */
    public static final String HEXADECIMAL_CERTIFICATE_SERIAL_NUMBER_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "hexadecimal_certificate_serial_number";
    /**
     * 证书不存在
     */
    public static final String CERT_NOT_FOUND_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "cert_not_found";
    /**
     * 证书为空
     */
    public static final String CERT_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "cert_is_empty";
    /**
     * 不是ca证书
     */
    public static final String NOT_CA_CERT_I18N_KEY  = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "not_ca_cert";
    // endregion

    // region kl.npki.base.core.utils.PfxUtils
    /**
     * PFX密码为空
     */
    public static final String PFX_PASSWORD_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "pfx_password_is_empty";
    // endregion

    // region kl.npki.base.core.common.crypto.SignatureHelper
    /**
     * 签名验证失败
     */
    public static final String SIGNATURE_VERIFICATION_FAILED_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "signature_verification_failed";
    /**
     * 签名为空
     */
    public static final String SIGNATURE_IS_NULL_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "signature_is_null";

    // endregion

    // region kl.npki.base.core.biz.check.SelfCheckManager
    /**
     * 待审核项目{0}未注册
     */
    public static final String THE_CHECK_ITEM_IS_NOT_REGISTERED_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_check_item_is_not_registered";
    // endregion

    // region kl.npki.base.core.repository.BaseRepository
    /**
     * 无国际化值
     */
    public static final String THE_GETFULLDATAHASH_METHOD_OF_THE_DATA_OBJECT_MUST_BE_PUBLIC_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_getFullDataHash_method_of_the_data_object_must_be_public";
    /**
     * 数据对象的getFullDataHash方法必须返回一个String类型
     */
    public static final String THE_GETFULLDATAHASH_METHOD_OF_A_DATA_OBJECT_MUST_RETURN_A_STRING_TYPE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_getFullDataHash_method_of_a_data_object_must_return_a_string_type";
    /**
     * 数据对象必须有一个没有参数的方法名为getFullDataHash
     */
    public static final String THE_DATA_OBJECT_MUST_HAVE_A_PARAMETER_FREE_METHOD_CALLED_GETFULLDATAHASH_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_data_object_must_have_a_parameter_free_method_called_getFullDataHash";
    /**
     * 无法访问数据对象的getFullDataHash方法
     */
    public static final String THE_GETFULLDATAHASH_METHOD_THAT_CANNOT_ACCESS_THE_DATA_OBJECT_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_getFullDataHash_method_that_cannot_access_the_data_object";
    /**
     * 调用数据对象的getFullDataHash方法时发生错误
     */
    public static final String AN_ERROR_OCCURRED_WHILE_CALLING_THE_GETFULLDATAHASH_METHOD_OF_THE_DATA_OBJECT_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "an_error_occurred_while_calling_the_getFullDataHash_method_of_the_data_object";
    // endregion

    // region kl.npki.base.core.constant.MainKeyAlgoEnum
    /**
     * 主密钥算法转换失败
     */
    public static final String MASTER_KEY_ALGORITHM_CONVERSION_FAILED_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "master_key_algorithm_conversion_failed";
    // endregion

    // region kl.npki.base.core.biz.license.service.impl.AbstractExternalLicenseCheckImpl
    /**
     * 获取授权状态失败
     */
    public static final String GET_LICENSE_STATUS_FAILED_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "get_license_status_failed";
    // endregion

    // region kl.npki.base.core.utils.KeyStoreUtil
    /**
     * 信任Keystore文件已存在[{0}]
     */
    public static final String TRUST_KEYSTORE_FILE_ALREADY_EXISTS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "trust_keystore_file_already_exists";
    /**
     * 生成信任Keystore文件失败
     */
    public static final String ERROR_GENERATING_TRUST_KEYSTORE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "error_generating_trust_keystore";
    // endregion

    // region kl.npki.base.core.utils.PriKeyUtils
    /**
     * 获取私钥失败
     */
    public static final String FAILED_TO_OBTAIN_PRIVATE_KEY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "failed_to_obtain_private_key";
    // endregion

    // region kl.npki.base.core.utils.PQExtensionUtil
    /**
     * 从扩展中获取抗量子保护公钥失败，扩展为空
     */
    public static final String FAILED_TO_OBTAIN_ANTI_QUANTUM_PROTECTION_PUBLIC_KEY_FROM_EXTENSION_EXTENSION_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "failed_to_obtain_anti_quantum_protection_public_key_from_extension_extension_is_empty";
    /**
     * 从扩展中获取抗量子保护公钥失败，没有对应的扩展
     */
    public static final String FAILED_TO_OBTAIN_ANTI_QUANTUM_PROTECTION_PUBLIC_KEY_FROM_EXTENSION_THERE_IS_NO_CORRESPONDING_EXTENSION_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "failed_to_obtain_anti_quantum_protection_public_key_from_extension_there_is_no_corresponding_extension";
    // endregion

    // region kl.npki.base.core.configs.EmConfig
    /**
     * 未检测到 {0}[{1}]的密钥数据目录，请使用encryption-fusion工具为其生成内置密钥。生成时请将结果路径指定为：{2}，如已生成则将已生成的目录拷贝为：" {2}
     */
    public static final String THE_KEY_DATA_DIRECTORY_DOES_NOT_EXIST_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_key_data_directory_does_not_exist";
    // endregion

    // region kl.npki.base.core.repository.BaseRepository
    /**
     * {0}原文为空
     */
    public static final String THE_ORIGINAL_TEXT_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_original_text_is_empty";
    /**
     * {0}哈希值为空
     */
    public static final String HASH_VALUE_IS_EMPTY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "hash_value_is_empty";
    // endregion

    // region kl.npki.base.core.biz.cert.service.CertVerifyHelper
    /**
     * 证书{0}尚未生效，生效时间为{1}
     */
    public static final String THE_CERTIFICATE_HAS_NOT_YET_TAKEN_EFFECT_WITH_AN_EFFECTIVE_DATE_OF_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_certificate_has_not_yet_taken_effect_with_an_effective_date_of";
    /**
     * {0}证书已经失效,失效时间为：{1}
     */
    public static final String THE_CERTIFICATE_HAS_EXPIRED_WITH_AN_EXPIRATION_DATE_OF_I18N_KEY =  BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "the_certificate_has_expired_with_an_expiration_date_of";

    /**
     * 待签发证书有效期[{0}]超出颁发者证书有效期[{1}]
     */
    public static final String CERTIFICATE_VALIDITY_EXCEEDS_ISSUER_VALIDITY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "certificate_validity_exceeds_issuer_validity";
    // endregion

    /**
     * 第{0}列格式为：[{1}]但期望格式为：[{2}]
     */
    public static final String EXCEL_HEADER_IS_NOT_VALID_OF_COLUMN = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "excel_header_is_not_valid_of_column";


    // region kl.npki.ca.core.annotation.ValueOfEnum

    public static final String  ILLEGAL_ENUM_TYPE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "illegal_enum_type";

    // endregion
}
