package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * 证书类型
 * <AUTHOR>
 * @date 2022/12/22
 */
public enum CertTypeEnum implements EnumI18n {

    /**
     * 自签发证书
     */
    SELF_SIGN_CERT(1, "自签发证书"),
    
    /**
     * 用户签名证书
     */
    USER_SIGN_CERT(2, "用户签名证书"),
    
    /**
     * 用户加密证书
     */
    USER_ENC_CERT(3, "用户加密证书"),
    
    /**
     * SSL签名服务端证书
     */
    SSL_SIGN_SERVER_CERT(4, "SSL签名服务端证书"),
    
    /**
     * SSL加密服务端证书
     */
    SSL_ENC_SERVER_CERT(5, "SSL加密服务端证书"),

    /**
     * SSL签名客户端证书
     */
    SSL_SIGN_CLIENT_CERT(6, "SSL签名客户端证书"),

    /**
     * SSL加密客户端证书
     */
    SSL_ENC_CLIENT_CERT(7, "SSL加密客户端证书"),
    
    ;
    
    private int id;
    
    private String desc;


    CertTypeEnum(int id, String desc) {
        this.id = id;
        this.desc = desc;
    }


    public int getId() {
        return id;
    }

    public String getDesc() {
        return tr();
    }
    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
    
}
