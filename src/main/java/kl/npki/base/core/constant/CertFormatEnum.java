package kl.npki.base.core.constant;

/**
 * 证书格式
 *
 * <AUTHOR>
 */
public enum CertFormatEnum {

    /**
     * 未知
     */
    UNKNOWN,

    /**
     * 带头尾B64字符串格式
     */
    PEM,

    /**
     * 二进制格式
     */
    DER,

    /**
     * 证书格式，可能为B64，可能为二进制
     */
    CER,

    /**
     * 带私钥的证书格式，需要密码
     */
    PFX,

    /**
     * JKS格式(Java密钥库)
     */
    JKS;

    public static CertFormatEnum getByName(String name) {
        try {
            return valueOf(name);
        } catch (Exception e) {
            return UNKNOWN;
        }
    }

}
