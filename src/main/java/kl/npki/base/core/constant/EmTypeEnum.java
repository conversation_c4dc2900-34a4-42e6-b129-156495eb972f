package kl.npki.base.core.constant;

import kl.nbase.emengine.constant.EngineTypeEnum;
import kl.nbase.emengine.constant.FusionDeviceTypeEnum;
import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * <AUTHOR>
 * @since 2024/8/1
 */
public enum EmTypeEnum implements EnumI18n {
    FILE_ENGINE("file", "文件密码机", EngineTypeEnum.FILE_ENGINE, null),
    STANDARD_ENGINE("standard", "标准密码机", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.STANDARD),
    EMULATOR_ENGINE("emulator", "高性能密码机", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.EMULATOR),
    KOAL_ENGINE("koal", "KOAL密码机", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.KOAL),
    CHINA_CORE_CCP_907_T("china_core_ccp_907_t", "国芯密码卡CCP-907-T", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.CHINA_CORE_CCP_907_T),
    MUCSE_RSP_20("mucse_rsp_20", "沐创密码卡RSP-20", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.MUCSE_RSP_20),
    MUCSE_RSP_20_CAP("mucse_rsp_20_cap", "沐创密码卡RSP-20-CAP", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.MUCSE_RSP_20_CAP),
    SANSEC("sansec", "三未信安SJJ1012A密码机", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.SANSEC),
    SANSEC_FIPS("sansec_fips", "三未信安FIPS密码机", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.SANSEC_FIPS),
    HONGSI("hongsi", "宏思密码卡", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.HONGSI),
    HYGON("hygon", "海光密码卡", EngineTypeEnum.FUSION_SDF_ENGINE, FusionDeviceTypeEnum.HYGON),
    ;

    private final String engineType;
    private final String engineAlias;
    private final EngineTypeEnum engineTypeEnum;
    private final FusionDeviceTypeEnum fusionDeviceTypeEnum;

    EmTypeEnum(String engineType, String engineAlias, EngineTypeEnum engineTypeEnum, FusionDeviceTypeEnum fusionDeviceTypeEnum) {
        this.engineType = engineType;
        this.engineAlias = engineAlias;
        this.engineTypeEnum = engineTypeEnum;
        this.fusionDeviceTypeEnum = fusionDeviceTypeEnum;
    }

    public String getEngineType() {
        return engineType;
    }

    public String getEngineAlias() {
        return tr();
    }

    public EngineTypeEnum getEngineTypeEnum() {
        return engineTypeEnum;
    }

    public FusionDeviceTypeEnum getFusionDeviceTypeEnum() {
        return fusionDeviceTypeEnum;
    }

    public static EmTypeEnum getEmTypeEnumByEngineType(String engineType) {
        for (EmTypeEnum emTypeEnum : EmTypeEnum.values()) {
            if (emTypeEnum.getEngineType().equals(engineType)) {
                return emTypeEnum;
            }
        }
        return FILE_ENGINE;
    }

    public static boolean isFileEngine(String engineType) {
        return FILE_ENGINE.getEngineType().equals(engineType);
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
