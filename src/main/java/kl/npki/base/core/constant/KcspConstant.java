package kl.npki.base.core.constant;

/**
 * KCSP 常量池
 *
 * <AUTHOR>
 * @date 2024/5/16
 */
public final class KcspConstant {

    private KcspConstant() {
    }

    /**
     * 应用名称 环境变量标识
     */
    public static final String APP_NAME_ENV_KEY = "APP_NAME";

    /**
     * KCSP 日志上报存储类型
     */
    public static final String KCSP_LOG_STORE_TYPE = "kcspLogStore";

    /**
     * KCSP 管理员操作日志上报类型
     */
    public static final String KCSP_ADMIN_OPERATION_LOG_TYPE = "kcspAdminOpLog";

    /**
     * KCSP 商密监管日志上报类型
     */
    public static final String KCSP_ALGO_USAGE_LOG_TYPE = "kcspEngineAlgoUsageLog";

    /**
     * KCSP 服务API日志上报类型
     */
    public static final String KCSP_SERVICE_API_LOG_TYPE = "kcspServiceApiLog";

    /**
     * KCSP 加密机类型
     */
    public static final String KCSP_SDF_ENGINE_TYPE = "KcspSdf";

    /**
     * KCSP 管理端根证书别名
     */
    public static final String KCSP_DEFAULT_MGR_ROOT_CA_ALIAS = "PKI-KCSP-MGR-ROOT";

    /**
     * KCSP 身份证书别名
     */
    public static final String KCSP_DEFAULT_ID_CERT_ALIAS = "PKI-KCSP-ID-CERT";

    /**
     * KCSP SSL 站点证书别名
     */
    public static final String KCSP_DEFAULT_SSL_SITE_ALIAS = "PKI-KCSP-SSL-SITE";

    /**
     * KCSP3.0 服务注册 Key
     */
    public static final String KL_NPKI_FUNCTION_TYPE = "function-type";

    /**
     * KCSP3.0 服务注册 Key
     */
    public static final String KL_NPKI_SERVICE_TYPE = "service-type";

    /**
     * KCSP3.0定义的管理节点 function_type=1
     */
    public static final String KCSP_NPKI_MANAGE_SUFFIX = "management";

    /**
     * KCSP3.0定义的服务节点 function_type=0
     */
    public static final String KCSP_NPKI_SERVICE_SUFFIX = "service";

    /**
     * KCSP3.0 服务注册 服务名称
     */
    public static final String DEFAULT_SERVICE_NAME = "kl-nkm";

    /**
     * KCSP3.0 服务注册 获取用户详情接口
     */
    public static final String GET_USER_DETAIL_URL = "/user/v1/user/serv/detail";

    /**
     * KCSP3.0 服务注册 管理员操作日志上报接口
     */
    public static final String REPORT_ADMIN_OPER_LOG_URL = "/log/v1/log/addLog";

    /**
     * KCSP3.0 服务注册 服务API日志上报接口
     */
    public static final String REPORT_SERVICE_API_LOG_URL = "/cipher/v1/log/add";

    /**
     * KCSP3.0 服务注册 商密监管日志上报接口
     */
    public static final String REPORT_SECRETS_MONITOR_URL = "/log/v1/logAlgousage/store";

}
