package kl.npki.base.core.constant;

import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.i18n.i18n.EnumI18n;
import kl.npki.base.core.exception.BaseValidationError;

import java.util.*;

import static kl.npki.base.core.constant.I18nConstant.*;

/**
 * 用户证书状态
 *
 * <AUTHOR>
 * @Date 2023/11/82
 */
public enum CertStatus implements EnumI18n {

    RECOVERED(-5, "证书被恢复"),

    KEY_UPDATED(-4, "密钥被更新"),

    EXTEND(-3, "证书被延期"),

    UPDATED(-2, "证书被更新"),

    REVOKED(-1, "证书被废除"),

    TO_BE_ISSUED(0, "待签发"),

    ISSUED(1, "已签发"),

    UPDATE_TO_BE_CHECKED(2001, "更新待审核"),

    TO_BE_UPDATED(2002, "待更新"),

    EXTEND_TO_BE_CHECKED(2003, "延期待审核"),

    TO_BE_EXTENDED(2004, "待延期"),

    FROZEN(2005, "证书被冻结"),

    REISSUE_TO_BE_CHECKED(2007, "重发待审核"),

    TO_BE_RECOVERED(2008, "待恢复"),

    TO_BE_REISSUED(2009, "待重发"),

    FREEZE_TO_BE_CHECKED(2010, "冻结待审核"),

    TO_BE_FROZEN(2011, "待冻结"),

    UNFREEZE_TO_BE_CHECKED(2012, "解冻待审核"),

    TO_BE_UNFROZEN(2013, "待解冻"),

    KEY_UPDATE_TO_BE_CHECKED(2014, "密钥更新待审核"),

    KEY_TO_BE_UPDATE(2015, "密钥待更新"),

    RECOVERY_TO_BE_CHECKED(2016, "恢复待审核"),

    REVOKE_TO_BE_CHECKED(2017, "废除待审核"),

    REVOKE_AT_FROZENT_STATUS_TO_BE_CHECKED(2018, "冻结废除待审核"),

    TO_BE_REVOKED(2019, "待废除"),

    TO_BE_REVOKED_AT_FROZENT_STATUS(2021, "冻结待废除"),
    ;

    /**
     * 该Map的Key是待审核状态码，Value是一个列表，该列表的索引0上是该Key对应的待审核状态、索引1上是其审核通过后对应的状态、索引2上是其审核拒绝后对应的状态。
     */
    private static final Map<Integer, List<CertStatus>> TO_BE_CHECKED_MAP = new HashMap<>(16);
    private static final int TO_BE_CHECKED_INDEX = 0;
    private static final int PASS_CHECK_INDEX = 1;
    private static final int REJECT_CHECK_INDEX = 2;

    /**
     * 允许登录的证书状态
     */
    private static final Map<Integer, CertStatus> ALLOWED_LOGIN_STATUS_MAP = new HashMap<>(16);

    static {
        TO_BE_CHECKED_MAP.put(UPDATE_TO_BE_CHECKED.getCode(), Arrays.asList(UPDATE_TO_BE_CHECKED, TO_BE_UPDATED, ISSUED));
        TO_BE_CHECKED_MAP.put(EXTEND_TO_BE_CHECKED.getCode(), Arrays.asList(EXTEND_TO_BE_CHECKED, TO_BE_EXTENDED, ISSUED));
        TO_BE_CHECKED_MAP.put(FREEZE_TO_BE_CHECKED.getCode(), Arrays.asList(FREEZE_TO_BE_CHECKED, TO_BE_FROZEN, ISSUED));
        TO_BE_CHECKED_MAP.put(UNFREEZE_TO_BE_CHECKED.getCode(), Arrays.asList(UNFREEZE_TO_BE_CHECKED, TO_BE_UNFROZEN, ISSUED));
        TO_BE_CHECKED_MAP.put(RECOVERY_TO_BE_CHECKED.getCode(), Arrays.asList(RECOVERY_TO_BE_CHECKED, TO_BE_RECOVERED, ISSUED));
        TO_BE_CHECKED_MAP.put(REVOKE_TO_BE_CHECKED.getCode(), Arrays.asList(REVOKE_TO_BE_CHECKED, TO_BE_REVOKED, ISSUED));
        TO_BE_CHECKED_MAP.put(KEY_UPDATE_TO_BE_CHECKED.getCode(), Arrays.asList(KEY_UPDATE_TO_BE_CHECKED, KEY_TO_BE_UPDATE, ISSUED));
        TO_BE_CHECKED_MAP.put(REISSUE_TO_BE_CHECKED.getCode(), Arrays.asList(REISSUE_TO_BE_CHECKED, TO_BE_REISSUED, REVOKED));
        TO_BE_CHECKED_MAP.put(REVOKE_AT_FROZENT_STATUS_TO_BE_CHECKED.getCode(), Arrays.asList(REVOKE_AT_FROZENT_STATUS_TO_BE_CHECKED, TO_BE_REVOKED_AT_FROZENT_STATUS, FROZEN));

        ALLOWED_LOGIN_STATUS_MAP.put(ISSUED.getCode(), ISSUED);
        ALLOWED_LOGIN_STATUS_MAP.put(REVOKE_TO_BE_CHECKED.getCode(), REVOKE_TO_BE_CHECKED);
        ALLOWED_LOGIN_STATUS_MAP.put(TO_BE_REVOKED.getCode(), TO_BE_REVOKED);
        ALLOWED_LOGIN_STATUS_MAP.put(EXTEND_TO_BE_CHECKED.getCode(), EXTEND_TO_BE_CHECKED);
        ALLOWED_LOGIN_STATUS_MAP.put(TO_BE_EXTENDED.getCode(), TO_BE_EXTENDED);
    }

    private final int code;
    private final String desc;

    CertStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    public static CertStatus valueOf(int code) {
        CertStatus status = null;
        for (CertStatus value : CertStatus.values()) {
            if (value.getCode() == code) {
                status = value;
                break;
            }
        }
        return status;
    }

    /**
     * 根据code获取国际化描述
     */
    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        CertStatus certStatus = valueOf(code);
        return Objects.nonNull(certStatus) ? certStatus.getDesc() : getBaseCoreI18nMessage(COMMON_UNKNOWN);
    }

    /**
     * 获取待审核状态码集合
     */
    public static Set<Integer> getToBeCheckedStatusSet() {
        return TO_BE_CHECKED_MAP.keySet();
    }

    /**
     * 判断当前状态是否为待审核相关状态
     */
    public static boolean isToBeChecked(int code) {
        return TO_BE_CHECKED_MAP.get(code) != null;
    }

    /**
     * 获取审核通过后对应的状态，如果入参的状态码不为待审核相关状态则抛出异常
     */
    public static CertStatus passCheck(int code) {
        List<CertStatus> statusList = TO_BE_CHECKED_MAP.get(code);
        CheckUtils.notNull(statusList, BaseValidationError.USER_CERT_STATUS_ERROR.toException("当前状态不为待审核状态"));
        return statusList.get(PASS_CHECK_INDEX);
    }

    /**
     * 获取审核拒绝后对应的状态，如果入参的状态码不为待审核相关状态则抛出异常
     */
    public static CertStatus rejectCheck(int code) {
        List<CertStatus> statusList = TO_BE_CHECKED_MAP.get(code);
        CheckUtils.notNull(statusList, BaseValidationError.USER_CERT_STATUS_ERROR.toException("当前状态不为待审核状态"));
        return statusList.get(REJECT_CHECK_INDEX);
    }

    /**
     * 判断当前证书状态是否允许进行登录
     */
    public static boolean allowToLogin(int code) {
        return Objects.nonNull(ALLOWED_LOGIN_STATUS_MAP.get(code));
    }

    public static Set<Integer> getAllowedLoginStatusSet() {
        return ALLOWED_LOGIN_STATUS_MAP.keySet();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
