package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import java.util.Objects;

import static kl.npki.base.core.constant.I18nConstant.*;

/**
 * @Author: guoq
 * @Date: 2025/1/6
 * @description: CA等级
 */
public enum CaLevel implements EnumI18n {
    /**
     * 根证书
     */
    ROOT(1,"root.certificate"),
    SUB(2,"lower.level.certificate");

    private final int code;
    private final String desc;

    CaLevel(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    public static CaLevel getCaLevelByCode(Integer code) {
        for (CaLevel caLevel : CaLevel.values()) {
            if (caLevel.getCode() == code) {
                return caLevel;
            }
        }
        return null;
    }

    /**
     * 根据code获取国际化描述
     */
    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        CaLevel caLevel = getCaLevelByCode(code);
        return Objects.nonNull(caLevel) ? caLevel.getDesc() : getBaseCoreI18nMessage(COMMON_UNKNOWN);
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
