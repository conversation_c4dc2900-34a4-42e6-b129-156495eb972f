package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 机构操作
 *
 * <AUTHOR>
 * @date 2024/07/04
 */
public enum OrgOperationEnum implements EnumI18n {

    ADD("机构新增"),
    UPDATE("机构修改"),
    REVOKE("机构废除"),
    DELETE("机构删除");

    private String desc;

    OrgOperationEnum(String desc) {
        this.desc = desc;
    }


    public String getDesc() {
        return tr();
    }

    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
