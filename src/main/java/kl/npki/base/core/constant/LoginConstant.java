package kl.npki.base.core.constant;

/**
 * <AUTHOR>
 * @date 2022/6/22
 * @desc 登录/签名验签 常量值
 */
public class LoginConstant {

    private LoginConstant() {
    }

    public static final String AUTHORIZATION_KEY = "Authorization";
    /**
     * 临时会话id
     */
    public static final String TEMP_SESSION_ID = "TempSessionId";
    /**
     * 会话id默认过期时间，单位毫秒
     */
    public static final long DEFAULT_SESSION_ID_TTL = 5 * 60 * 1000L;
    public static final String ROLE_ID = "roleId";
    public static final String USER_ID = "userId";
    public static final String USERNAME = "username";
    /**
     * 操作签名值
     */
    public static final String SIGN_DATA = "signData";

    /**
     * 登录接口路径，不包含apiPrefix
     */
    public static final String LOGIN_API_PATH = "/login";

    /**
     * 登录重试次数限制
     */
    public static final int MAX_LOGIN_RETRIES = 5;

    /**
     * 登录重试次数超出限制次数后账户锁定时间，值为1天包含的毫秒数
     */
    public static final long LOCK_ACCOUNT_TTL = 24 * 60 * 60 * 1000L;

    /**
     * 部署者的token过期时间，值为10小时包含的毫秒数
     */
    public static final long DEPLOYER_TOKEN_TTL = 10 * 60 * 60 * 1000L;

}
