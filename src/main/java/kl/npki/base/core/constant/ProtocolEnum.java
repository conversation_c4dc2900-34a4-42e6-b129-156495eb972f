package kl.npki.base.core.constant;

/**
 * TLS协议枚举
 *
 * <AUTHOR>
 */
public enum ProtocolEnum {
    /**
     * 协议枚举
     */
    UNKNOWN(0, "UNKNOWN", "UNKNOWN"),
    SSLv2(1, "SSLv2", "SSL 2.0"),
    SSLv3(2, "SSLv3", "SSL 3.0"),
    TLSv10(3, "TLSv1", "TLS 1.0"),
    TLSv11(4, "TLSv1.1", "TLS 1.1"),
    TLSv12(5, "TLSv1.2", "TLS 1.2"),
    TLSv13(6, "TLSv1.3", "TLS 1.3"),
    GMVPNv11(8, "GMVPNv1.1", "GMVPN 1.1");

    private final Integer id;

    private final String protocol;

    private final String desc;


    ProtocolEnum(Integer id, String protocol, String desc) {
        this.id = id;
        this.protocol = protocol;
        this.desc = desc;
    }

    public static ProtocolEnum getByDesc(String desc) {
        for (ProtocolEnum tlsProtocolEnum : values()) {
            if (tlsProtocolEnum.getDesc().equals(desc)) {
                return tlsProtocolEnum;
            }
        }
        return UNKNOWN;
    }

    public static ProtocolEnum getByName(String name) {
        for (ProtocolEnum tlsProtocolEnum : values()) {
            if (tlsProtocolEnum.getProtocol().equals(name)) {
                return tlsProtocolEnum;
            }
        }
        return UNKNOWN;
    }

    public Integer getId() {
        return id;
    }

    public String getProtocol() {
        return protocol;
    }

    public String getDesc() {
        return desc;
    }
}