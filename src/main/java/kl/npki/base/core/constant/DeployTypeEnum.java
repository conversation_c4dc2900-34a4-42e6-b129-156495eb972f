package kl.npki.base.core.constant;

/**
 * 系统部署类型枚举
 * <p>
 * 每个子系统zip包部署的时候需要在启动脚本中设置<code>KL_NGPKI_DEPLOY_TYPE</code>环境变量为ZIP。
 * war包部署的时候可以不用设置该环境变量，默认为WAR部署。
 * </p>
 *
 * <AUTHOR>
 * @create 2025/5/27 上午10:22
 */
public enum DeployTypeEnum {

    /**
     * war包部署
     */
    WAR,

    /**
     * zip包部署
     */
    ZIP;

    private static DeployTypeEnum deployType;

    /**
     * 系统部署类型
     * 通过系统属性 KL_NGPKI_DEPLOY_TYPE 获取，默认为 WAR
     */
    private static final String KL_NGPKI_DEPLOY_TYPE = "KL_NGPKI_DEPLOY_TYPE";
    private static final String DEFAULT_DEPLOY_TYPE = "WAR";

    public static DeployTypeEnum getDeployType() {
        if (null == deployType) {
            String type = System.getProperty(KL_NGPKI_DEPLOY_TYPE, DEFAULT_DEPLOY_TYPE);
            deployType = DeployTypeEnum.valueOf(type);
        }
        return deployType;
    }
}