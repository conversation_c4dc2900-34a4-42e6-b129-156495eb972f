package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午3:48
 */
public enum UploadFileProcessStatusEnum implements EnumI18n {
    UPLOAD(-1, "已上传"),
    PROCESSING(0, "正在处理中"),
    COMPLETE(1, "处理完成"),
    ;

    private final Integer status;

    private final String desc;

    UploadFileProcessStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * 根据处理状态转换成枚举对象
     *
     * @param status 上传文件处理状态
     * @return
     */
    public static UploadFileProcessStatusEnum valueOfByStatus(int status) {
        for (UploadFileProcessStatusEnum uploadFileProcessStatus : values()) {
            if (uploadFileProcessStatus.getStatus() == status) {
                return uploadFileProcessStatus;
            }
        }
        return null;
    }

    public int getStatus() {
        return status;
    }


    public String getDesc() {
        return tr();
    }

    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX +  this.name().toLowerCase() ;
    }
}