package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 系统日志类型枚举类
 *
 * <AUTHOR>
 * @since 2025/7/7 15:01
 */
public enum SystemLogEnum implements EnumI18n {

    /**
     * 安全日志
     */
    SECURITY_LOG("security_log", 1, "安全操作日志"),

    /**
     * 业务日志
     */
    OPERATION_LOG("operation_log", 2, "业务操作日志"),

    /**
     * 系统服务日志
     */
    API_LOG("api_log", 3, "系统服务日志"),

    ;

    private final String name;
    private final Integer type;
    private final String desc;

    SystemLogEnum(String name, Integer code, String desc) {
        this.name = name;
        this.type = code;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase() + "_fileName";
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String tr() {
        return EnumI18n.super.tr();
    }
}
