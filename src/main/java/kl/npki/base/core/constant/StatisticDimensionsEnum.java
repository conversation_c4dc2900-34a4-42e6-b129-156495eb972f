package kl.npki.base.core.constant;

/**
 * 统计维度
 * <AUTHOR>
 */
public enum StatisticDimensionsEnum {
    /**
     * 按天统计
     */
    BY_DAY(0, "by_day"),
    /**
     * 按月统计
     */
    BY_MONTH(1, "by_month");

    private int code;

    private String desc;

    StatisticDimensionsEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
