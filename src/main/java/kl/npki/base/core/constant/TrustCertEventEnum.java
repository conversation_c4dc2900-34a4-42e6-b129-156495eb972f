package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 证书链配置事件
 *
 * <AUTHOR> by niugang on 2025-07-24 10:05
 */
public enum TrustCertEventEnum implements EnumI18n {

    /**
     *
     */
    IMPORT_TRUST_CERT("导入证书链"),
    DELETE_TRUST_CERT("删除证书链");

    private String desc;

    TrustCertEventEnum(String desc) {
        this.desc = desc;
    }


    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
