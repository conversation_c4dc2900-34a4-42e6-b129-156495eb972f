package kl.npki.base.core.constant;

import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.i18n.i18n.EnumI18n;
import kl.npki.base.core.exception.BaseValidationError;

import java.util.*;

import static kl.npki.base.core.constant.I18nConstant.*;
import static kl.npki.base.core.constant.I18nExceptionInfoConstant.*;
/**
 * 用户状态
 *
 * <AUTHOR>
 * @Date 2023/11/8
 */
public enum UserStatus implements EnumI18n {

    CANCELLED(-1, "已注销"),

    REGISTER_TO_BE_CHECKED(1000, "用户注册待审核"),

    REGISTER_CHECK_FAILED(1001, "注册审核未通过"),

    NORMAL(1002, "正常"),

    TO_BE_UPDATED(1003, "用户待更新"),

    UPDATE_TO_BE_CHECKED(1004, "用户更新待审核"),

    CANCEL_TO_BE_CHECKED(1005, "用户注销待审核"),

    TO_BE_CANCLED(1006, "用户待注销"),

    /**
     * 2xxx状态码均表示账号已锁定，将锁定前的状态码+1000，即得到具体的2xxx状态码。<br>
     * 如：1002 + 1000 = 2002，表示是从1002（正常）状态进入到2002（锁定）状态。
     */
    LOCKED(2000, "已锁定"),
    ;

    /**
     * 用于帮助计算锁定、解锁状态码的变量，1xxx状态码 + 1000 后得到锁定后的状态码，2xxx状态码 - 1000 后得到解锁后的状态码。
     */
    private static final int LOCK_UNLOCK_HELPER = 1000;

    /**
     * 该Map的Key是待审核状态码，Value是一个列表，该列表的索引0上是该Key对应的待审核状态、索引1上是其审核通过后对应的状态、索引2上是其审核拒绝后对应的状态。
     */
    private static final Map<Integer, List<UserStatus>> TO_BE_CHECKED_MAP = new HashMap<>(16);
    private static final int TO_BE_CHECKED_INDEX = 0;
    private static final int PASS_CHECK_INDEX = 1;
    private static final int REJECT_CHECK_INDEX = 2;

    static {
        TO_BE_CHECKED_MAP.put(REGISTER_TO_BE_CHECKED.getCode(), Arrays.asList(REGISTER_TO_BE_CHECKED, NORMAL, REGISTER_CHECK_FAILED));
        TO_BE_CHECKED_MAP.put(UPDATE_TO_BE_CHECKED.getCode(), Arrays.asList(UPDATE_TO_BE_CHECKED, TO_BE_UPDATED, NORMAL));
        TO_BE_CHECKED_MAP.put(CANCEL_TO_BE_CHECKED.getCode(), Arrays.asList(CANCEL_TO_BE_CHECKED, TO_BE_CANCLED, NORMAL));
    }

    private final int code;
    private final String desc;

    UserStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    public static UserStatus valueOf(int code) {
        UserStatus status = null;
        for (UserStatus value : UserStatus.values()) {
            if (value.getCode() == code) {
                status = value;
                break;
            }
        }
        status = isLockedStatus(code) ? LOCKED : status;
        return status;
    }

    /**
     * 根据code获取国际化描述
     */
    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        UserStatus userStatus = valueOf(code);
        return Objects.nonNull(userStatus) ? userStatus.getDesc() : getBaseCoreI18nMessage(COMMON_UNKNOWN);
    }

    /**
     * 获取待审核状态码集合
     */
    public static Set<Integer> getToBeCheckedStatusSet() {
        return TO_BE_CHECKED_MAP.keySet();
    }

    /**
     * 判断当前状态是否为待审核相关状态
     */
    public static boolean isToBeChecked(int code) {
        return TO_BE_CHECKED_MAP.get(code) != null;
    }

    /**
     * 获取审核通过后对应的状态，如果入参的状态不为待审核相关状态则抛出异常
     */
    public static UserStatus passCheck(int code) {
        List<UserStatus> statusList = TO_BE_CHECKED_MAP.get(code);
        CheckUtils.notNull(statusList, BaseValidationError.USER_STATUS_ERROR.toException(THE_CURRENT_STATUS_IS_NOT_PENDING_APPROVAL_I18N_KEY));
        return statusList.get(PASS_CHECK_INDEX);
    }

    /**
     * 获取审核拒绝后对应的状态，如果入参的状态不为待审核相关状态则抛出异常
     */
    public static UserStatus rejectCheck(int code) {
        List<UserStatus> statusList = TO_BE_CHECKED_MAP.get(code);
        CheckUtils.notNull(statusList, BaseValidationError.USER_STATUS_ERROR.toException(THE_CURRENT_STATUS_IS_NOT_PENDING_APPROVAL_I18N_KEY));
        return statusList.get(REJECT_CHECK_INDEX);
    }

    /**
     * 判断当前状态是否为锁定状态
     */
    public static boolean isLockedStatus(int code) {
        // 2000 < code < 3000
        return code > LOCKED.getCode() && code < LOCKED.getCode() + LOCK_UNLOCK_HELPER;
    }

    /**
     * 获取当前状态锁定后对应的状态码
     */
    public static int lockAccount(int code) {
        CheckUtils.isTrue(isUnlockedStatusValid(code), BaseValidationError.USER_STATUS_ERROR.toException(ACCOUNT_LOCKING_FAILED_UNKNOWN_USER_STATUS_CODE_I18N_KEY, code));
        return toLockedStatus(code);
    }

    /**
     * 获取当前状态解锁后对应的状态码
     */
    public static int unlockAccount(int code) {
        CheckUtils.isTrue(isLockedStatus(code), BaseValidationError.USER_STATUS_ERROR.toException(THE_CURRENT_STATE_IS_NOT_LOCKED_I18N_KEY));
        int unlockedStatus = toUnlockedStatus(code);
        CheckUtils.isTrue(isUnlockedStatusValid(unlockedStatus), BaseValidationError.USER_STATUS_ERROR.toException(UNLOCKING_ACCOUNT_FAILED_UNKNOWN_USER_STATUS_CODE_I18N_KEY, code));
        return unlockedStatus;
    }

    /**
     * 判断是否为合法的未锁定状态
     */
    private static boolean isUnlockedStatusValid(int unlockedStatus) {
        // 在(1000,2000)区间内并且是已定义的状态码则认为是合法的未锁定状态
        return unlockedStatus > LOCK_UNLOCK_HELPER
                && unlockedStatus < LOCKED.getCode()
                && Objects.nonNull(valueOf(unlockedStatus));
    }

    private static int toLockedStatus(int unlockedStatus) {
        // 锁定前状态码 + 1000 = 锁定后状态码
        return unlockedStatus + LOCK_UNLOCK_HELPER;
    }

    private static int toUnlockedStatus(int lockedStatus) {
        // 锁定后状态码 - 1000 = 锁定前状态码
        return lockedStatus - LOCK_UNLOCK_HELPER;
    }

    /**
     * 判断当前用户状态是否允许进行登录
     */
    public static boolean allowToLogin(int code) {
        return NORMAL.getCode() == code
                || TO_BE_UPDATED.getCode() == code
                || UPDATE_TO_BE_CHECKED.getCode() == code
                || CANCEL_TO_BE_CHECKED.getCode() == code
                || TO_BE_CANCLED.getCode() == code;
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
