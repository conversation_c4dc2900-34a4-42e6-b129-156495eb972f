package kl.npki.base.core.constant;

import kl.nbase.auth.core.AuthnType;
import kl.nbase.auth.sso.ISsoAuthSpi;
import kl.nbase.auth.utils.SsoAuthUtils;
import kl.nbase.i18n.i18n.EnumI18n;
import kl.npki.base.core.biz.login.model.LoginTypeInfo;
import kl.npki.base.core.exception.BaseValidationError;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 登录类型
 *
 * <AUTHOR>
 * @date 2023/4/12
 */
public enum LoginType implements Serializable, EnumI18n {

    /**
     * 测试模式
     * 密码口令登录
     */
    PWD("pwd", "用户名密码登录", BaseConstant.TEST_LOGIN_MODE, Collections.singletonList(AuthnType.PWD)),

    /**
     * 密评模式
     */
    SIGN("sign", "证书签名登录", BaseConstant.SECURITY_LOGIN_MODE, Collections.singletonList(AuthnType.CERT_SIGN)),

    SIGN_PWD("sign_pwd", "证书签名和密码登录", BaseConstant.SECURITY_LOGIN_MODE, Arrays.asList(AuthnType.PWD, AuthnType.CERT_SIGN)),

    /**
     * SSO（单点登录）认证方式。
     * 通过实现接口 {@code kl.nbase.auth.identifier.ISsoAuthSpi}，即可扩展支持特定平台的单点登录集成。
     *
     * 系统默认内置以下两种单点登录模式：
     * <li>密码服务平台单点登录：打包时默认支持。</li>
     * <li>工商银行单点登录：打包时默认不支持。需单独引入实现该功能的 SPI Jar 包，并放置于 lib 目录，系统即可自动识别并启用。</li>
     */
    SSO("sso", "SSO单点登录", BaseConstant.SECURITY_LOGIN_MODE, Collections.singletonList(AuthnType.SSO)),

    /**
     * 安审模式
     */
    MULTI_SIGN_2_OF_3("multi_sign_2_of_3", "3选2证书签名登录", BaseConstant.AUDIT_LOGIN_MODE, Collections.singletonList(AuthnType.CERT_SIGN)),

    MULTI_SIGN_3_OF_5("multi_sign_3_of_5", "5选3证书签名登录", BaseConstant.AUDIT_LOGIN_MODE, Collections.singletonList(AuthnType.CERT_SIGN)),

    ;

    private String type;

    private String desc;

    private String mode;
    // 登录类型对应的认证方式
    private Set<AuthnType> authnTypes;

    LoginType(String type, String desc, String mode, Collection<AuthnType> authnTypes) {
        this.type = type;
        this.desc = desc;
        this.mode = mode;
        this.authnTypes = new HashSet<>(authnTypes);
    }

    /**
     * 当前的登录方式是否需要证书
     *
     * @return boolean
     */
    public boolean needCertificate() {
        return this != PWD && this != SSO;
    }

    public static LoginType getLoginTypeByType(String type) {
        LoginType[] loginTypes = LoginType.values();
        LoginType loginType = null;
        for (LoginType value : loginTypes) {
            if (value.type.equalsIgnoreCase(type)) {
                loginType = value;
                break;
            }
        }
        if (type.toLowerCase().startsWith(SSO.getType())) {
            loginType = SSO;
        }
        return loginType;
    }

    public static List<LoginTypeInfo> valuesList() {
        List<LoginTypeInfo> loginTypeInfoList = new ArrayList<>();
        LoginType[] loginTypes = LoginType.values();
        for (LoginType value : loginTypes) {
            if (SSO.equals(value)) {
                // 单点登录是由具体平台实现的，不在这里添加
                continue;
            }

            LoginTypeInfo loginTypeInfo = new LoginTypeInfo(value);
            loginTypeInfoList.add(loginTypeInfo);
        }


        // 添加单点登录实现
        List<ISsoAuthSpi> platformsList = SsoAuthUtils.getAllSupportedSsoPlatformsList();
        platformsList.forEach(iSsoAuthSpi -> {
            LoginTypeInfo loginTypeInfo = new LoginTypeInfo()
                    .setType(iSsoAuthSpi.getType())
                    .setDesc(SSO.getDesc() + "-" + iSsoAuthSpi.platformI18nName())
                    .setMode(SSO.getMode());
            loginTypeInfoList.add(loginTypeInfo);
        });
        return loginTypeInfoList;
    }

    /**
     * 根据传入的登录类型标识查找系统支持的标准类型。
     *
     * <p>
     * 支持的类型来源包括：
     * <li>{@link LoginType} 枚举类型</li>
     * <li>{@link ISsoAuthSpi} 注册的扩展单点登录平台</li>
     * </p>
     *
     * <p>
     * 若匹配成功，则返回系统标准化的登录类型（为了用于内部逻辑统一）。<br>
     * 若无匹配项，则抛出异常。
     * </p>
     *
     * @param loginType 登录类型标识（大小写不敏感）
     * @return 系统支持的标准登录类型（如 {@code "pwd"、"sso_kcsp"、"sso_icbc"}）
     * @throws BaseValidationError 如果类型为空或不支持
     */
    public static String valueOfLoginType(String loginType) {
        if (StringUtils.isBlank(loginType)) {
            throw BaseValidationError.LOGIN_TYPE_NULL_ERROR.toException();
        }

        // 枚举匹配
        for (LoginType type : LoginType.values()) {
            if (type.getType().equalsIgnoreCase(loginType)) {
                return type.getType();
            }
        }

        // SPI 扩展平台匹配
        for (ISsoAuthSpi spi : SsoAuthUtils.getAllSupportedSsoPlatformsList()) {
            if (spi.getType().equalsIgnoreCase(loginType)) {
                return spi.getType();
            }
        }

        throw BaseValidationError.LOGIN_TYPE_NOT_SUPPORT_ERROR.toException(loginType);
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return tr();
    }

    public String getMode() {
        return mode;
    }

    public Set<AuthnType> getAuthnTypes() {
        return authnTypes;
    }

    @Override
    public String toString() {
        return "LoginType{" +
            "type='" + type + '\'' +
            ", desc='" + desc + '\'' +
            ", mode='" + mode + '\'' +
            ", authnTypes=" + authnTypes +
            "} " + super.toString();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}

