package kl.npki.base.core.annotation;

import kl.npki.base.core.biz.dataprotect.service.ICustomGenOriDataService;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 数据完整性保护注解
 * 需要自定义完整性值生成时，可用该注解
 * <AUTHOR>
 */
@Target(TYPE)
@Retention(RUNTIME)
public @interface DataProtect {

    /**
     * 自定义生成完整性值的服务lei
     * @return
     */
    Class<? extends ICustomGenOriDataService> genOriDataClass();
}
