package kl.npki.base.core.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import kl.npki.base.core.validator.Base64ValueValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Base64格式校验注解
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE, ElementType.TYPE_USE})
@Retention(RUNTIME)
@Constraint(validatedBy = Base64ValueValidator.class)
@Documented
public @interface Base64ValueConstraint {
    String message() default "{kl.npki.base.core.annotation.Base64ValueConstraint.message}";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
