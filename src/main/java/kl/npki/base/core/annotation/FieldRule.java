package kl.npki.base.core.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import kl.npki.base.core.validator.FieldRuleValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 根据配置规则，对字段进行校验
 *
 * <AUTHOR> by niugang on 2025-07-02 15:10
 */
@Target({FIELD})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = FieldRuleValidator.class)
public @interface FieldRule {

    /**
     * 当前校验字段的描述信息
     *
     * @return String
     */
    String description() default "";

    /**
     * 验证失败时返回的错误信息。
     *
     * @return 错误提示信息
     */
    String message() default FieldRuleValidator.KL_NPKI_BASE_CORE_ANNOTATION_FIELD_RULE_MESSAGE;

    /**
     * 分组校验的分组标识。
     *
     * @return 分组数组
     */
    Class<?>[] groups() default {};

    /**
     * 校验的负载信息，用于携带额外数据。
     *
     * @return Payload 数组
     */
    Class<? extends Payload>[] payload() default {};
}
