package kl.npki.base.core.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import kl.npki.base.core.constant.ValueOfDefaultEnum;
import kl.npki.base.core.validator.ValueOfEnumValidator;


import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验字段是枚举类型
 *
 * <AUTHOR> by niugang on 2025-06-05 15:10
 */
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = ValueOfEnumValidator.class)
public @interface ValueOfEnum {

    /**
     * 枚举类类型，必须为 Enum 的子类。
     *
     * @return 枚举类的 Class 对象
     */
    Class<? extends Enum<?>> enumClass() default ValueOfDefaultEnum.class;

    /**
     * 验证失败时返回的错误信息。
     *
     * @return 错误提示信息
     */
    String message() default ValueOfEnumValidator.KL_NPKI_BASE_CORE_ANNOTATION_VALUE_OF_ENUM_MESSAGE;

    /**
     * 分组校验的分组标识。
     *
     * @return 分组数组
     */
    Class<?>[] groups() default {};

    /**
     * 校验的负载信息，用于携带额外数据。
     *
     * @return Payload 数组
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 也可以指定一个数据集合
     * @return 枚举值
     */
    String[]  values() default {};
}
