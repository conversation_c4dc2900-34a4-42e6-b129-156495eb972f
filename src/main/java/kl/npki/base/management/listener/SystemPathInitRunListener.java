package kl.npki.base.management.listener;

import kl.nbase.config.constant.ConfigConstantsEnum;
import kl.nbase.exception.context.ExceptionContext;
import kl.npki.base.management.utils.SubsystemUtil;
import org.h2.engine.SysProperties;
import org.springframework.boot.ConfigurableBootstrapContext;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringApplicationRunListener;

import static kl.npki.base.core.constant.BaseConstant.APP_ROOT;
import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;

/**
 * 系统路径初始化监听器，适用于系统三种模式启动服务：war、zip、idea
 *
 * <AUTHOR>
 * @create 2025/5/29 上午10:20
 */
public class SystemPathInitRunListener implements SpringApplicationRunListener {

    private static final String CONFIG_LOCATION_PROPERTY = "spring.config.location";
    private static final String KOAL_KEYSTORE_PATH = "koal.keystore.path";
    private static final String USER_DIR = "user.dir";
    private static final String LOG_DIR = "logs.dir";

    public SystemPathInitRunListener(SpringApplication application, String[] args) {
    }

    @Override
    public void starting(ConfigurableBootstrapContext bootstrapContext) {
        // 初始化路径相关配置
        pathInit();
        // 初始化异常上下文
        ExceptionContext.getInstance().init(SubsystemUtil.getSystemCode(), SubsystemUtil.getServiceCode());
    }


    private static void pathInit() {
        // 设置h2根目录
        SysProperties.setBaseDir(WEB_ROOT);
        // 设置keyStore路径
        System.setProperty(KOAL_KEYSTORE_PATH, WEB_ROOT);
        // 设置user.dir路径
        System.setProperty(USER_DIR, WEB_ROOT);
        // 设置日志目录路径
        System.setProperty(LOG_DIR, APP_ROOT);
        // 设置配置文件路径
        System.setProperty(ConfigConstantsEnum.CONFIG_FILE_LOCATION.getKey(), WEB_ROOT + "/config/");
        System.setProperty(CONFIG_LOCATION_PROPERTY, WEB_ROOT + "/config/");
    }
}