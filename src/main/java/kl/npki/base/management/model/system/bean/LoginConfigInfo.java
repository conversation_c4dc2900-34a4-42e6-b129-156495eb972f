package kl.npki.base.management.model.system.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.configs.LoginConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import org.hibernate.validator.constraints.Range;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.LOGIN_TOKEN_LIFECYCLE_RANGE_MESSAGE_I18N_KEY;

/**
 * 登录配置信息，来源于{@link LoginConfig}
 *
 * <AUTHOR>
 * @date 2025/7/17 17:25
 **/
@Schema(description = "登录配置信息")
public class LoginConfigInfo {

    /**
     * token生命周期，单位ms
     */
    @Schema(description = "登录Token有效期（毫秒）")
    @Range(min = 10000, max = 86400000, message = LOGIN_TOKEN_LIFECYCLE_RANGE_MESSAGE_I18N_KEY)
    private long accessTokenLifecycle;

    public static LoginConfigInfo defaultInstance() {
        LoginConfig loginConfig = BaseConfigWrapper.getLoginConfig();

        LoginConfigInfo loginConfigInfo = new LoginConfigInfo();
        loginConfigInfo.setAccessTokenLifecycle(loginConfig.getAccessTokenLifecycle());
        return loginConfigInfo;
    }

    public long getAccessTokenLifecycle() {
        return accessTokenLifecycle;
    }

    public void setAccessTokenLifecycle(long accessTokenLifecycle) {
        this.accessTokenLifecycle = accessTokenLifecycle;
    }
}
