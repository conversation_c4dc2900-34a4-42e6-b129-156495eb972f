package kl.npki.base.management.model.admin.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
public class AdminInfoListResponse {

    /**
     * 用户ID
     */
    @Schema(description = "管理员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long adminInfoId;

    /**
     * 用户角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 角色编码
     */
    @Schema(description = "角色编码")
    private String roleCode;

    /**
     * 人员名称
     */
    @Schema(description = "管理员名称（证书CN）")
    private String username;

    /**
     * 省
     */
    @Schema(description = "省")
    private String province;

    /**
     * 市
     */
    @Schema(description = "市")
    private String city;

    /**
     * 组织
     */
    @Schema(description = "组织")
    private String organization;

    /**
     * 机构
     */
    @Schema(description = "机构")
    private String organizationUnit;

    @Schema(description = "所属机构ID")
    private Long orgId;

    @Schema(description = "所属机构名称")
    private String orgName;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 颁发者名称
     */
    @Schema(description = "颁发者名称")
    private String issuerCn;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 审核状态
     */
    @Schema(description = "管理员审核状态")
    private Integer status;

    @Schema(description = "管理员审核状态国际化描述")
    private String statusDesc;

    /**
     * 证书状态
     */
    @Schema(description = "证书状态")
    private Integer certStatus;

    @Schema(description = "证书状态国际化描述")
    private String certStatusDesc;

    /**
     * 是否为导入管理员
     */
    @Schema(description = "是否为导入管理员")
    private Boolean imported;

    /**
     * 证书有效期--起始
     */
    @Schema(description = "证书有效期--起始")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime validStart;

    /**
     * 证书有效期--结束
     */
    @Schema(description = "证书有效期--结束")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime validEnd;

    /**
     * 证书序列号
     */
    @Schema(description = "签名证书序列号")
    private String signCertSn;

    @Schema(description = "加密证书序列号")
    private String encCertSn;

    /**
     * 账号被锁定时的时间
     */
    @Schema(description = "账号被锁定时的时间")
    private LocalDateTime lockTime;

    public Long getAdminInfoId() {
        return adminInfoId;
    }

    public void setAdminInfoId(Long adminInfoId) {
        this.adminInfoId = adminInfoId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getOrganizationUnit() {
        return organizationUnit;
    }

    public void setOrganizationUnit(String organizationUnit) {
        this.organizationUnit = organizationUnit;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIssuerCn() {
        return issuerCn;
    }

    public void setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Integer getCertStatus() {
        return certStatus;
    }

    public void setCertStatus(Integer certStatus) {
        this.certStatus = certStatus;
    }

    public String getCertStatusDesc() {
        return certStatusDesc;
    }

    public void setCertStatusDesc(String certStatusDesc) {
        this.certStatusDesc = certStatusDesc;
    }

    public Boolean getImported() {
        return imported;
    }

    public void setImported(Boolean imported) {
        this.imported = imported;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public String getSignCertSn() {
        return signCertSn;
    }

    public void setSignCertSn(String signCertSn) {
        this.signCertSn = signCertSn;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public LocalDateTime getLockTime() {
        return lockTime;
    }

    public void setLockTime(LocalDateTime lockTime) {
        this.lockTime = lockTime;
    }
}

