package kl.npki.base.management.model.system.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kl.npki.base.management.model.system.bean.TransactionConfigInfo;
import kl.npki.base.management.model.system.bean.LogExportConfigInfo;
import kl.npki.base.management.model.system.bean.LoginConfigInfo;
import kl.npki.base.management.model.system.bean.SysConfigInfo;

/**
 * 通用系统配置请求
 *
 * <AUTHOR>
 * @Date 2023/10/16
 */
@Schema(description = "通用系统配置请求")
public class SysConfigRequest {

    /**
     * 系统配置信息
     */
    @Schema(description = "系统基本配置")
    @Valid
    private SysConfigInfo sysConfigInfo;

    /**
     * 日志导出配置信息
     */
    @Schema(description = "日志导出配置")
    @Valid
    private LogExportConfigInfo logExportConfigInfo;

    /**
     * 数据库事务配置信息
     */
    @Schema(description = "数据库事务配置")
    @Valid
    private TransactionConfigInfo transactionConfigInfo;

    /**
     * 登录配置信息
     */
    @Schema(description = "登录配置")
    @Valid
    private LoginConfigInfo loginConfigInfo;

    public SysConfigInfo getSysConfigInfo() {
        return sysConfigInfo;
    }

    public void setSysConfigInfo(SysConfigInfo sysConfigInfo) {
        this.sysConfigInfo = sysConfigInfo;
    }

    public LogExportConfigInfo getLogExportConfigInfo() {
        return logExportConfigInfo;
    }

    public void setLogExportConfigInfo(LogExportConfigInfo logExportConfigInfo) {
        this.logExportConfigInfo = logExportConfigInfo;
    }

    public TransactionConfigInfo getDbTransactionConfigInfo() {
        return transactionConfigInfo;
    }

    public void setDbTransactionConfigInfo(TransactionConfigInfo transactionConfigInfo) {
        this.transactionConfigInfo = transactionConfigInfo;
    }

    public LoginConfigInfo getLoginConfigInfo() {
        return loginConfigInfo;
    }

    public void setLoginConfigInfo(LoginConfigInfo loginConfigInfo) {
        this.loginConfigInfo = loginConfigInfo;
    }
}
