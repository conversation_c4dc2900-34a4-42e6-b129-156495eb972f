package kl.npki.base.management.model.system.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.configs.LogConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import org.hibernate.validator.constraints.Range;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.LOG_FILE_EXCEL_LIMIT_SIZE_RANGE_MESSAGE_I18N_KEY;
import static kl.npki.base.management.constant.I18nParameterVerifyConstant.LOG_FILE_LIMIT_SIZE_RANGE_MESSAGE_I18N_KEY;

/**
 * 日志导出配置，来源于{@link LogConfig}
 *
 * <AUTHOR>
 * @date 2025/7/17 16:48
 **/
@Schema(description = "日志导出配置")
public class LogExportConfigInfo {

    /**
     * 日志文件数量限制: 超过限制时，数据库将删除旧的日志文件
     */
    @Schema(description = "历史文件最大数量")
    @Range(min = 10, max = 100, message = LOG_FILE_LIMIT_SIZE_RANGE_MESSAGE_I18N_KEY)
    private int logFileLimitSize;

    /**
     * excel文件行数限制: 超过限制时将生成新的excel
     */
    @Schema(description = "单个文件最大行数")
    @Range(min = 10, max = 1000000, message = LOG_FILE_EXCEL_LIMIT_SIZE_RANGE_MESSAGE_I18N_KEY)
    private int logFileExcelLimitSize;

    public static LogExportConfigInfo defaultInstance() {
        LogConfig logConfig = BaseConfigWrapper.getLogConfig();

        LogExportConfigInfo logExportConfigInfo = new LogExportConfigInfo();
        logExportConfigInfo.setLogFileLimitSize(logConfig.getLogFileLimitSize());
        logExportConfigInfo.setLogFileExcelLimitSize(logConfig.getLogFileExcelLimitSize());
        return logExportConfigInfo;
    }

    public int getLogFileLimitSize() {
        return logFileLimitSize;
    }

    public void setLogFileLimitSize(int logFileLimitSize) {
        this.logFileLimitSize = logFileLimitSize;
    }

    public int getLogFileExcelLimitSize() {
        return logFileExcelLimitSize;
    }

    public void setLogFileExcelLimitSize(int logFileExcelLimitSize) {
        this.logFileExcelLimitSize = logFileExcelLimitSize;
    }
}
