package kl.npki.base.management.model.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.biz.batch.model.BatchErrorInfo;
import kl.npki.base.core.biz.batch.model.BatchSuccessInfo;

import java.util.List;

/**
 * 批量操作响应
 *
 * <AUTHOR> rs
 * @date 2024/3/25 9:58
 */
public class BatchResponse {

    @Schema(description = "操作")
    private String operationName;

    @Schema(description = "成功数量")
    private Integer successCount;

    @Schema(description = "失败数量")
    private Integer errorCount;

    @Schema(description = "错误原因")
    private List<BatchErrorInfo> batchErrorInfo;

    @Schema(description = "正常信息")
    List<BatchSuccessInfo> batchSuccessInfo;

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public List<BatchErrorInfo> getBatchErrorInfo() {
        return batchErrorInfo;
    }

    public void setBatchErrorInfo(List<BatchErrorInfo> batchErrorInfo) {
        this.batchErrorInfo = batchErrorInfo;
    }

    public List<BatchSuccessInfo> getBatchSuccessInfo() {
        return batchSuccessInfo;
    }

    public void setBatchSuccessInfo(List<BatchSuccessInfo> batchSuccessInfo) {
        this.batchSuccessInfo = batchSuccessInfo;
    }

}
