package kl.npki.base.management.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * ID集合请求参数
 *
 * <AUTHOR>
 * @date 08/05/2025 20:52
 **/
public class IdListRequest {

    /**
     * 待批量操作的ID列表
     */
    @Schema(description = "待批量操作的ID列表")
    private List<Long> ids;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
}
