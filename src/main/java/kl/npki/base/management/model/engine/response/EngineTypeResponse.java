package kl.npki.base.management.model.engine.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.management.core.biz.config.model.EngineTypeInfo;

import java.util.List;

/**
 * 加密机信息列表响应
 * <AUTHOR>
 */
public class EngineTypeResponse {
    @Schema(description = "加密机信息")
    private List<EngineTypeInfo> engineTypeInfoList;

    public EngineTypeResponse(List<EngineTypeInfo> engineTypeInfoList) {
        this.engineTypeInfoList = engineTypeInfoList;
    }

    public List<EngineTypeInfo> getEngineTypeInfoList() {
        return engineTypeInfoList;
    }

    public EngineTypeResponse setEngineTypeInfoList(List<EngineTypeInfo> engineTypeInfoList) {
        this.engineTypeInfoList = engineTypeInfoList;
        return this;
    }
}
