package kl.npki.base.management.model.system.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.management.model.system.bean.TransactionConfigInfo;
import kl.npki.base.management.model.system.bean.LogExportConfigInfo;
import kl.npki.base.management.model.system.bean.LoginConfigInfo;
import kl.npki.base.management.model.system.bean.SysConfigInfo;

/**
 * 系统通用配置响应
 *
 * <AUTHOR>
 * @Date 2023/10/16
 */
@Schema(description = "系统通用配置响应")
public class SysConfigResponse {

    /**
     * 系统配置信息
     */
    @Schema(description = "系统基本配置")
    private SysConfigInfo sysConfigInfo;

    /**
     * 日志导出配置信息
     */
    @Schema(description = "日志导出配置")
    private LogExportConfigInfo logExportConfigInfo;

    /**
     * 数据库事务配置信息
     */
    @Schema(description = "数据库事务配置")
    private TransactionConfigInfo transactionConfigInfo;

    /**
     * 登录配置信息
     */
    @Schema(description = "登录配置")
    private LoginConfigInfo loginConfigInfo;

    public static SysConfigResponse defaultInstance() {

        SysConfigInfo sysConfigInfo = SysConfigInfo.defaultInstance();
        LogExportConfigInfo logExportConfigInfo = LogExportConfigInfo.defaultInstance();
        TransactionConfigInfo transactionConfigInfo = TransactionConfigInfo.defaultInstance();
        LoginConfigInfo loginConfigInfo = LoginConfigInfo.defaultInstance();

        SysConfigResponse sysConfigResponse = new SysConfigResponse();
        sysConfigResponse.setSysConfigInfo(sysConfigInfo);
        sysConfigResponse.setLogExportConfigInfo(logExportConfigInfo);
        sysConfigResponse.setDbTransactionConfigInfo(transactionConfigInfo);
        sysConfigResponse.setLoginConfigInfo(loginConfigInfo);

        return sysConfigResponse;
    }

    public SysConfigInfo getSysConfigInfo() {
        return sysConfigInfo;
    }

    public void setSysConfigInfo(SysConfigInfo sysConfigInfo) {
        this.sysConfigInfo = sysConfigInfo;
    }

    public LogExportConfigInfo getLogExportConfigInfo() {
        return logExportConfigInfo;
    }

    public void setLogExportConfigInfo(LogExportConfigInfo logExportConfigInfo) {
        this.logExportConfigInfo = logExportConfigInfo;
    }

    public TransactionConfigInfo getDbTransactionConfigInfo() {
        return transactionConfigInfo;
    }

    public void setDbTransactionConfigInfo(TransactionConfigInfo transactionConfigInfo) {
        this.transactionConfigInfo = transactionConfigInfo;
    }

    public LoginConfigInfo getLoginConfigInfo() {
        return loginConfigInfo;
    }

    public void setLoginConfigInfo(LoginConfigInfo loginConfigInfo) {
        this.loginConfigInfo = loginConfigInfo;
    }
}
