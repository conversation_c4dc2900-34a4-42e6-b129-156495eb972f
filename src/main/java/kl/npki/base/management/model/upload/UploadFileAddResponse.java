package kl.npki.base.management.model.upload;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午7:34
 */
public class UploadFileAddResponse {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "文件标识")
    private final Long id;

    public UploadFileAddResponse(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
}