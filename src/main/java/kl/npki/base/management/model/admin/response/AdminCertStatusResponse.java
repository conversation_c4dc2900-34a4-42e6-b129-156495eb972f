package kl.npki.base.management.model.admin.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.constant.CertStatus;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 管理员证书状态
 *
 * <AUTHOR>
 * @date 12/05/2025 10:55
 **/
@Schema(description = "管理员证书状态")
public class AdminCertStatusResponse {

    /**
     * 管理员证书状态
     */
    @Schema(description = "管理员证书状态，key为状态码，value为国际化描述")
    private Map<Integer, String> status;

    public AdminCertStatusResponse(Map<Integer, String> status) {
        this.status = status;
    }

    public Map<Integer, String> getStatus() {
        return status;
    }

    public void setStatus(Map<Integer, String> status) {
        this.status = status;
    }

    public static AdminCertStatusResponse instance() {
        Map<Integer, String> statusMap = new LinkedHashMap<>(CertStatus.values().length);
        Arrays.stream(CertStatus.values()).forEach(v -> statusMap.put(v.getCode(), v.getDesc()));
        return new AdminCertStatusResponse(statusMap);
    }
}
