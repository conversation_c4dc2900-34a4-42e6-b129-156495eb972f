package kl.npki.base.management.model.upload;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午7:34
 */
public class UploadFileAddRequest {
    @NotNull
    @Schema(description = "上传文件用途（接口名称）")
    private String interfaceName;

    @NotNull
    @Schema(description = "文件")
    private MultipartFile file;

    public String getInterfaceName() {
        return interfaceName;
    }

    public UploadFileAddRequest setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
        return this;
    }

    public MultipartFile getFile() {
        return file;
    }

    public UploadFileAddRequest setFile(MultipartFile file) {
        this.file = file;
        return this;
    }
}