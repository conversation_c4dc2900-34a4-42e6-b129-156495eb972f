package kl.npki.base.management.model.system.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kl.npki.base.core.configs.SysInfoConfig;

import java.io.Serializable;
import java.util.Objects;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * 修改系统信息请求体
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
public class SysManagerRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = THE_SYSTEM_NAME_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_SYSTEM_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_SYSTEM_NAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "系统名称")
    private String name;

    @Email(message = EMAIL_ERROR_I18N_KEY)
    @Schema(description = "电子邮件")
    private String email;

    /**
     * 系统logo约定以base64格式传过来，最终形式如："data:image/jpg;base64,xxxxxxxxxxxx"
     */
    @Pattern(regexp = "^$|^data:image/(jpg|jpeg|png|svg\\+xml);base64,([A-Za-z0-9+/=]{4})*$", message = THE_SYSTEM_LOGO_FORMAT_IS_INCORRECT_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "系统LOGO")
    private String logo;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public SysInfoConfig updateSysInfoConfig(SysInfoConfig sysInfoConfig) {
        if (Objects.isNull(sysInfoConfig)) {
            sysInfoConfig = new SysInfoConfig();
        }
        sysInfoConfig.setName(this.getName());
        sysInfoConfig.setEmail(this.getEmail());
        sysInfoConfig.setLogo(this.getLogo());
        return sysInfoConfig;
    }
}
