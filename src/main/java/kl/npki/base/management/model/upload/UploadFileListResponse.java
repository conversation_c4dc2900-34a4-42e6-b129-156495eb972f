package kl.npki.base.management.model.upload;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午7:34
 */
public class UploadFileListResponse {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "文件标识")
    private Long id;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "处理状态")
    private Integer processStatus;

    @Schema(description = "总计条数")
    private Integer totalCount;

    @Schema(description = "已经处理条数")
    private Integer processCount;

    @Schema(description = "处理成功条数")
    private Integer processSuccessCount;

    @Schema(description = "处理失败条数")
    private Integer processErrorCount;

    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @Schema(description = "添加时间")
    private LocalDateTime createdAt;

    public Long getId() {
        return id;
    }

    public String getFileName() {
        return fileName;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public UploadFileListResponse setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public UploadFileListResponse setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public UploadFileListResponse setProcessCount(Integer processCount) {
        this.processCount = processCount;
        return this;
    }

    public Integer getProcessSuccessCount() {
        return processSuccessCount;
    }

    public UploadFileListResponse setProcessSuccessCount(Integer processSuccessCount) {
        this.processSuccessCount = processSuccessCount;
        return this;
    }

    public Integer getProcessErrorCount() {
        return processErrorCount;
    }

    public UploadFileListResponse setProcessErrorCount(Integer processErrorCount) {
        this.processErrorCount = processErrorCount;
        return this;
    }

    public UploadFileListResponse setId(Long id) {
        this.id = id;
        return this;
    }

    public UploadFileListResponse setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public UploadFileListResponse setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }
}