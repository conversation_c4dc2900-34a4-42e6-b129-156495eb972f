package kl.npki.base.management.model.cache;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kl.npki.management.core.biz.config.model.AddressInfo;

import java.io.Serializable;
import java.util.List;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * @Author: guoq
 * @Date: 2024/1/30
 * @description:
 */
public class CacheConfigRequest implements Serializable {

    private static final long serialVersionUID = 7952321426951820402L;

    @Schema(description = "是否启用")
    private boolean enabled = true;

    @NotBlank(message = CACHE_TYPE_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 20, message = THE_LENGTH_OF_THE_DATABASE_TYPE_CANNOT_EXCEED_20_CHARACTERS_I18N_KEY)
    @Schema(description = "缓存类型", allowableValues = {"redis", "caffeine"})
    private String type;

    @NotBlank(message = CACHE_OPERATION_MODE_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 20, message = CACHE_OPERATION_MODE_CANNOT_EXCEED_20_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = CACHE_OPERATION_MODE_CANNOT_USE_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags =
        Pattern.Flag.DOTALL)
    @Schema(description = "缓存运行模式", allowableValues = {"single", "cluster", "sentinel"})
    private String runMode;
    @NotNull(message = SSL_SWITCH_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "启用SSL")
    private boolean ssl;

    /**
     * 用户名，可选
     */
    @Schema(description = "用户名称")
    private String username;

    /**
     * 访问密码，可选
     */
    @Schema(description = "密码")
    private String password;

    /**
     * 连接超时时间，单位毫秒
     */
    @Schema(description = "连接超时时间")
    private Integer connectTimeoutMills = 10000;

    /**
     * 响应读取超时时间，单位毫秒
     */
    @Schema(description = "读取超时时间")
    private Integer readTimeoutMills = 3000;

    @NotNull(message = REDIS_NODE_CONFIGURATION_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "redis节点配置")
    private List<AddressInfo> addressInfos;

    @Schema(description = "哨兵主节点名称")
    private String masterName;

    @Schema(description = "哨兵密码")
    private String sentinelPassword;

    public boolean isEnabled() {
        return enabled;
    }

    public CacheConfigRequest setEnabled(boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRunMode() {
        return runMode;
    }

    public void setRunMode(String runMode) {
        this.runMode = runMode;
    }

    public boolean isSsl() {
        return ssl;
    }

    public void setSsl(boolean ssl) {
        this.ssl = ssl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getConnectTimeoutMills() {
        return connectTimeoutMills;
    }

    public void setConnectTimeoutMills(Integer connectTimeoutMills) {
        this.connectTimeoutMills = connectTimeoutMills;
    }

    public Integer getReadTimeoutMills() {
        return readTimeoutMills;
    }

    public void setReadTimeoutMills(Integer readTimeoutMills) {
        this.readTimeoutMills = readTimeoutMills;
    }

    public List<AddressInfo> getAddressInfos() {
        return addressInfos;
    }

    public void setAddressInfos(List<AddressInfo> addressInfos) {
        this.addressInfos = addressInfos;
    }

    public String getMasterName() {
        return masterName;
    }

    public void setMasterName(String masterName) {
        this.masterName = masterName;
    }

    public String getSentinelPassword() {
        return sentinelPassword;
    }

    public void setSentinelPassword(String sentinelPassword) {
        this.sentinelPassword = sentinelPassword;
    }
}
