package kl.npki.base.management.model.admin.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.management.core.constants.ImportKeyEnum;

/**
 * 管理员证书签发响应
 * <AUTHOR>
 * @date 2023/2/21
 */
public class AdminCertIssueResponse {

    /**
     * 是否是双证
     */
    @Schema(description = "是否是双证书")
    private Boolean twinCert;

    /**
     * 签名证书值
     */
    @Schema(description = "签名证书值")
    private String signCertValue;

    /**
     * 加密证书值
     */
    @Schema(description = "加密证书值")
    private String encCertValue;

    /**
     * pfx类型的证书
     */
    @Schema(description = "pfx证书值")
    private String pfxCertValue;

    /**
     * 保护密钥类型
     */
    @Schema(description = "保护密钥类型")
    private String keyType;

    /**
     * 数字信封
     */
    @Schema(description = "数字信封")
    private String envelopData;

    /**
     * @see ImportKeyEnum
     * 加密证书导入key的模式
     */
    @Schema(description = "加密证书导入key里面的方式", example = "[1: 通过保护密钥对的方式进行导入, 2: 通过pfx类型的证书进行导入]")
    private Integer importModel;

    /**
     * base64的密钥（加密证书的私钥，需要用保护密钥对对其进行加密，加密证书的公钥存放在了加密证书里面）
     */
    @Schema(description = "base64的密钥")
    private String base64KeyValue;

    public Boolean getTwinCert() {
        return twinCert;
    }

    public void setTwinCert(Boolean twinCert) {
        this.twinCert = twinCert;
    }

    public String getSignCertValue() {
        return signCertValue;
    }

    public void setSignCertValue(String signCertValue) {
        this.signCertValue = signCertValue;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }

    public String getPfxCertValue() {
        return pfxCertValue;
    }

    public void setPfxCertValue(String pfxCertValue) {
        this.pfxCertValue = pfxCertValue;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getEnvelopData() {
        return envelopData;
    }

    public void setEnvelopData(String envelopData) {
        this.envelopData = envelopData;
    }

    public Integer getImportModel() {
        return importModel;
    }

    public void setImportModel(Integer importModel) {
        this.importModel = importModel;
    }

    public String getBase64KeyValue() {
        return base64KeyValue;
    }

    public void setBase64KeyValue(String base64KeyValue) {
        this.base64KeyValue = base64KeyValue;
    }
}
