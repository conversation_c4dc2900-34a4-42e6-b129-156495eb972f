package kl.npki.base.management.model.system.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.configs.TransactionConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import org.hibernate.validator.constraints.Range;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.TRANSACTION_TIMEOUT_SECONDS_RANGE_MESSAGE_I18N_KEY;

/**
 * 数据库事务配置，来源于{@link TransactionConfig}
 *
 * <AUTHOR>
 * @date 2025/7/17 17:16
 **/
@Schema(description = "数据库事务配置")
public class TransactionConfigInfo {

    /**
     * 事务超时时间(单位：秒)，-1,永不超时
     */
    @Schema(description = "事务超时时间(秒)")
    @Range(min = -1, max = 86400, message = TRANSACTION_TIMEOUT_SECONDS_RANGE_MESSAGE_I18N_KEY)
    private int timeoutSeconds;

    public static TransactionConfigInfo defaultInstance() {
        TransactionConfig transactionConfig = BaseConfigWrapper.getTransactionConfig();

        TransactionConfigInfo transactionConfigInfo = new TransactionConfigInfo();
        transactionConfigInfo.setTimeoutSeconds(transactionConfig.getTimeoutSeconds());
        return transactionConfigInfo;
    }

    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }

    public void setTimeoutSeconds(int timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
}
