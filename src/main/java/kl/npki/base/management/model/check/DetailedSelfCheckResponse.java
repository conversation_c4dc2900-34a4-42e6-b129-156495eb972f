package kl.npki.base.management.model.check;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 自检详细响应
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
public class DetailedSelfCheckResponse implements Serializable {

    /**
     * 自检项唯一标识
     */
    private String id;

    /**
     * 自检项编码
     */
    private String code;

    /**
     * 自检项名称
     */
    private String name;

    /**
     * 自检项分类
     */
    private String type;

    /**
     * 自检结果状态
     */
    private String status;

    /**
     * 检测时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime checkTime;

    /**
     * 检测失败的严重性
     */
    private String severity;

    /**
     * 简洁地描述自检的情况,可能只包含简单的错误消息或失败的总结
     */
    private String message;

    /**
     * 用于引导用户解决自检失败的字段，可以是前端页面URL或其他引导提示信息
     */
    private String guidance;

    /**
     * 检测的详细信息,提供了更详细的上下文信息,可以包含任何相关的详细信息，比如异常的堆栈跟踪、失败的详细说明、可能的解决方案
     */
    private Map<String, Object> details;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getSeverity() {
        return severity;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getGuidance() {
        return guidance;
    }

    public void setGuidance(String guidance) {
        this.guidance = guidance;
    }

    public Map<String, Object> getDetails() {
        return details;
    }

    public void setDetails(Map<String, Object> details) {
        this.details = details;
    }
}
