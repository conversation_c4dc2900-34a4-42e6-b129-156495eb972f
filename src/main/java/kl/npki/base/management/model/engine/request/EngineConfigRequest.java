package kl.npki.base.management.model.engine.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.configs.EmConfig;
import kl.npki.base.core.configs.SysSupportedAlgoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.constant.EmTypeEnum;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.service.util.EnvironmentUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * 加密机属性配置请求
 *
 * <AUTHOR>
 */
public class EngineConfigRequest implements Serializable {
    private static final String KEY_STORE_NAME = "keystore";
    private static final long serialVersionUID = 225571014625657833L;
    @Schema(description = "加密机名称")
    private String engineName;
    @NotBlank(message = THE_ENCRYPTION_MACHINE_TYPE_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_ENCRYPTION_MACHINE_TYPE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = ENCRYPTION_MACHINE_TYPE_CANNOT_USE_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags =
        Pattern.Flag.DOTALL)
    @Schema(description = "加密机类型")
    private String engineType;
    @Schema(description = "加密机ip")
    private String ip;
    @Schema(description = "加密机端口")
    private Integer port;
    @Schema(description = "路径")
    private String path;
    @Schema(description = "加密机用户名")
    private String username;
    @Schema(description = "加密机密码")
    private String engineCred;
    @Valid
    @NotEmpty(message = ASYMMETRIC_KEY_INDEX_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "非对称密钥索引信息")
    private List<AsymKeyIndexInfo> asymKeyIndexInfoList;
    @Schema(description = "默认密钥密码")
    private String defaultKeyCred;

    private static String buildIndexPair(int startIndex, int endIndex) {
        StringBuilder indexPair = new StringBuilder();
        for (int i = startIndex; i <= endIndex; i++) {
            indexPair.append(i);
            if (i != endIndex) {
                indexPair.append(BaseConstant.COMMA);
            }
        }
        return indexPair.toString();
    }

    public EmConfig toEmConfig(String defaultEngineName, boolean isBackupEngine) {
        EmConfig emConfig = new EmConfig();
        // 填充基本配置信息
        emConfig.setEngineType(this.engineType);

        // 获取当前系统状态： 演示环境， 部署中， 部署完成
        // 是否部署完成
        boolean deployed = SystemUtil.isDeployed();
        // 是否部署中
        boolean deploying = EnvironmentUtil.isDeploying();
        // 获取原来的密码机配置
        ClusterEmConfig clusterEmConfig = BaseConfigWrapper.getClusterEmConfig();

        if (!deployed && !deploying) {
            // 未部署状态保存时keystore为原来的配置中的keystore路径
            // 加密机名称也是使用原来的配置中的名称
            if (isBackupEngine) {
                emConfig.setPath(defaultEngineName + BaseConstant.UNDERSCORE + KEY_STORE_NAME);
                this.engineName = defaultEngineName;
            } else {
                emConfig.setPath(clusterEmConfig.getGroupEngine().getEngines().get(0).getPath());
                this.engineName = clusterEmConfig.getGroupEngine().getEngines().get(0).getEngineName();
            }
            // 非文件密码机还是使用之前的命名规则
            if (!this.engineType.equals(EmTypeEnum.FILE_ENGINE.getEngineType())) {
                this.engineName = defaultEngineName;
            }
        }
        if (deployed || deploying) {
            // 部署中或部署完成后按照规则填充keystore路径
            // 填充keystore路径配置
            if (StringUtils.isBlank(this.path) && EmTypeEnum.isFileEngine(engineType)) {
                // keystore路径, 原来的逻辑
                emConfig.setPath(defaultEngineName + BaseConstant.UNDERSCORE + KEY_STORE_NAME);
            } else {
                // 驱动路径，为空时，加密机会使用对应默认的路径
                emConfig.setPath(this.path);
            }
            // 加密机名称为空则设置为默认分配的加密机名称
            if (StringUtils.isBlank(this.engineName)) {
                this.engineName = defaultEngineName;
            }
        }
        emConfig.setUsername(this.username);
        emConfig.setEngineName(this.engineName);
        emConfig.setIp(this.ip);
        emConfig.setPort(this.port);
        emConfig.setEngineCred(this.engineCred);
        // 填充密钥索引配置
        buildKeyIndex(emConfig);
        emConfig.setDefaultIndexCred(this.defaultKeyCred);

        return emConfig;
    }

    private void buildKeyIndex(EmConfig emConfig) {

        if (CollectionUtils.isEmpty(asymKeyIndexInfoList)) {
            return;
        }
        // 校验asymKeyIndexInfoList中的算法是否重复
        int size = asymKeyIndexInfoList.size();
        long distinctCount = asymKeyIndexInfoList.stream().map(AsymKeyIndexInfo::getAlgoName).distinct().count();
        if (size != distinctCount) {
            throw ManagementValidationError.ASYM_ALGO_NAME_REPEAT_ERROR.toException();
        }
        Map<String, String> signKeyIndexMap = new LinkedHashMap<>();
        Map<String, String> encKeyIndexMap = new LinkedHashMap<>();
        emConfig.setSignIndexes(signKeyIndexMap);
        emConfig.setEncIndexes(encKeyIndexMap);
        SysSupportedAlgoConfig sysSupportedAlgoConfig = ConfigHolder.get().get(SysSupportedAlgoConfig.class);
        List<String> asymmetricList = sysSupportedAlgoConfig.getSupport().extractAsymmetricList();
        List<String> pqcAsymAlgoStr = sysSupportedAlgoConfig.getSupport().extractPostQuantumList();
        HashSet<String> asymmetricSet = new HashSet<>(asymmetricList);
        asymmetricSet.addAll(pqcAsymAlgoStr);
        asymKeyIndexInfoList.forEach(asymKeyIndexInfo -> {
            String algoName = asymKeyIndexInfo.getAlgoName();
            asymmetricSet.stream().filter(algoName::contains).forEach(asymAlgoName -> {
                AsymAlgo asymAlgo = new AsymAlgo(algoName);
                String indexPair = buildIndexPair(asymKeyIndexInfo.getKeyStartIndex(),
                    asymKeyIndexInfo.getKeyEndIndex());
                switch (asymAlgo.getPurpose()) {
                    case SIGN_ENC:
                        signKeyIndexMap.put(algoName, indexPair);
                        encKeyIndexMap.put(algoName, indexPair);
                        return;
                    case SIGN:
                        signKeyIndexMap.put(algoName, indexPair);
                        return;
                    case ENC:
                        encKeyIndexMap.put(algoName, indexPair);
                        return;
                    default:
                }
            });
        });
    }

    public String getEngineName() {
        return engineName;
    }

    public void setEngineName(String engineName) {
        this.engineName = engineName;
    }

    public String getEngineType() {
        return engineType;
    }

    public void setEngineType(String engineType) {
        this.engineType = engineType;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEngineCred() {
        return engineCred;
    }

    public void setEngineCred(String engineCred) {
        this.engineCred = engineCred;
    }

    public @NotEmpty(message = ASYMMETRIC_KEY_INDEX_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY) List<AsymKeyIndexInfo> getAsymKeyIndexInfoList() {
        return asymKeyIndexInfoList;
    }

    public void setAsymKeyIndexInfoList(@Valid @NotEmpty(message = ASYMMETRIC_KEY_INDEX_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY) List<AsymKeyIndexInfo> asymKeyIndexInfoList) {
        this.asymKeyIndexInfoList = asymKeyIndexInfoList;
    }

    public String getDefaultKeyCred() {
        return defaultKeyCred;
    }

    public void setDefaultKeyCred(String defaultKeyCred) {
        this.defaultKeyCred = defaultKeyCred;
    }

}
