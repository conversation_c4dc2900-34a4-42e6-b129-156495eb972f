package kl.npki.base.management.model.system.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.configs.SysConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;

/**
 * 系统信息配置
 *
 * <AUTHOR>
 * @date 2025/7/17 16:34
 **/
@Schema(description = "系统信息配置")
public class SysConfigInfo {

    /**
     * 是否显示微软CSP设备
     */
    @Schema(description = "是否显示微软CSP设备")
    private boolean showCSP;

    /**
     * 是否开启完整性检查
     */
    @Schema(description = "是否开启完整性检查")
    private boolean openDataFull;

    /**
     * 是否加密请求体
     */
    @Schema(description = "是否加密请求体")
    private boolean encryptRequestBody;

    /**
     * 性能测试配置
     */
    @Schema(description = "高性能模式", defaultValue = "false")
    private boolean performanceTest;

    public static SysConfigInfo defaultInstance() {
        SysConfig sysConfig = BaseConfigWrapper.getSysConfig();

        SysConfigInfo sysConfigInfo = new SysConfigInfo();
        sysConfigInfo.setShowCSP(sysConfig.isShowCSP());
        sysConfigInfo.setOpenDataFull(sysConfig.isOpenDataFull());
        sysConfigInfo.setEncryptRequestBody(sysConfig.isEncryptRequestBody());
        sysConfigInfo.setPerformanceTest(sysConfig.isPerformanceTest());

        return sysConfigInfo;
    }

    public boolean isShowCSP() {
        return showCSP;
    }

    public void setShowCSP(boolean showCSP) {
        this.showCSP = showCSP;
    }

    public boolean isOpenDataFull() {
        return openDataFull;
    }

    public void setOpenDataFull(boolean openDataFull) {
        this.openDataFull = openDataFull;
    }

    public boolean isEncryptRequestBody() {
        return encryptRequestBody;
    }

    public void setEncryptRequestBody(boolean encryptRequestBody) {
        this.encryptRequestBody = encryptRequestBody;
    }

    public boolean isPerformanceTest() {
        return performanceTest;
    }

    public void setPerformanceTest(boolean performanceTest) {
        this.performanceTest = performanceTest;
    }
}
