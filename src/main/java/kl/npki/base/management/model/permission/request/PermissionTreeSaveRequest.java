package kl.npki.base.management.model.permission.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.ROLE_INFORMATION_SHOULD_NOT_BE_EMPTY_I18N_KEY;
import static kl.npki.base.management.constant.I18nParameterVerifyConstant.THE_ROLE_NAME_SHOULD_NOT_BE_EMPTY_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/9/5
 * @desc
 */
public class PermissionTreeSaveRequest {

    /**
     * 选中的资源id
     */
    @Schema(description = "选中的资源Code")
    private List<String> chooseList;

    /**
     * 角色id
     */
    @NotNull(message = ROLE_INFORMATION_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "角色编码")
    private String roleCode;

    /**
     * 角色名
     */
    @NotBlank(message = THE_ROLE_NAME_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 备注
     */
    private String remark;

    public List<String> getChooseList() {
        return chooseList;
    }

    public PermissionTreeSaveRequest setChooseList(List<String> chooseList) {
        this.chooseList = chooseList;
        return this;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public PermissionTreeSaveRequest setRoleCode(String roleCode) {
        this.roleCode = roleCode;
        return this;
    }

    public String getRoleName() {
        return roleName;
    }

    public PermissionTreeSaveRequest setRoleName(String roleName) {
        this.roleName = roleName;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public PermissionTreeSaveRequest setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    @Override
    public String toString() {
        return "PermissionTreeSaveRequest{" +
               "chooseList=" + chooseList +
               ", roleCode=" + roleCode +
               ", roleName='" + roleName + '\'' +
               ", remark='" + remark + '\'' +
               '}';
    }
}
