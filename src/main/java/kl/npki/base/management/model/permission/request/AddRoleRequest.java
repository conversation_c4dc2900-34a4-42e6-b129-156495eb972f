package kl.npki.base.management.model.permission.request;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.io.Serializable;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * <AUTHOR>
 * @date 2022/8/31
 * @desc
 */
public class AddRoleRequest implements Serializable {

    /**
     * 角色名
     */
    @NotBlank(message = THE_ROLE_NAME_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_CHARACTER_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_ROLE_NAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    private String roleName;

    /**
     * 角色编码
     */
    @NotBlank(message = ROLE_CODE_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_CHARACTER_CODE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = CHARACTER_ENCODING_CANNOT_USE_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    private String roleCode;

    /**
     * 父角色编码
     */
    @NotBlank(message = PARENT_ROLE_CODE_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_PARENT_ROLE_CODE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = PARENT_ROLE_CODE_CANNOT_USE_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    private String parentRoleCode;

    /**
     * 角色备注
     */
    @Size(max = 128, message = THE_LENGTH_OF_THE_ROLE_NOTE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    private String remark;

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getParentRoleCode() {
        return parentRoleCode;
    }

    public void setParentRoleCode(String parentRoleCode) {
        this.parentRoleCode = parentRoleCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
