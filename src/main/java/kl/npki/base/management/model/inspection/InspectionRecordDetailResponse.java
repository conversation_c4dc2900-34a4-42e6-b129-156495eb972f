package kl.npki.base.management.model.inspection;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.management.core.biz.inspection.model.InspectionGroupedResults;

import java.util.List;

/**
 * 系统巡检记录详情
 *
 * <AUTHOR>
 * @date 07/05/2025 19:54
 **/
public class InspectionRecordDetailResponse extends InspectionRecordListResponse {

    /**
     * 巡检项结果
     */
    @Schema(description = "巡检项结果")
    private List<InspectionGroupedResults> itemResults;

    public List<InspectionGroupedResults> getItemResults() {
        return itemResults;
    }

    public void setItemResults(List<InspectionGroupedResults> itemResults) {
        this.itemResults = itemResults;
    }
}
