package kl.npki.base.management.utils;

import jakarta.validation.constraints.NotNull;
import kl.nbase.db.support.druid.DruidDataSourceProperties;
import kl.nbase.db.support.druid.SlotDruidDataSourceProperties;
import kl.nbase.db.support.sharding.config.ShardingConfig;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.emengine.entity.secretkey.EmMainSecretKey;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.helper.utils.CommentedProperties;
import kl.nbase.helper.utils.PropertiesUtils;
import kl.nbase.helper.utils.ResourceUtils;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.constants.AsymAlgoTypeEnum;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.nbase.security.jce.provider.BouncyCastleProvider;
import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.service.CertSignService;
import kl.npki.base.core.biz.cert.service.ManageCertMgr;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.configs.EmConfig;
import kl.npki.base.core.configs.GroupEmConfig;
import kl.npki.base.core.configs.LoginTypeConfig;
import kl.npki.base.core.constant.*;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.DateUtil;
import kl.npki.base.core.utils.KeyStoreUtil;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.management.core.constants.DatasourceIndexEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.ScriptRunner;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;
import static kl.npki.base.core.constant.KeyStoreConstants.DEFAULT_MANAGE_ROOT_CA_ALIAS;

/**
 * <AUTHOR>
 * @Date 2023/3/21 15:27
 */
@SuppressWarnings("all")
public class PkiDemoBuildUtil {

    private static final Random RANDOM = new SecureRandom();
    private static final AsymAlgo DEFAULT_ALGO = AsymAlgo.RSA_2048;

    /**
     * 密码配置中用于匹配签名密钥的索引正则，配置密码机集群时，暂时只考虑一个
     */
    private static final Pattern EM_SIGN_INDEX_PATTERN = Pattern.compile("kl.base.em.groupEngine.engines\\[0]" +
        ".signIndexes.(\\w+)");
    /**
     * 密码配置中用于匹配签名密钥的索引正则，配置密码机集群时，暂时只考虑一个
     */
    private static final Pattern EM_ENC_INDEX_PATTERN = Pattern.compile("kl.base.em.groupEngine.engines\\[0]" +
        ".encIndexes.(\\w+)");
    /**
     * 配置名之间的连接符号
     */
    public static final String CONFIG_NAME_DELIMETER = ".";

    /**
     * 系统证书算法
     */
    public static final String SYSTEM_CERT_ALGO_CONFIG_NAME = "kl.base.algo.systemCertAlgo";


    public static void main(String[] args) throws Exception {
        Date now = new Date();
        // simpleDateFormat
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.dateTimePattern);
        String nowDateStr = simpleDateFormat.format(now);
        String nowDateSqlStr = "'" + nowDateStr + "'";
        String rootPath = FilenameUtils.normalize(args[0]);
        String configPath = FilenameUtils.normalize(args[1]);
        String tempConfigPath = args.length > 2 && StringUtils.isNotBlank(args[2]) ? FilenameUtils.normalize(args[2]) : null;
        AsymAlgo demoCertAsymAlgo = args.length > 3 && StringUtils.isNotBlank(args[3]) ? AsymAlgo.getAsymAlgo(args[3]) : DEFAULT_ALGO;

        String applicationFilePath = Paths.get(configPath, "application.properties").toString();
        String defaultFilePath = Paths.get(configPath, "npki-default.properties").toString();
        String demoFilePath = Paths.get(configPath, "npki-demo.properties").toString();
        String sqlPath = Paths.get(rootPath, "WEB-INF", "sql").toString();

        // 初始化application.properties中的demo配置
        String appResource = FileUtils.readFileToString(new File(applicationFilePath), StandardCharsets.UTF_8);
        // 构造application的properties对象
        CommentedProperties applicationProp = new CommentedProperties();
        PropertiesUtils.loadCommentedPropertiesFromSource(applicationProp, appResource);

        // 保存配置
        appResource = PropertiesUtils.toCommentedPropertiesString(applicationProp);
        FileUtils.writeStringToFile(new File(applicationFilePath), appResource, StandardCharsets.UTF_8);

        // 复制default配置到demo配置中
        String demoResource = FileUtils.readFileToString(new File(defaultFilePath), StandardCharsets.UTF_8);
        File demoFile = new File(demoFilePath);
        Files.deleteIfExists(demoFile.toPath());
        if (!demoFile.createNewFile()) {
            throw ManagementInternalError.FILE_CREATE_FAIL.toException(demoFilePath);
        }
        FileUtils.writeStringToFile(demoFile, demoResource, StandardCharsets.UTF_8);
        // 构造demo的properties对象
        CommentedProperties demoProp = new CommentedProperties();
        PropertiesUtils.loadCommentedPropertiesFromSource(demoProp, demoResource);

        // 构造密码机配置
        ClusterEmConfig clusterEmConfig = buildClusterEmConfig(demoProp);
        Security.addProvider(new BouncyCastleProvider());
        ClusterEngine engine = new ClusterEngine(clusterEmConfig.toClusterEngineConfig());

        // 初始化demo环境需要的数据，并生成sql语句，并写入demosql文件
        // 自签根证书及密钥对 算法类型从配置文件中的kl.base.algo.systemCertAlgo读取
        boolean containsKey = demoProp.containsKey(SYSTEM_CERT_ALGO_CONFIG_NAME);
        if (!containsKey) {
            throw ManagementInternalError.SYSTEM_CERT_ALGORITHM_NOT_CONFIG.toException(demoCertAsymAlgo.getAlgoName());
        }
        String property = demoProp.getProperty(SYSTEM_CERT_ALGO_CONFIG_NAME);
        // 将演示环境的管理根和管理员证书的算法改为默认配置的算法
        demoCertAsymAlgo = AsymAlgo.getAsymAlgo(property);
        KeyPair[] allSignKeyPair = engine.getAllSignKeyPair(demoCertAsymAlgo);
        // 管理根和身份证书各自用一对密钥
        if (allSignKeyPair == null || allSignKeyPair.length < 2) {
            throw ManagementInternalError.SIGN_KEY_NOT_FOUND.toException(demoCertAsymAlgo.getAlgoName());
        }
        KeyPair mgrRootKeyPair = allSignKeyPair[0];
        KeyPair idCertKeyPair = allSignKeyPair[1];
        CertRequestInfo certRequestInfo = new CertRequestInfo();
        certRequestInfo.setCn("PKI-DEMO-ROOT");
        Certificate rootCert = CertSignService.issueSelfSignCert(engine, certRequestInfo.buildDn(), mgrRootKeyPair, 365 * 40L);
        X500Name subject = new X500Name(certRequestInfo.buildDn());
        String subjectDn = subject.toString();
        Date startDate = rootCert.getStartDate()
            .getDate();
        Date endDate = rootCert.getEndDate()
            .getDate();
        String startDateStr = simpleDateFormat.format(startDate);
        String endDateStr = simpleDateFormat.format(endDate);
        // 生成预置根证书sql
        StringBuilder rootCertSql = new StringBuilder("-- 预置根证书\n");
        rootCertSql.append("INSERT INTO T_TRUST_CERT (ID, TENANT_ID, HEX_SN, SUBJECT_CN, KEY_ID, ISSUER_CN, " +
            "ISSUER_KEY_ID, VALID_START, VALID_END," +
            "SUBJECT_DN, CERT_VALUE, CERT_LEVEL, CREATE_TIME, IS_DELETE) VALUES (");
        rootCertSql.append(1).append(", ").append("'demo', ").append("'").append(CertUtil.getHexSnOfCert(rootCert)).append("', ");
        rootCertSql.append("'").append(CertUtil.getCommonName(rootCert.getSubject())).append("', ");
        rootCertSql.append("'', ").append("'").append(CertUtil.getCommonName(rootCert.getIssuer())).append("', ");
        rootCertSql.append("'', ").append("'").append(startDateStr).append("', ").append("'").append(endDateStr).append("', ").append("'").append(subjectDn).append("', ");
        rootCertSql.append("'").append(CertUtil.encodeCertWithBase64(rootCert)).append("', ");
        rootCertSql.append(TrustCertLevel.LOCAL.getCode()).append(", ").append(nowDateSqlStr).append(", ").append(0);
        rootCertSql.append(");\n");

        Properties applicationProperties = PropertiesUtils.loadOrCreateCfgFile(applicationFilePath);
        String sslTrustStorePassword = applicationProperties.getProperty("server.ssl.trust-store-password");
        if (StringUtils.isBlank(sslTrustStorePassword)) {
            throw kl.npki.management.core.exception.ManagementInternalError.PROPERTY_NOT_EXISTS.toException("server.ssl.trust-store-password 配置不能为空");
        }
        // 预生成服务可信证书trustkeytore 可用于GMSSL通讯
        byte[] trustKeyStoreBytes = KeyStoreUtil.genP12TrustKeyStoreBytes(Collections.singletonList(rootCert),
            Collections.singletonList(DEFAULT_MANAGE_ROOT_CA_ALIAS),
            sslTrustStorePassword);
        String trustKeyStoreBase64 = Base64Util.base64Encode(trustKeyStoreBytes);
        String trustKeyStoreFileName = KeyStoreConstants.DEFAULT_KEY_STORE_FILE_NAME;
        String defaultTrustKeyStoreFilePath = KeyStoreConstants.DEFAULT_TRUST_STORE_FILE_RELATIVE_PATH;
        ResourceUtils.writeToFile(trustKeyStoreBytes, KeyStoreConstants.DEFAULT_KEY_STORE_FILE_ABSOLUTE_PATH);
        // 生成trustStore sql，此处不写入绝对路径是为集群考虑
        StringBuilder trustStoreSql = new StringBuilder("-- 预置信任证书\n");
        trustStoreSql.append("INSERT INTO T_FILE_CONFIG (ID, FILE_NAME, FILE_PATH, FILE_CONTENT_BASE64, CREATE_TIME)")
            .append(" values (3, '").append(trustKeyStoreFileName).append("', '").append(defaultTrustKeyStoreFilePath)
            .append("', '").append(trustKeyStoreBase64).append("', ").append(nowDateSqlStr).append(");\n");
        // 获取身份证书公钥
        PublicKey idCertKeyPairPublic = idCertKeyPair.getPublic();
        // 预置身份证书
        Certificate idCert = CertSignService.issueCert(engine,
            CertTypeEnum.USER_SIGN_CERT,
            "CN=DEMO-ID, C=CN",
            idCertKeyPairPublic,
            365 * 20L,
            rootCert,
            null);
        StringBuilder idCertSql = new StringBuilder("-- 预置身份证书\n");
        idCertSql.append("INSERT INTO T_TRUST_CERT (ID, TENANT_ID, HEX_SN, SUBJECT_CN, KEY_ID, ISSUER_CN, " +
            "ISSUER_KEY_ID, VALID_START, VALID_END," +
            "SUBJECT_DN, CERT_VALUE, CERT_LEVEL,CERT_TYPE, CREATE_TIME, IS_DELETE) VALUES (");
        idCertSql.append(2).append(", ").append("'demo', ").append("'").append(CertUtil.getHexSnOfCert(idCert)).append("', ");
        idCertSql.append("'").append(CertUtil.getCommonName(idCert.getSubject())).append("', ");
        idCertSql.append("'").append(CertUtil.getKeyId(idCert)).append("', ").append("'").append(CertUtil.getCommonName(idCert.getIssuer())).append("', ");
        idCertSql.append("'").append(CertUtil.getAuthorityKeyId(idCert)).append("', ").append("'").append(startDateStr).append("', ").append("'").append(endDateStr).append("', ").append("'").append(idCert.getSubject().toString()).append("', ");
        idCertSql.append("'").append(CertUtil.encodeCertWithBase64(idCert)).append("', ");
        idCertSql.append(TrustCertLevel.LOCAL.getCode()).append(", ").append(TrustCertType.IDENTITY.getCode()).append(", ").append(nowDateSqlStr).append(", ").append(0);
        idCertSql.append(");\n");

        KeyPair sslCertKeyPair = engine.genKeyPair(demoCertAsymAlgo);
        KeyPair sslEncCertKeyPair = engine.genKeyPair(demoCertAsymAlgo);
        boolean isTwinCert = isTwinCert(demoCertAsymAlgo.getType());
        // 预置SSL站点证书
        Certificate sslCert = CertSignService.issueCert(engine,
            CertTypeEnum.SSL_SIGN_SERVER_CERT,
            "CN=DEMO-SSL, C=CN",
            sslCertKeyPair.getPublic(),
            365 * 20L,
            rootCert,
            "127.0.0.1");
        Certificate sslEncCert = null;
        if (isTwinCert) {
            sslEncCert = CertSignService.issueCert(engine,
                CertTypeEnum.SSL_ENC_SERVER_CERT,
                "CN=DEMO-SSL, C=CN",
                sslEncCertKeyPair.getPublic(),
                365 * 20L,
                rootCert,
                "127.0.0.1");
        }

        StringBuilder sslCertSql = new StringBuilder("-- 预置SSL站点证书\n");
        sslCertSql.append("INSERT INTO T_TRUST_CERT (ID, TENANT_ID, HEX_SN, SUBJECT_CN, KEY_ID, ISSUER_CN, " +
            "ISSUER_KEY_ID, VALID_START, VALID_END," +
            "SUBJECT_DN, CERT_VALUE,ENC_CERT_VALUE, CERT_LEVEL,CERT_TYPE, CREATE_TIME, IS_DELETE) VALUES (");
        sslCertSql.append(3).append(", ").append("'demo', ").append("'").append(CertUtil.getHexSnOfCert(sslCert)).append("', ");
        sslCertSql.append("'").append(CertUtil.getCommonName(sslCert.getSubject())).append("', ");
        sslCertSql.append("'").append(CertUtil.getKeyId(sslCert)).append("', ").append("'").append(CertUtil.getCommonName(sslCert.getIssuer())).append("', ");
        sslCertSql.append("'").append(CertUtil.getAuthorityKeyId(sslCert)).append("', ").append("'").append(startDateStr).append("', ").append("'").append(endDateStr).append("', ").append("'").append(sslCert.getSubject().toString()).append("', ");
        sslCertSql.append("'").append(CertUtil.encodeCertWithBase64(sslCert)).append("', ");
        sslCertSql.append("'");
        if (isTwinCert) {
            sslCertSql.append(CertUtil.encodeCertWithBase64(sslEncCert));
        }
        sslCertSql.append("', ");
        sslCertSql.append(TrustCertLevel.LOCAL.getCode()).append(", ").append(TrustCertType.SSL.getCode()).append(", ").append(nowDateSqlStr).append(", ").append(0);
        sslCertSql.append(");\n");

        // 设置生成keyStore所需参数
        List<PrivateKey> privateKeyList = new ArrayList<>();
        List<Certificate> certList = new ArrayList<>();
        List<String> aliasList = new ArrayList<>();
        privateKeyList.add(sslCertKeyPair.getPrivate());
        certList.add(sslCert);
        if (isTwinCert) {
            privateKeyList.add(sslEncCertKeyPair.getPrivate());
            certList.add(sslEncCert);
        }
        aliasList.add(CertTypeEnum.SSL_SIGN_SERVER_CERT.name());
        aliasList.add(CertTypeEnum.SSL_ENC_SERVER_CERT.name());

        String sslKeyStorePassword = applicationProperties.getProperty("server.ssl.key-store-password");
        if (StringUtils.isBlank(sslKeyStorePassword)) {
            throw kl.npki.management.core.exception.ManagementInternalError.PROPERTY_NOT_EXISTS.toException("server.ssl.key-store-password 配置不能为空");
        }
        // 预生成ssl站点证书keystore 可用于GMSSL通讯
        byte[] keyStoreBytes = KeyStoreUtil.genP12KeyStoreBytes(privateKeyList, certList, aliasList, sslKeyStorePassword);
        String defaultKeyStoreFileName = KeyStoreConstants.DEFAULT_KEY_STORE_FILE_NAME;
        String defaultKeyStoreFilePath = KeyStoreConstants.DEFAULT_KEY_STORE_FILE_RELATIVE_PATH;
        ResourceUtils.writeToFile(keyStoreBytes, WEB_ROOT + File.separator +
            KeyStoreConstants.DEFAULT_KEY_STORE_FILE_RELATIVE_PATH);
        // 生成keyStore sql，此处不写入绝对路径是为集群考虑
        StringBuilder keyStoreSql = new StringBuilder("-- 预置证书\n");
        keyStoreSql.append("INSERT INTO T_FILE_CONFIG (ID, FILE_NAME, FILE_PATH, FILE_CONTENT_BASE64, CREATE_TIME)")
            .append(" values (4, '").append(defaultKeyStoreFileName).append("', '").append(defaultKeyStoreFilePath)
            .append("', '").append(Base64Util.base64Encode(keyStoreBytes)).append("', ").append(nowDateSqlStr).append(");\n");

        // 添加登录模式配置
        LoginTypeConfig loginTypeConfig = new LoginTypeConfig();
        loginTypeConfig.setLoginModel(LoginType.PWD.getType());
        buildProperties(demoProp, loginTypeConfig, loginTypeConfig.prefix());

        // 生成主密钥
        BlockSymAlgo symAlgo = BlockSymAlgo.SM4_GCM_NOPADDING;
        String mainKeyAlgo = symAlgo.getAlgoStr();
        Long mainKeyId = 1122334L;
        EmMainSecretKey eskey = engine.exportMainKey();
        byte[] iv = engine.getRandom(symAlgo.getIvLength());
        String base64Iv = Base64Util.base64Encode(iv);
        // 生成预置主密钥sql
        StringBuilder mainKeySql = new StringBuilder("-- 预置主密钥\n");
        mainKeySql.append("INSERT INTO t_main_key (" +
            "id, " +
            "tenant_id, " +
            "key_algo, " +
            "pub_key_id, " +
            "key_value, " +
            "iv, " +
            "ref_value, " +
            "engine_id, " +
            "key_status, " +
            "be_using, " +
            "create_time) VALUES (");
        mainKeySql
            .append(mainKeyId).append(", ")
            .append("'demo', ")
            .append("'").append(mainKeyAlgo).append("', ")
            .append("'").append(eskey.getB64PublicKeyId()).append("', ")
            .append("'").append(eskey.getB64EncMainKeyValue()).append("', ")
            .append("'").append(base64Iv).append("', ")
            .append("'").append(eskey.getB64RefValue()).append("', ")
            .append(1).append(", ")
            .append(0).append(", ")
            .append(1).append(", ")
            .append(nowDateSqlStr)
            .append(");\n");

        // 写入demosql文件
        String demoSqlPath = Paths.get(sqlPath, "data", "npki-demo.sql").toString();
        File demoSqlFile = new File(demoSqlPath);
        if (!demoSqlFile.exists() && !demoSqlFile.createNewFile()) {
            throw ManagementInternalError.FILE_CREATE_FAIL.toException(demoSqlPath);
        }
        String demoSqlStr = FileUtils.readFileToString(demoSqlFile, StandardCharsets.UTF_8);
        FileUtils.writeStringToFile(new File(demoSqlPath), demoSqlStr + rootCertSql + idCertSql + sslCertSql + mainKeySql +
            trustStoreSql + keyStoreSql, StandardCharsets.UTF_8);

        // 预置h2数据库配置
        // 从demo配置中初始化数据库配置
        SlotDataSourceConfig slotDataSourceConfig = PropertiesUtils.form(SlotDataSourceConfig.class, demoResource,
            "kl.base.datasource");
        slotDataSourceConfig.setSlotName(EnvironmentEnum.DEMO.getId());
        DruidDataSourceProperties dataSourceProperties = slotDataSourceConfig.getDatasource().get(0);
        // 监控指标数据源名称需要不一样
        dataSourceProperties.setName(EnvironmentEnum.DEMO.getId() + DatasourceIndexEnum.FORMAL_DATA_BASE.getDesc());
        dataSourceProperties.setUrl("jdbc:h2:file:db/npki;AUTO_SERVER=TRUE;MODE=MySQL;");

        // 构造测试sharding配置
        ShardingConfig shardingConfig = new ShardingConfig();
        shardingConfig.setShardingEnabled(true);
        shardingConfig.setReadWriteEnabled(false);
        slotDataSourceConfig.setSharding(shardingConfig);

        SlotDruidDataSourceProperties druid = new SlotDruidDataSourceProperties();
        druid.setDriverClassName(DbConfigType.H2.getDriver());
        // 这里不设置默认的校验语句，则会执行 SlotDruidDataSourceProperties 中设置的默认校验语句，部分数据库不支持默认的语句，从而报错
        druid.setValidationQuery(DbConfigType.H2.getValidationQuery());
        slotDataSourceConfig.setDruid(druid);

        buildProperties(demoProp, slotDataSourceConfig, "kl.base.datasource");

        // 追加额外的demo配置
        if (StringUtils.isNotEmpty(tempConfigPath)) {
            File tempConfigFileDir = new File(tempConfigPath);
            if (tempConfigFileDir.exists()) {
                File[] tempConfigFiles = tempConfigFileDir.listFiles((dir, name) -> name.matches("^demo\\S*"));
                if (ArrayUtils.isNotEmpty(tempConfigFiles)) {
                    for (File demoConigFile : tempConfigFiles) {
                        String extDemoConfigSource = FileUtils.readFileToString(demoConigFile, StandardCharsets.UTF_8);
                        PropertiesUtils.loadCommentedPropertiesFromSource(demoProp, extDemoConfigSource);
                    }
                }
            }
        }

        // 将demo配置写入文件
        FileUtils.writeStringToFile(demoFile, PropertiesUtils.toCommentedPropertiesString(demoProp), StandardCharsets.UTF_8);

        // 初始化demo数据库
        // 删除demo数据库
        File dbFileList = new File(WEB_ROOT + "/db");
        if (!dbFileList.exists()) {
            boolean success = dbFileList.mkdir();
            if (!success) {
                throw ManagementInternalError.FILE_CREATE_FAIL.toException("Could not create db directory: " + dbFileList.getAbsolutePath());
            }
        }
        for (File file : Objects.requireNonNull(dbFileList.listFiles())) {
            Files.deleteIfExists(file.toPath());
        }
        // 设置h2数据库的环境变量
        System.setProperty("h2.baseDir", WEB_ROOT);
        // 初始化demo数据库文件
        initH2Database(sqlPath, dataSourceProperties);
        engine.close();
    }

    @NotNull
    private static String buildProperties(CommentedProperties demoProp, Object config,
                                          String prefix) {
        String demoResource = PropertiesUtils.writeToCommentedPropertiesString(config, demoProp, prefix);
        // 重新加载demo.properties对象
        PropertiesUtils.loadCommentedPropertiesFromSource(demoProp, demoResource);
        return demoResource;
    }

    private static void initH2Database(String sqlPath, DruidDataSourceProperties dbProp) throws IOException,
        SQLException, ClassNotFoundException {
        String realRegionSqlPath = WEB_ROOT + "specialsql/regionsql/cn.sql";

        Class.forName(DbConfigType.H2.getDriver());
        // 建立数据库连接
        try (Connection h2Connection = DriverManager.getConnection(dbProp.getUrl(), dbProp.getUsername(),
            dbProp.getPassword())) {

            ScriptRunner scriptRunner = new ScriptRunner(h2Connection);

            // 1. 执行建表语句
            try (FileReader h2SqlFileReader = new FileReader(Paths.get(sqlPath, "h2.sql").toString())) {
                scriptRunner.runScript(h2SqlFileReader);
            }

            // 2. 执行data目录下的sql
            try (FileReader npkiSqlFileReader = new FileReader(Paths.get(sqlPath, "data", "npki.sql").toString())) {
                scriptRunner.runScript(npkiSqlFileReader);
            }

            // 3. 执行demo目录下的sql
            try (FileReader demoSqlFileReader =
                     new FileReader(Paths.get(sqlPath, "data", "npki-demo.sql").toString())) {
                scriptRunner.runScript(demoSqlFileReader);
            }

            // 4. regionsql不为空，执行regionsql目录下的初始化sql
            if (new File(realRegionSqlPath).exists()) {
                try (FileReader demoSqlFileReader = new FileReader(Paths.get(realRegionSqlPath).toString())) {
                    scriptRunner.runScript(demoSqlFileReader);
                }
            }
        }
    }

    private static ClusterEmConfig buildClusterEmConfig(CommentedProperties demoProp) {
        ClusterEmConfig clusterEmConfig = new ClusterEmConfig();
        GroupEmConfig groupEmConfig = buildGenericFileGroupEngineConfig(demoProp, "main");
        clusterEmConfig.setGroupEngine(groupEmConfig);
        // 构建demoProperties
        buildProperties(demoProp, clusterEmConfig, "kl.base.em");
        return clusterEmConfig;
    }

    private static GroupEmConfig buildGenericFileGroupEngineConfig(CommentedProperties demoProp, String groupName) {
        GroupEmConfig groupFileEmConfig = new GroupEmConfig();
        List<EmConfig> fileEngines = new ArrayList<>();
        String keystoreFilePath = "keystore";
        EmConfig fileEmConfig = new EmConfig();
        fileEmConfig.setEngineName("File" + RANDOM.nextInt(5));
        fileEmConfig.setEncIndexes(buildKeyIndex(demoProp, false, AsymAlgo.SM2, AsymAlgo.RSA_2048));
        fileEmConfig.setSignIndexes(buildKeyIndex(demoProp, true, AsymAlgo.SM2, AsymAlgo.RSA_2048));
        fileEmConfig.setDefaultIndexCred(null);
        fileEmConfig.setUsername("root");
        fileEmConfig.setPath(keystoreFilePath);
        fileEmConfig.setEngineType(EmTypeEnum.FILE_ENGINE.getEngineType());

        fileEngines.add(fileEmConfig);

        groupFileEmConfig.setEngines(fileEngines);
        groupFileEmConfig.setGroupName(groupName);
        // 初始化keystore目录
        File keystoreFile = new File(keystoreFilePath);
        if (keystoreFile.exists()) {
            try {
                FileUtils.deleteDirectory(keystoreFile);
            } catch (IOException e) {
                // ignore
            }
        }
        boolean success = keystoreFile.mkdir();
        if (!success) {
            throw BaseInternalError.FILE_CREATE_FAIL.toException("Could not create keystore directory: " + keystoreFile.getAbsolutePath());
        }
        return groupFileEmConfig;
    }

    private static Map<String, String> buildKeyIndex(CommentedProperties demoProp, boolean forSign,
                                                     AsymAlgo... defaultKeyAlgo) {

        // 从配置中匹配出密钥索引
        Pattern keyNamePattern = forSign ? EM_SIGN_INDEX_PATTERN : EM_ENC_INDEX_PATTERN;
        Set<String> keyNames = demoProp.propertyNames().stream()
            .filter(key -> {
                Matcher matcher = keyNamePattern.matcher(key);
                return matcher.matches();
            })
            .collect(Collectors.toSet());

        // 如果为空，则使用默认算法进行填充
        Map<String, String> keyIndexMap = new LinkedHashMap<>();
        if (CollectionUtils.isEmpty(keyNames)) {
            for (AsymAlgo keyAlgo : defaultKeyAlgo) {
                keyIndexMap.put(keyAlgo.getAlgoName(), buildIndexPair(1, 10));
            }
        } else {
            keyNames.stream().forEach(key -> {
                String algoName = key.substring(key.lastIndexOf(CONFIG_NAME_DELIMETER) + 1);
                String indexes = demoProp.getProperty(key);
                // 默认起始索引
                int startIndex = 1;
                // 默认结束索引
                int endIndex = 10;
                if (StringUtils.isNotBlank(indexes)) {
                    // 配置索引不为空，则获取配置的索引
                    String[] indexArr = indexes.split(BaseConstant.COMMA);
                    startIndex = Integer.parseInt(indexArr[0]);
                    endIndex = Integer.parseInt(indexArr[indexArr.length - 1]);
                }
                keyIndexMap.put(algoName, buildIndexPair(startIndex, endIndex));
            });
        }
        return keyIndexMap;
    }

    private static String buildIndexPair(int startIndex, int endIndex) {
        StringBuilder indexPair = new StringBuilder();
        for (int i = startIndex; i <= endIndex; i++) {
            indexPair.append(i);
            if (i != endIndex) {
                indexPair.append(BaseConstant.COMMA);
            }
        }
        return indexPair.toString();
    }

    /**
     * 是否需要签发双证书，由配置的算法kl.base.sys.systemCertAlgo决定<br/>
     * <li>如果配置的算法类型是RSA和后量子，则站点证书、身份证书、管理员证书签发单证书</li>
     * <li>如果配置的算法是SM2，则站点证书、管理员证书签发双证书</li>
     * {@link ManageCertMgr#isTwinCert()}
     *
     * @return 是否需要签发双证书
     */
    public static boolean isTwinCert(AsymAlgoTypeEnum algoTypeEnum) {
        switch (algoTypeEnum) {
            case RSA:
            case ML_DSA:
            case SLH_DSA:
            case LMS_SM3_R1:
            case HSS_SM3_R1:
            case AIGIS_SIG_R1:
                return false;
            case SM2:
                return true;
            default:
                throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(algoTypeEnum.getDesc());
        }
    }

}
