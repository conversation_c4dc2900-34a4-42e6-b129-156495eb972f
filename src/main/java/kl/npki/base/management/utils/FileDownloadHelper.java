package kl.npki.base.management.utils;

import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.utils.DateUtil;
import kl.npki.base.core.utils.URLCoder;
import kl.npki.base.management.exception.ManagementInternalError;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * 文件下载工具类
 *
 * <AUTHOR>
 */
public class FileDownloadHelper {
    /**
     * Content-disposition 是 MIME 协议的扩展
     */
    public static final String CONTENT_DISPOSITION = "Content-disposition";

    public static final String FILE_ZIP_CONTENT_TYPE = "application/zip";

    /**
     * HTTP附件下载方式
     */
    public static final String ATTACHMENT = "attachment;filename*=utf-8''%s";

    /**
     * 字节缓冲区大小，8192参考BufferedInputStream、BufferedOutputStream
     */
    private static final int BYTE_BUFFER_SIZE = 8192;

    private static final Pattern ILLEGAL_CHAR_PATTERN = Pattern.compile("[\\r\\n\\\\/:*?\"<>|\u0000-\u001F\u007F\\p{C}]", Pattern.UNICODE_CHARACTER_CLASS);
    private static final Pattern LEADING_TRAILING_SPECIAL_CHARS_PATTERN = Pattern.compile("^[.\\s_-]+|[.\\s_-]+$");
    private static final Pattern MULTIPLE_UNDERSCORES_PATTERN = Pattern.compile("_+");
    private static final String DEFAULT_FILE_NAME = "download";
    private static final int DEFAULT_FILE_NAME_LENGTH = 250;


    private FileDownloadHelper() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 构建{@link HttpHeaders#CONTENT_DISPOSITION}所需字符
     *
     * @param filename 文件名
     * @return String
     */
    public static String buildAttachFileName(String filename) {
        return "attachment;filename=" + filename;
    }

    /**
     * 将文件转化为响应
     *
     * @param file        文件
     * @param fileName    文件名称
     * @param deleteAfter 转化完成后是否删除源文件
     * @return ResponseEntity
     */
    public static ResponseEntity<byte[]> getResponseEntityByFile(File file, String fileName, boolean deleteAfter) {

        try {
            //读取文件转为字节流
            byte[] fileContent = FileUtils.readFileToByteArray(file);
            if (deleteAfter) {
                FileUtils.delete(file);
            }
            return getResponseEntityByFile(fileContent, fileName);
        } catch (IOException e) {
            throw ManagementInternalError.FILE_DOWNLOAD_FAIL.toException(e);
        }
    }

    /**
     * 将文件内容转化为响应
     *
     * @param fileContent 文件内容字节数组
     * @param fileName    文件名称
     * @return ResponseEntity
     */
    public static ResponseEntity<byte[]> getResponseEntityByFile(byte[] fileContent, String fileName) {
        // 设置响应头信息
        HttpHeaders responseHeaders = new HttpHeaders();
        String downloadFileName = encodeFileNameForURL(fileName);
        responseHeaders.add(HttpHeaders.CONTENT_DISPOSITION, FileDownloadHelper.buildAttachFileName(downloadFileName));
        responseHeaders.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

        return ResponseEntity.ok().headers(responseHeaders).body(fileContent);
    }

    /**
     * 将文件转化为响应
     *
     * @param file        文件
     * @param deleteAfter 转化完成后是否删除源文件
     * @return ResponseEntity
     */
    public static ResponseEntity<byte[]> getResponseEntityByFile(File file, boolean deleteAfter) {
        return getResponseEntityByFile(file, file.getName(), deleteAfter);
    }

    public static String buildAttachmentVal(String fileName) {
        return String.format(ATTACHMENT, encodeFileNameForURL(fileName));
    }

    /**
     * 构造下载文件名，包含前缀和时间，最终文件名为prefix_证书类型_date
     *
     * @param prefixName 文件前缀名
     * @param fileName   文件主体名
     * @return 下载文件名
     */
    public static String buildFileNameWithPrefixAndDate(String prefixName, String fileName) {
        // 构造文件名
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.fileDateTimePattern);
        String downloadedFileName = String.format(fileName, prefixName, dateFormat.format(new Date()));
        return encodeFileNameForURL(downloadedFileName);
    }

    /**
     * 对下载的文件名进行URL编码以支持中文
     */
    private static String encodeFileNameForURL(String fileName) {
        return URLCoder.encode(fileName);
    }

    /**
     * 将文件写到响应中
     */
    public static void writeToResponse(File file, HttpServletResponse response) {
        try (InputStream is = new BufferedInputStream(Files.newInputStream(file.toPath())); OutputStream os = new BufferedOutputStream(response.getOutputStream())) {
            byte[] buffer = new byte[BYTE_BUFFER_SIZE];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.flush();
        } catch (IOException e) {
            throw BaseInternalError.FILE_READ_ERROR.toException(e);
        }
    }

    /**
     * 标准化文件名，确保文件名适合文件系统使用
     * 此方法通过一系列步骤清理和标准化输入的文件名，以防止文件名包含非法字符或过长，
     * 同时确保文件名不会为空
     *
     * @param fileName 待处理的文件名
     * @return 经过清理和标准化后的文件名
     */
    public static String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return DEFAULT_FILE_NAME;
        }

        // 1. 去除首尾空白字符
        fileName = fileName.trim();

        // 2. 替换非法字符为下划线
        fileName = ILLEGAL_CHAR_PATTERN.matcher(fileName).replaceAll(BaseConstant.UNDERSCORE);

        // 3. 清理首尾的特殊字符（包括空格、点、连字符）
        fileName = LEADING_TRAILING_SPECIAL_CHARS_PATTERN.matcher(fileName).replaceAll("");

        // 4. 合并连续下划线
        fileName = MULTIPLE_UNDERSCORES_PATTERN.matcher(fileName).replaceAll(BaseConstant.UNDERSCORE);

        // 5. 限制文件名长度（留出空间给扩展名等）
        if (fileName.length() > DEFAULT_FILE_NAME_LENGTH) {
            fileName = fileName.substring(0, DEFAULT_FILE_NAME_LENGTH);
        }

        // 6. 最终确保文件名非空
        if (fileName.isEmpty()) {
            fileName = DEFAULT_FILE_NAME;
        }

        return fileName;
    }
}

