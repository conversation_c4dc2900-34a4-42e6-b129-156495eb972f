package kl.npki.base.management.utils;

import kl.nbase.db.support.sharding.config.ShardingConfig;
import kl.nbase.helper.utils.CommentedProperties;
import kl.nbase.helper.utils.PropertiesUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Properties;


/**
 * 整合base和其他子系统的配置文件，生成application.properties
 *
 * <AUTHOR>
 * @Date 2023/3/1 15:45
 */
public class PkiPropertiesBuildUtil {

    private static final String SHARDING_PREFIX = "kl.base.sharding";

    public static void main(String[] args) throws Exception {
        if (args == null || args.length == 0) {
            return;
        }
        // 加载待处理的文件目录下所有文件
        String tmpConfigPath = FilenameUtils.normalize(args[0]);
        String resourcesPath = FilenameUtils.normalize(args[1]);
        String configPath = FilenameUtils.normalize(args[2]);

        File file = new File(tmpConfigPath);

        CommentedProperties applicationProp = new CommentedProperties();

        // 1.首先将各产品的sharding配置写入application.properties中
        // 首先读取application.properties作为基准配置
        File applicationFile = new File(Paths.get(resourcesPath, "application.properties").toString());
        String applicationResource = FileUtils.readFileToString(applicationFile, StandardCharsets.UTF_8);
        PropertiesUtils.loadCommentedPropertiesFromSource(applicationProp, applicationResource);

        // 写入预置到application中的配置
        // 读取所有application配置文件
        File[] applicationFiles = file.listFiles((dir, name) -> name.matches("^application-preset\\S*"));
        if (ArrayUtils.isNotEmpty(applicationFiles)) {
            for (File applicationPresetFile : applicationFiles) {
                String applicationPresetSource = FileUtils.readFileToString(applicationPresetFile, StandardCharsets.UTF_8);
                PropertiesUtils.loadCommentedPropertiesFromSource(applicationProp, applicationPresetSource);
            }
        }

        // 写入sharding配置
        // 读取所有sharding配置文件
        File[] shardingFiles = file.listFiles((dir, name) -> name.matches("^sharding\\S*"));
        if (ArrayUtils.isNotEmpty(shardingFiles)) {
            // 初始化base中的sharding配置
            ShardingConfig fullShardingConfig = PropertiesUtils.form(ShardingConfig.class, PropertiesUtils.toCommentedPropertiesString(applicationProp), SHARDING_PREFIX);
            fullShardingConfig.setTables(new ArrayList<>());
            Properties fullProperties = new Properties();
            // 依次读取sharding配置，整合后写入application中
            for (File shardingFile : shardingFiles) {
                String shardingSource = FileUtils.readFileToString(shardingFile, StandardCharsets.UTF_8);
                ShardingConfig shardingPart = PropertiesUtils.form(ShardingConfig.class, shardingSource, SHARDING_PREFIX);
                if (shardingPart.getTables() == null || shardingPart.getTables().isEmpty()) {
                    continue;
                }
                fullShardingConfig.getTables().addAll(shardingPart.getTables());
                if (ObjectUtils.isNotEmpty(shardingPart.getProps())) {
                    fullProperties.putAll(shardingPart.getProps());
                }
            }
            fullShardingConfig.setProps(fullProperties);
            // 将整合后的sharding配置写入application中
            PropertiesUtils.writeToCommentedPropertiesString(fullShardingConfig, applicationProp, SHARDING_PREFIX);
        }

        // 写入config目录的application.properties中，resources目录中的application作为基准模板
        File configAppFile = new File(Paths.get(configPath, "application.properties").toString());
        FileUtils.writeStringToFile(configAppFile, PropertiesUtils.toCommentedPropertiesString(applicationProp), StandardCharsets.UTF_8);

        // 2.以npki-base.properties为基准，继续写入各产品主配置文件，最终生成默认配置文件npki-default.properties
        // 读取npki-base.properties文件
        CommentedProperties npkiDefaultProp = new CommentedProperties();
        String npkiBaseProp = FileUtils.readFileToString(new File(Paths.get(tmpConfigPath, "npki-base.properties").toString()), StandardCharsets.UTF_8);
        PropertiesUtils.loadCommentedPropertiesFromSource(npkiDefaultProp, npkiBaseProp);
        File[] pkiFiles = file.listFiles((dir, name) -> name.matches("^npki\\S*"));
        if (pkiFiles == null || pkiFiles.length == 0) {
            return;
        }
        for (File pkiFile : pkiFiles) {
            if (pkiFile.getName().equals("npki-base.properties")) {
                continue;
            }
            String pkiSource  = FileUtils.readFileToString(pkiFile, StandardCharsets.UTF_8);
            PropertiesUtils.loadCommentedPropertiesFromSource(npkiDefaultProp, pkiSource);
        }

        // npki-defalut.properties文件写入config目录中
        File defaultFile = new File(Paths.get(configPath, "npki-default.properties").toString());
        FileUtils.writeStringToFile(defaultFile, PropertiesUtils.toCommentedPropertiesString(npkiDefaultProp), StandardCharsets.UTF_8);
    }
}
