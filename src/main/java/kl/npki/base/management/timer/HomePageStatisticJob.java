package kl.npki.base.management.timer;

import kl.nbase.timer.job.AbstractTimerJob;
import kl.npki.base.core.biz.home.service.SelfCheckStatisticService;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.BaseJobEnum;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 首页数据统计，缓存更新任务
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnMissingBean(HomePageStatisticJob.class)
public class HomePageStatisticJob extends AbstractTimerJob implements CommandLineRunner {

    @Resource
    private SelfCheckStatisticService selfCheckStatisticService;

    /**
     * 保证系统启动后，缓存进行初始化一次
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(String... args) throws Exception {
        taskExecute();
    }

    @Override
    protected void taskExecute() {
        // 暂无任务需要执行
    }

    @Override
    public boolean getEnable() {
        return BaseConfigWrapper.getHomePageDataCacheJobConfig().isEnabled();
    }

    @Override
    public String getCron() {
        return BaseConfigWrapper.getHomePageDataCacheJobConfig().getCronExpression();
    }

    @Override
    public String getId() {
        return BaseJobEnum.HOME_PAGE_DATA_UPDATE.getId();
    }

    @Override
    public String getName() {
        return BaseJobEnum.HOME_PAGE_DATA_UPDATE.getName();
    }
}
