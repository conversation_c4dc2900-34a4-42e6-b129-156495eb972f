package kl.npki.base.management.timer;

import kl.nbase.timer.job.AbstractTimerJob;
import kl.npki.base.core.biz.check.SelfCheckManager;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.BaseJobEnum;
import org.springframework.stereotype.Component;

/**
 * 服务自检定时任务
 *
 * <AUTHOR>
 * @Date 2024/2/26
 */
@Component
public class SelfCheckJob extends AbstractTimerJob {

    @Override
    protected void taskExecute() {
        SelfCheckManager.getInstance().checkAll();
    }

    @Override
    public boolean getEnable() {
        return BaseConfigWrapper.getSelfCheckJobConfig().isEnable();
    }

    @Override
    public String getCron() {
        return BaseConfigWrapper.getSelfCheckJobConfig().getCron();
    }

    @Override
    public String getId() {
        return BaseJobEnum.SELF_CHECK.getId();
    }

    @Override
    public String getName() {
        return BaseJobEnum.SELF_CHECK.getName();
    }

}
