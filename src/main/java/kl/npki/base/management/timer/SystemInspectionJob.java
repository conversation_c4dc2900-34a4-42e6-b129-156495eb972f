package kl.npki.base.management.timer;

import kl.nbase.timer.job.AbstractTimerJob;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.BaseJobEnum;
import kl.npki.base.core.utils.DateUtil;
import kl.npki.management.core.service.inspection.IInspectionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 系统巡检服务定时任务
 *
 * <AUTHOR>
 * @date 08/05/2025 11:16
 **/
@Component
public class SystemInspectionJob extends AbstractTimerJob {

    @Resource
    private IInspectionService inspectionService;

    @Override
    protected void taskExecute() {
        inspectionService.executeInspection("Automated Inspection" + DateUtil.formatFileDate(new Date()));
    }

    @Override
    public boolean getEnable() {
        return BaseConfigWrapper.getSystemInspectionJobConfig().isEnabled();
    }

    @Override
    public String getCron() {
        return BaseConfigWrapper.getSystemInspectionJobConfig().getCron();
    }

    @Override
    public String getId() {
        return BaseJobEnum.SYSTEM_INSPECTION.getId();
    }

    @Override
    public String getName() {
        return BaseJobEnum.SYSTEM_INSPECTION.getName();
    }

}
