package kl.npki.base.management.configurations;

import com.fasterxml.jackson.databind.ObjectMapper;
import kl.nbase.i18n.spring.LocaleResolveInterceptor;
import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.core.constant.RegionAndLanguageConstant;
import kl.npki.base.management.config.StaticResourceConfig;
import kl.npki.base.management.controller.IndexPageController;
import kl.npki.base.management.converter.CustomLocalDateTimeConverter;
import kl.npki.base.management.converter.StrictJacksonMessageConverter;
import kl.npki.base.management.interceptor.AuthorizationInterceptor;
import kl.npki.base.service.interceptor.OperationSignVerifyInterceptor;
import kl.npki.base.service.interceptor.RequestInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

import java.util.List;

import static kl.npki.base.management.constant.BaseManagementConstant.STRICT_OBJECT_MAPPER;

/**
 * <AUTHOR>
 * Created on 2022/08/23 16:25
 */
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    private static final String URL_PATTERN = "**.management.controller**";

    @Resource
    private NpkiServerConfig npkiServerConfig;

    @Resource(name = STRICT_OBJECT_MAPPER)
    private ObjectMapper strictMapper;

    @Override
    public void addFormatters(FormatterRegistry registry) {
        // 时间国际化
        registry.addConverter(new CustomLocalDateTimeConverter(
                RegionAndLanguageConstant.CN.getDateFormat(),
                RegionAndLanguageConstant.DZ.getDateFormat()));
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 统一使用StaticResourceConfig中定义的静态资源过滤模式
        // 注意：RequestInterceptor负责请求上下文处理（租户、日志、数据源等）
        // JwtAuthenticationTokenFilter负责JWT认证，两者职责不同但都需要过滤静态资源
        registry
                .addInterceptor(new LocaleResolveInterceptor())
                .addPathPatterns("/**")
                // 过滤前端资源
                .excludePathPatterns(StaticResourceConfig.PATTERNS);
        registry
            .addInterceptor(new RequestInterceptor())
            .addPathPatterns("/**")
            // 过滤前端资源
            .excludePathPatterns(StaticResourceConfig.PATTERNS);
        registry
            .addInterceptor(new AuthorizationInterceptor())
            // 当前仅在前端鉴权
            .addPathPatterns(npkiServerConfig.getApiPrefix() + "/**")
            // 过滤前端资源
            .excludePathPatterns(StaticResourceConfig.PATTERNS);
        registry
            .addInterceptor(new OperationSignVerifyInterceptor())
            // 当前仅在前端操作签名验证
            .addPathPatterns(npkiServerConfig.getApiPrefix() + "/**")
            // 过滤前端资源
            .excludePathPatterns(StaticResourceConfig.PATTERNS);

    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // 创建路径匹配类
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        configurer.addPathPrefix(npkiServerConfig.getApiPrefix(), clazz ->
                !clazz.equals(IndexPageController.class)
                        && clazz.isAnnotationPresent(RestController.class)
                        && antPathMatcher.match(URL_PATTERN, clazz.getPackage().getName())
        );
    }

    /**
     * 扩展消息转换器：将启用严格反序列化的 Jackson 转换器插入到首位
     * 以优先处理敏感或安全性要求高的 JSON 请求。
     *
     * @param converters Spring 默认的消息转换器列表
     */
    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        HttpMessageConverter<?> strictConverter = new StrictJacksonMessageConverter(strictMapper);

        // 插入到首位，确保严格转换器优先生效
        converters.add(0, strictConverter);
    }

    /**
     * 配置session的cookie为https
     */
    @Bean
    @ConditionalOnProperty(
        prefix = "server.ssl",
        name = "enabled",
        havingValue = "true"
    )
    public ServletContextInitializer servletContextInitializer() {
        return servletContext -> servletContext.getSessionCookieConfig().setSecure(true);
    }

}
