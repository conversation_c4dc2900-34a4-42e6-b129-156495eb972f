package kl.npki.base.management.configurations;

import kl.npki.base.core.configs.MultipartConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.util.unit.DataUnit;

import javax.servlet.MultipartConfigElement;

/**
 * <AUTHOR>
 */
@Configuration
public class MultipartConfiguration {
    /**
     * 配置上传文件大小的配置
     *
     * @return
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();

        MultipartConfig multipartConfig = BaseConfigWrapper.getMultipartConfig();
        //  单个数据大小
        factory.setMaxFileSize(DataSize.of(multipartConfig.getMaxFileSize(), DataUnit.MEGABYTES));
        // 总上传数据大小
        factory.setMaxRequestSize(DataSize.of(multipartConfig.getMaxRequestSize(), DataUnit.MEGABYTES));

        return factory.createMultipartConfig();
    }
}
