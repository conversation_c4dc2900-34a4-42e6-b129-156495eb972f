package kl.npki.base.management.configurations;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.TimeZone;

import static kl.npki.base.management.constant.BaseManagementConstant.STRICT_OBJECT_MAPPER;

/**
 * 严格反序列化策略的 ObjectMapper 配置类。
 *
 * <p>该 ObjectMapper 启用 {@code FAIL_ON_UNKNOWN_PROPERTIES = true}，
 * 用于拒绝包含未知字段的 JSON 请求，确保客户端提交的数据结构
 * 与后端声明完全一致。</p>
 *
 * <p>主要用于安全场景：</p>
 * <ul>
 *   <li>防止字段注入、兼容性绕过等潜在攻击。</li>
 *   <li>强化 API 契约约束，避免宽松解析带来的隐患。</li>
 * </ul>
 *
 * <p>注意：此配置仅供 {@code StrictJacksonMessageConverter} 使用，
 * 不影响系统默认的全局 JSON 配置。</p>
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Configuration
public class StrictObjectMapperConfig {

    @Bean(STRICT_OBJECT_MAPPER)
    public ObjectMapper strictObjectMapper() {
        // fixme 临时代码，待子产品都支持严格模式后需要关闭 {{
        String enableStrictProp = System.getProperty("enable.strict.object.mapper", "false");
        boolean enableStrict = Boolean.parseBoolean(enableStrictProp);
        // }}
        return JsonMapper.builder()
//                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, true)
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .defaultTimeZone(TimeZone.getDefault())
                .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true)
                .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
                .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
                .addModule(new JavaTimeModule())
                .build();
    }
}