package kl.npki.base.management.configurations;

import kl.nbase.gmssl.jsse.KlSslProvider;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;
import java.security.Security;

/**
 * <AUTHOR>
 * @since 2025/7/12 上午11:54
 */
@WebListener
public class SecurityServletContextListener implements ServletContextListener {


    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        // 应用卸载时清理 BC
        if (null != Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)) {
            Security.removeProvider(BouncyCastleProvider.PROVIDER_NAME);
        }
        // 应用卸载时清理 KLGMJSSE
        if (null != Security.getProvider(KlSslProvider.PROVIDER_NAME)) {
            Security.removeProvider(KlSslProvider.PROVIDER_NAME);
        }
    }

}