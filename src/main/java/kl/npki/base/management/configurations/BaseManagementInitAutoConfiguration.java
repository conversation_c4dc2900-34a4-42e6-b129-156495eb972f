package kl.npki.base.management.configurations;

import kl.nbase.config.holder.RefreshableConfigHolder;
import kl.nbase.gmssl.jsse.KlSslProvider;
import kl.nbase.netty.conf.NettyHttpServerConfig;
import kl.nbase.netty.conf.NettyTcpServerConfig;
import kl.nbase.rpc.core.utils.StringUtils;
import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.core.constant.DeployTypeEnum;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.management.utils.SubsystemUtil;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.server.Ssl;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Configuration;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import java.io.File;
import java.security.Security;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;

/**
 * war包部署部分路径需要修改，此处修改路径
 * <p>该类用于设置项目的上下文路径、keystore路径。以此来兼容war包部署</p>
 *
 * <AUTHOR>
 * @create 2025/5/27 下午1:28
 */
@Configuration
public class BaseManagementInitAutoConfiguration extends SpringBootServletInitializer implements BeanPostProcessor, SmartInitializingSingleton {

    /**
     * 上下文路径
     */
    public static String contextPath;

    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        contextPath = servletContext.getContextPath();

        // 初始化阶段注册 BC, 初始化阶段注册 KLGMJSSE
        Security.addProvider(new BouncyCastleProvider());
        Security.addProvider(new KlSslProvider());

        super.onStartup(servletContext);
    }

    /**
     * 自动设置上下文路径
     *
     * @param bean     the new bean instance
     * @param beanName the name of the bean
     * @return
     */
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) {
        if (bean instanceof ServerProperties) {
            resetServerProperties((ServerProperties) bean);
        }

        // 基于Netty组件提供的SSL服务，与管理端有相同需求，都需要将keyStore和trustStore路径修改为绝对路径，
        // 防止加载到第三方依赖jar中的p12文件
        if (bean instanceof NettyHttpServerConfig) {
            resetNettyHttpServer((NettyHttpServerConfig) bean);
        }

        if (bean instanceof NettyTcpServerConfig) {
            resetNettyTcpServer((NettyTcpServerConfig) bean);
        }

        return bean;
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        if (DeployTypeEnum.getDeployType() == DeployTypeEnum.WAR) {
            // war包部署需要设置主类
            builder.sources(SubsystemUtil.getSubsystemMainClass());
        }
        return builder;
    }

    private static void resetNettyTcpServer(NettyTcpServerConfig bean) {
        NettyTcpServerConfig nettyTcpServerConfig = bean;
        String sslKeyStore = nettyTcpServerConfig.getSslKeyStore();
        String sslTrustStore = nettyTcpServerConfig.getSslTrustStore();
        if (StringUtils.isNotBlank(sslKeyStore) && !new File(sslKeyStore).isAbsolute()) {
            nettyTcpServerConfig.setSslKeyStore(WEB_ROOT + File.separator + sslKeyStore);
        }
        if (StringUtils.isNotBlank(sslTrustStore) && !new File(sslTrustStore).isAbsolute()) {
            nettyTcpServerConfig.setSslTrustStore(WEB_ROOT + File.separator + sslTrustStore);
        }
    }

    private static void resetNettyHttpServer(NettyHttpServerConfig bean) {
        NettyHttpServerConfig nettyHttpServerConfig = bean;
        String sslKeyStore = nettyHttpServerConfig.getSslKeyStore();
        String sslTrustStore = nettyHttpServerConfig.getSslTrustStore();
        if (StringUtils.isNotBlank(sslKeyStore) && !new File(sslKeyStore).isAbsolute()) {
            nettyHttpServerConfig.setSslKeyStore(WEB_ROOT + File.separator + sslKeyStore);
        }
        if (StringUtils.isNotBlank(sslTrustStore) && !new File(sslTrustStore).isAbsolute()) {
            nettyHttpServerConfig.setSslTrustStore(WEB_ROOT + File.separator + sslTrustStore);
        }
    }

    private static void resetServerProperties(ServerProperties bean) {
        ServerProperties serverProperties = bean;
        if (null != contextPath && !contextPath.equals(serverProperties.getServlet().getContextPath())) {
            // 上下文路径不一致，以服务器返回为准并且修改配置文件中的路径
            serverProperties.getServlet().setContextPath(contextPath);
            RefreshableConfigHolder refreshableConfigHolder = ConfigHolder.get();
            if (null != refreshableConfigHolder) {
                NpkiServerConfig npkiServerConfig = refreshableConfigHolder.get(NpkiServerConfig.class);
                npkiServerConfig.setContextPath(contextPath);
                refreshableConfigHolder.save(npkiServerConfig);
            }
        }

        // 为了兼容war包部署此处按照以下修改
        // 将配置文件中的keyStore和trustStore相对路径修改为绝对路径
        Ssl ssl = serverProperties.getSsl();
        if (null != ssl) {
            String keyStore = ssl.getKeyStore();
            String trustStore = ssl.getTrustStore();
            if (!new File(keyStore).isAbsolute()) {
                // 注意，此处不修改配置文件中的配置是因为未来要支持集群部署的时候，会将现有的文件夹复制到新的服务器上，
                // 路径可能会不一样，所以此处只修改内存中的值
                ssl.setKeyStore(WEB_ROOT + File.separator + keyStore);
            }
            if (!new File(trustStore).isAbsolute()) {
                ssl.setTrustStore(WEB_ROOT + File.separator + trustStore);
            }
        }
    }

    @Override
    public void afterSingletonsInstantiated() {
        // 恢复配置对象中的SSL路径为相对路径
        restoreNettyConfigPaths();
    }

    /**
     * 恢复Netty配置对象中的路径为相对路径
     * 在所有bean初始化完成后执行，确保不影响Netty服务的正常启动
     */
    private void restoreNettyConfigPaths() {
        // 恢复HTTP SSL配置中的路径
        NettyHttpServerConfig httpConfig = ConfigHolder.get().get(NettyHttpServerConfig.class);
        if (httpConfig != null) {
            restoreNettyHttpServerPaths(httpConfig);
        }

        // 恢复TCP SSL配置中的路径
        NettyTcpServerConfig tcpConfig = ConfigHolder.get().get(NettyTcpServerConfig.class);
        if (tcpConfig != null) {
            restoreNettyTcpServerPaths(tcpConfig);
        }
    }

    /**
     * 恢复NettyHttpServerConfig中的路径为相对路径
     */
    private void restoreNettyHttpServerPaths(NettyHttpServerConfig config) {
        String webRootPrefix = WEB_ROOT + File.separator;

        String keyStore = config.getSslKeyStore();
        String trustStore = config.getSslTrustStore();

        if (StringUtils.isNotBlank(keyStore) && keyStore.startsWith(webRootPrefix)) {
            config.setSslKeyStore(keyStore.substring(webRootPrefix.length()));
        }

        if (StringUtils.isNotBlank(trustStore) && trustStore.startsWith(webRootPrefix)) {
            config.setSslTrustStore(trustStore.substring(webRootPrefix.length()));
        }
    }

    /**
     * 恢复NettyTcpServerConfig中的路径为相对路径
     */
    private void restoreNettyTcpServerPaths(NettyTcpServerConfig config) {
        String webRootPrefix = WEB_ROOT + File.separator;

        String keyStore = config.getSslKeyStore();
        String trustStore = config.getSslTrustStore();

        if (StringUtils.isNotBlank(keyStore) && keyStore.startsWith(webRootPrefix)) {
            config.setSslKeyStore(keyStore.substring(webRootPrefix.length()));
        }

        if (StringUtils.isNotBlank(trustStore) && trustStore.startsWith(webRootPrefix)) {
            config.setSslTrustStore(trustStore.substring(webRootPrefix.length()));
        }
    }

}
