package kl.npki.base.management.mapper.upload;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.management.model.upload.UploadFileUnfinishedResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/5/9 下午2:56
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UploadFileEntity2UploadFileResponseMapper extends BaseMapper<UploadFileEntity, UploadFileUnfinishedResponse> {

    @Override
    UploadFileUnfinishedResponse map(UploadFileEntity source);

    @Override
    UploadFileEntity reverseMap(UploadFileUnfinishedResponse target);
}
