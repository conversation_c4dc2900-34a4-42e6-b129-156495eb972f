package kl.npki.base.management.mapper.ssl;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.ssl.response.SslServerCertResponse;
import kl.npki.management.core.biz.ssl.model.SslServerCertInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SslServerCertInfo2Response extends BaseMapper<SslServerCertInfo, SslServerCertResponse> {
}
