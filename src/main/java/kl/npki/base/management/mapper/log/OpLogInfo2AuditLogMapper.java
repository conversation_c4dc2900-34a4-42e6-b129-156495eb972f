package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.management.core.biz.log.model.AuditLogInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OpLogInfo2AuditLogMapper extends BaseMapper<OpLogInfo, AuditLogInfo> {

}
