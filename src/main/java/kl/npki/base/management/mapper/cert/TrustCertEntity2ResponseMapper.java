package kl.npki.base.management.mapper.cert;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.management.model.cert.response.ManagerCertResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2024/8/2
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TrustCertEntity2ResponseMapper extends BaseMapper<ManagerCertResponse, TrustCertEntity> {}
