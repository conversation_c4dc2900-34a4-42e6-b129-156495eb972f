package kl.npki.base.management.mapper.upload;


import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.org.model.OrgBatchImportInfo;
import kl.npki.base.core.biz.org.model.OrgBatchImportResultInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgBatchImportInfo2ResultMapper extends BaseMapper<OrgBatchImportInfo, OrgBatchImportResultInfo> {
}
