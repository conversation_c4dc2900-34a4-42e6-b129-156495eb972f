package kl.npki.base.management.mapper.permission;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.repository.entity.TRoleResourceLinkDO;
import kl.npki.management.core.biz.admin.model.entity.RoleResourceLinkEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RoleResourceLinkDo2EntityMapper extends BaseMapper<TRoleResourceLinkDO, RoleResourceLinkEntity> {}