package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.log.response.OpLogDetailResponse;
import kl.npki.base.service.repository.entity.TOpLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OpLogDo2ResponseDetailMapper extends BaseMapper<TOpLogDO, OpLogDetailResponse> {

    @Override
    @Mapping(source = "id", target = "logId")
    OpLogDetailResponse map(TOpLogDO source);


    @Override
    @Mapping(source = "logId", target = "id")
    TOpLogDO reverseMap(OpLogDetailResponse target);
}
