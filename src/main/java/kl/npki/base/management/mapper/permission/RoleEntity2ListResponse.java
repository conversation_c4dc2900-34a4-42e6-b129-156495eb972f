package kl.npki.base.management.mapper.permission;

import kl.nbase.bean.convert.BaseMapper;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.management.core.biz.permission.response.RoleListResponse;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/1/17
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {I18nUtil.class})
public interface RoleEntity2ListResponse extends BaseMapper<RoleEntity, RoleListResponse> {
    @Override
    @Mapping(target = "roleName", expression = "java(I18nUtil.trWithDefaultMessage(source.getRoleNameI18nKey(), source.getRoleName()))")
    @Mapping(target = "remark", expression = "java(source.getRemark() == null ? null : I18nUtil.trWithDefaultMessage(source.getRemark(), source.getRemark()))")
    RoleListResponse map(RoleEntity source);

    @Override
    RoleEntity reverseMap(RoleListResponse target);
}
