package kl.npki.base.management.mapper.check;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.npki.base.management.model.check.DetailedSelfCheckResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * 自检结果与响应的mapper映射
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SelfCheckResult2DetailedResponseMapper extends BaseMapper<SelfCheckResult, DetailedSelfCheckResponse> {

    @Override
    @Mapping(target = "type", source = "category")
    DetailedSelfCheckResponse map(SelfCheckResult source);

    @Override
    @Mapping(target = "category", source = "type")
    SelfCheckResult reverseMap(DetailedSelfCheckResponse target);

}
