package kl.npki.base.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.management.model.system.request.SysManagerRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * sysInfoConfig 到 SysManagerRequest的转换接口
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysInfoConfig2SysManagerRequest extends BaseMapper<SysInfoConfig, SysManagerRequest> {
}
