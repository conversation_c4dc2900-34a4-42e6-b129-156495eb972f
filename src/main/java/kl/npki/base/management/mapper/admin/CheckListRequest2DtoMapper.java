package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.request.CheckListRequest;
import kl.npki.management.core.biz.admin.model.dto.AdminInfoListDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CheckListRequest2DtoMapper extends BaseMapper<CheckListRequest, AdminInfoListDTO> {
    @Override
    @Mapping(target = "status", ignore = true)
    AdminInfoListDTO map(CheckListRequest source);
}
