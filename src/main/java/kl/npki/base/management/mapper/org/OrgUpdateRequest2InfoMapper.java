package kl.npki.base.management.mapper.org;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.org.model.OrgUpdateInfo;
import kl.npki.base.management.model.org.OrgUpdateRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/3/21 15:05
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgUpdateRequest2InfoMapper extends BaseMapper<OrgUpdateRequest, OrgUpdateInfo> {

    @Override
    OrgUpdateInfo map(OrgUpdateRequest source);

    @Override
    OrgUpdateRequest reverseMap(OrgUpdateInfo target);
}
