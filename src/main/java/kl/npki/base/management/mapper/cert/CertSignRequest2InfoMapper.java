package kl.npki.base.management.mapper.cert;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.cert.request.CertSignRequest;
import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CertSignRequest2InfoMapper extends BaseMapper<CertSignRequest, CertRequestInfo> {
    @Override
    CertRequestInfo map(CertSignRequest source);

    @Override
    CertSignRequest reverseMap(CertRequestInfo target);
}
