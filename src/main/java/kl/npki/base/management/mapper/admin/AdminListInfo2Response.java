package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.management.model.admin.response.AdminInfoListResponse;
import kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * 接口转换
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {I18nUtil.class, UserStatus.class, CertStatus.class})
public interface AdminListInfo2Response extends BaseMapper<AdminInfoListInfo, AdminInfoListResponse> {

    @Override
    @Mapping(target = "roleName", expression = "java(I18nUtil.trWithDefaultMessage(adminInfoListInfo.getRoleNameI18nKey(), adminInfoListInfo.getRoleName()))")
    @Mapping(target = "statusDesc", expression = "java(UserStatus.getDescByCode(adminInfoListInfo.getStatus()))")
    @Mapping(target = "certStatusDesc", expression = "java(CertStatus.getDescByCode(adminInfoListInfo.getCertStatus()))")
    AdminInfoListResponse map(AdminInfoListInfo adminInfoListInfo);

}
