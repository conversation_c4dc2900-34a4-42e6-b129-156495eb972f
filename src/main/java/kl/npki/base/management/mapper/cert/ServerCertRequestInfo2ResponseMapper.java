package kl.npki.base.management.mapper.cert;


import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.cert.model.ServerCertRequestInfo;
import kl.npki.base.management.model.cert.response.CertInfoResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2025/6/19 11:37
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ServerCertRequestInfo2ResponseMapper extends BaseMapper<ServerCertRequestInfo, CertInfoResponse> {

}
