package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.request.AdminInfoListRequest;
import kl.npki.management.core.biz.admin.model.dto.AdminInfoListDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AdminRequest2DtoMapper extends BaseMapper<AdminInfoListRequest, AdminInfoListDTO> {}
