package kl.npki.base.management.mapper.org;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.management.model.org.OrgDetailResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/3/21 15:05
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgEntity2DetailResponseMapper extends BaseMapper<OrgEntity, OrgDetailResponse> {

    @Override
    OrgDetailResponse map(OrgEntity source);

    @Override
    OrgEntity reverseMap(OrgDetailResponse target);
}
