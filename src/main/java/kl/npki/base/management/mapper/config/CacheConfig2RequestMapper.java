package kl.npki.base.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.cache.CacheConfigRequest;
import kl.npki.management.core.biz.config.model.CacheConfigInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2024/1/31
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CacheConfig2RequestMapper extends BaseMapper<CacheConfigInfo, CacheConfigRequest> {
}
