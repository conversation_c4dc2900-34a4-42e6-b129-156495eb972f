package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.request.IssueAdminCertForDeployRequest;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface IssueForDeployRequest2AdminEntityMapper extends BaseMapper<IssueAdminCertForDeployRequest, AdminEntity> {

    @Override
    @Mapping(source = "subjectCn", target = "username")
    AdminEntity map(IssueAdminCertForDeployRequest source);


    @Override
    @Mapping(source = "username", target = "subjectCn")
    IssueAdminCertForDeployRequest reverseMap(AdminEntity target);
}
