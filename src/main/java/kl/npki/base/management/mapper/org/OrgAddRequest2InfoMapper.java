package kl.npki.base.management.mapper.org;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.org.model.OrgAddInfo;
import kl.npki.base.management.model.org.OrgAddRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/3/21 15:05
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgAddRequest2InfoMapper extends BaseMapper<OrgAddRequest, OrgAddInfo> {

    @Override
    OrgAddInfo map(OrgAddRequest source);

    @Override
    OrgAddRequest reverseMap(OrgAddInfo target);
}
