package kl.npki.base.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.login.model.LoginTypeInfo;
import kl.npki.base.management.model.LoginTypeResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025/06/26
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LoginTypeInfo2ResponseMapper extends BaseMapper<LoginTypeInfo, LoginTypeResponse> {
}
