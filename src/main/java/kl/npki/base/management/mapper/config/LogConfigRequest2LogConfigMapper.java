package kl.npki.base.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.configs.LogConfig;
import kl.npki.base.management.model.log.request.LogConfigRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LogConfigRequest2LogConfigMapper extends BaseMapper<LogConfigRequest, LogConfig> {
}
