package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.request.RegisterAdminRequest;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RegisterAdminRequest2AdminEntityMapper extends BaseMapper<RegisterAdminRequest, AdminEntity> {


    @Override
    @Mapping(source = "subjectCn", target = "username")
    AdminEntity map(RegisterAdminRequest source);

    @Override
    @Mapping(source = "username", target = "subjectCn")
    RegisterAdminRequest reverseMap(AdminEntity target);
}
