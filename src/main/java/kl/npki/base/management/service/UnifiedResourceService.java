package kl.npki.base.management.service;

import kl.npki.base.core.biz.batch.model.BatchResult;
import kl.npki.base.management.repository.entity.TResourceDO;
import kl.npki.management.core.biz.admin.model.entity.ResourceEntity;
import kl.npki.management.core.biz.permission.response.UnifiedResourceResponse;

import java.util.List;

/**
 * 统一资源服务接口
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public interface UnifiedResourceService {

    /**
     * 查询所有资源（树形结构）
     *
     * @param resourceNameOrUrl 资源名称或URL，支持模糊查询
     * @return 资源树形结构
     */
    List<UnifiedResourceResponse> listResourcesTree(String resourceNameOrUrl);


    /**
     * 新增资源
     *
     * @param resource 资源信息
     * @return 创建的资源
     */
    void createResource(TResourceDO resource);

    /**
     * 更新资源
     *
     * @param resource 资源信息
     * @return 更新的资源
     */
    void updateResource(TResourceDO resource);

    /**
     * 删除资源
     *
     * @param id 资源ID
     */
    void deleteResource(Long id);


    /**
     * 批量删除资源
     *
     * @param idList 资源列表
     * @return 是否删除成功
     */
    boolean batchDelete(List<Long> idList);
}
