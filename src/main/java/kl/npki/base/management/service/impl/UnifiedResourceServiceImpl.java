package kl.npki.base.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.id.UidGenerator;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.management.repository.entity.TResourceDO;
import kl.npki.base.management.repository.entity.TRoleResourceLinkDO;
import kl.npki.base.management.repository.mapper.TResourceMapper;
import kl.npki.base.management.repository.mapper.TRoleResourceLinkMapper;
import kl.npki.base.management.service.UnifiedResourceService;
import kl.npki.management.core.biz.permission.response.UnifiedResourceResponse;
import kl.npki.management.core.constants.DeleteStatusEnum;
import kl.npki.base.management.exception.ManagementValidationError;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统一资源服务实现类
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
@Service
public class UnifiedResourceServiceImpl implements UnifiedResourceService {

    @Resource
    private TResourceMapper resourceMapper;

    @Resource
    private UidGenerator uidGenerator;

    @Resource
    private TRoleResourceLinkMapper roleResourceLinkMapper;

    @Resource
    private ConvertService convertService;

    @Override
    public List<UnifiedResourceResponse> listResourcesTree(String resourceNameOrUrl) {

        // 查询所有资源
        LambdaQueryWrapper<TResourceDO> queryWrapper = new LambdaQueryWrapper<>();
        // 如果提供了资源名称或URL，则进行模糊查询
        if (StringUtils.isNotBlank(resourceNameOrUrl)) {
            queryWrapper.and(wrapper -> wrapper
                .like(TResourceDO::getResourceName, resourceNameOrUrl)
                .or()
                .like(TResourceDO::getResourceUrl, resourceNameOrUrl));
        }
        queryWrapper.eq(TResourceDO::getIsAnonymous, 0).or().isNull(TResourceDO::getIsAnonymous);
        List<TResourceDO> allResources = resourceMapper.selectList(queryWrapper);

        // 转换为响应对象
        List<UnifiedResourceResponse> responseList = allResources.stream()
            .map(resource -> convertService.convert(resource, UnifiedResourceResponse.class))
            .collect(Collectors.toList());

        // 构建树形结构
        return buildTree(responseList);
    }

    @Override
    public void createResource(TResourceDO resource) {
        long code = uidGenerator.getUID();
        resource.setResourceCode(String.valueOf(code));
        // 查询是否存在同名且已存在国际化键的资源
        LambdaQueryWrapper<TResourceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TResourceDO::getResourceName, resource.getResourceName())
            .eq(TResourceDO::getResourceUrl, resource.getResourceUrl())
            .isNotNull(TResourceDO::getResourceNameI18nKey)
            .eq(TResourceDO::getIsDelete, DeleteStatusEnum.NORMAL.getType());
        List<TResourceDO> tResourceDOS = resourceMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(tResourceDOS)) {
            resource.setResourceNameI18nKey(tResourceDOS.get(0).getResourceNameI18nKey());
        }
        resourceMapper.insert(resource);
    }

    @Override
    public void updateResource(TResourceDO resource) {
        resourceMapper.updateById(resource);
    }

    @Override
    @KlTransactional
    public void deleteResource(Long id) {
        // 查询要删除的资源信息
        TResourceDO resource = resourceMapper.selectById(id);
        if (resource == null) {
            return;
        }
        // 检查是否存在子资源
        LambdaQueryWrapper<TResourceDO> childQuery = new LambdaQueryWrapper<>();
        childQuery.eq(TResourceDO::getParentResourceCode, resource.getResourceCode());
        childQuery.eq(TResourceDO::getIsDelete, DeleteStatusEnum.NORMAL.getType());
        if (resourceMapper.selectCount(childQuery) > 0) {
            throw ManagementValidationError.RESOURCE_HAS_CHILD_RESOURCES_CANNOT_DELETE.toException();
        }
        // 删除关联的角色权限关系
        // TODO 存在管理角色权限时报错
        roleResourceLinkMapper.delete(
            new LambdaQueryWrapper<TRoleResourceLinkDO>()
                .eq(TRoleResourceLinkDO::getResourceCode, resource.getResourceCode())
        );

        // 删除资源
        resourceMapper.deleteById(id);

    }

    @Override
    @KlTransactional
    public boolean batchDelete(List<Long> idList) {
        if (idList == null || idList.isEmpty()) {
            return true;
        }

        List<TResourceDO> toDeleteResources = resourceMapper.selectBatchIds(idList);
        if (toDeleteResources.isEmpty()) {
            return true;
        }
        List<String> toDeleteResourceCodes = toDeleteResources.stream()
            .map(TResourceDO::getResourceCode).collect(Collectors.toList());

        // 检查是否存在子资源未被一同删除
        LambdaQueryWrapper<TResourceDO> childQuery = new LambdaQueryWrapper<>();
        childQuery.in(TResourceDO::getParentResourceCode, toDeleteResourceCodes);
        childQuery.eq(TResourceDO::getIsDelete, DeleteStatusEnum.NORMAL.getType());
        List<TResourceDO> childResources = resourceMapper.selectList(childQuery);

        if (!childResources.isEmpty()) {
            // 检查子资源是否都在删除列表中
            java.util.Set<Long> idSet = new java.util.HashSet<>(idList);
            for (TResourceDO child : childResources) {
                if (!idSet.contains(child.getId())) {
                    throw ManagementValidationError.BATCH_DELETE_HAS_CHILD_RESOURCES_NOT_SELECTED.toException();
                }
            }
        }

        // 删除关联的角色权限关系
        // TODO 存在管理角色权限时报错
        roleResourceLinkMapper.delete(
            new LambdaQueryWrapper<TRoleResourceLinkDO>()
                .in(TRoleResourceLinkDO::getResourceCode, toDeleteResourceCodes)
        );
        // 删除资源
        resourceMapper.deleteBatchIds(idList);
        return true;
    }

    /**
     * 构建树形结构
     *
     * @param resources 资源列表
     * @return 树形结构的资源列表
     */
    private List<UnifiedResourceResponse> buildTree(List<UnifiedResourceResponse> resources) {
        Map<String, UnifiedResourceResponse> resourceMap = resources.stream()
            .collect(Collectors.toMap(UnifiedResourceResponse::getResourceCode, resource -> resource));

        List<UnifiedResourceResponse> tree = new ArrayList<>();
        for (UnifiedResourceResponse resource : resources) {
            if (StringUtils.isBlank(resource.getParentResourceCode())) {
                tree.add(resource);
            } else {
                UnifiedResourceResponse parent = resourceMap.get(resource.getParentResourceCode());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(resource);
                } else {
                    // 如果找不到父节点，作为根节点添加
                    if (tree.stream().noneMatch(r -> r.getResourceCode().equals(resource.getResourceCode()))) {
                        tree.add(resource);
                    }
                }
            }
        }
        return tree;
    }
}
