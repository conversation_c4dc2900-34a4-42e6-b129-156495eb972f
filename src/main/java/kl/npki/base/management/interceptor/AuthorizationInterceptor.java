package kl.npki.base.management.interceptor;

import kl.nbase.auth.core.IAuthorizationService;
import kl.npki.base.management.utils.AuthzUtil;
import kl.npki.base.service.common.SpringBeanRegister;
import kl.npki.base.service.common.context.RequestContextHolder;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022/6/23
 * @desc 登录拦截器
 **/
public class AuthorizationInterceptor implements HandlerInterceptor {

    /**
     * 在请求处理之前，只有返回true才会执行请求
     *
     * @param request  请求
     * @param response 响应
     * @param handler  handler
     */
    @Override
    public boolean preHandle(@Nonnull HttpServletRequest request, @Nonnull HttpServletResponse response, @Nonnull Object handler) {
        //// 从request中获取认证token，白名单中的请求，token可以为null
        String token = AuthzUtil.getAuthorization(request);
        String requestUrl = RequestContextHolder.getContext().getRequestUrl();
        IAuthorizationService authorization = SpringBeanRegister.getBean(IAuthorizationService.class);
        return authorization.authorization(requestUrl, token);
    }
}
