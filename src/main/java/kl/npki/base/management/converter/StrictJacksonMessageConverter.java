package kl.npki.base.management.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.util.Collections;

/**
 * 严格模式的 JSON 消息转换器。
 *
 * <p>基于启用了 {@code FAIL_ON_UNKNOWN_PROPERTIES = true} 的 ObjectMapper，
 * 拒绝解析包含未声明字段的请求，确保客户端请求结构与后端模型一致。</p>
 *
 * <p>仅处理 {@code application/json} 类型请求。</p>
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
public class StrictJacksonMessageConverter extends MappingJackson2HttpMessageConverter {

    public StrictJacksonMessageConverter(ObjectMapper strictMapper) {
        super(strictMapper);
        setSupportedMediaTypes(Collections.singletonList(MediaType.APPLICATION_JSON));
    }
}