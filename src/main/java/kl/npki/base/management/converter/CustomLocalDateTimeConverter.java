package kl.npki.base.management.converter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.core.convert.converter.GenericConverter;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 自定义日期转换器，主要用于兼容不同国家的时间格式
 */
public class CustomLocalDateTimeConverter implements GenericConverter {

    private final DateTimeFormatter formatter;

    /**
     * @param patterns 时间格式
     */
    public CustomLocalDateTimeConverter(String... patterns) {
        DateTimeFormatterBuilder dateTimeFormatterBuilder = new DateTimeFormatterBuilder();
        for (int i = 0; i < patterns.length; i++) {
            dateTimeFormatterBuilder.appendOptional(DateTimeFormatter.ofPattern(patterns[i]));
        }
        this.formatter = dateTimeFormatterBuilder.toFormatter();
    }

    @Override
    public Set<ConvertiblePair> getConvertibleTypes() {
        ConvertiblePair localDateTimePair = new ConvertiblePair(String.class, LocalDateTime.class);
        ConvertiblePair datePair = new ConvertiblePair(String.class, Date.class);
        Set<ConvertiblePair> sets = new HashSet<>();
        sets.add(localDateTimePair);
        sets.add(datePair);
        return sets;
    }

    @Override
    public Object convert(Object source, TypeDescriptor sourceType, TypeDescriptor targetType) {
        if (!(source instanceof String)) {
            return null;
        }
        String dateTimeStr = (String) source;
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        // 转LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr, formatter);
        if (targetType.getObjectType().isAssignableFrom(LocalDateTime.class)) {
            return localDateTime;
        }
        // 转Date
        if (targetType.getObjectType().isAssignableFrom(Date.class)) {
            return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        return source;
    }

}