package kl.npki.base.management.controller.inspection;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.inspection.model.InspectionReportInfo;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.IdListRequest;
import kl.npki.base.management.model.inspection.InspectionItemsResponse;
import kl.npki.base.management.model.inspection.InspectionRecordDetailResponse;
import kl.npki.base.management.model.inspection.InspectionRecordListRequest;
import kl.npki.base.management.model.inspection.InspectionRecordListResponse;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.base.service.repository.InspectionRecordRepositoryImpl;
import kl.npki.base.service.repository.entity.InspectionRecordDO;
import kl.npki.management.core.biz.inspection.model.InspectionGroupedResults;
import kl.npki.management.core.service.inspection.IInspectionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.DATA_IS_EMPTY_I18N_KEY;
import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 系统巡检控制器
 *
 * <AUTHOR>
 * @date 07/05/2025 19:49
 **/
@RestController
@RequestMapping("/inspection")
@Tag(name = "系统管理-系统巡检")
@LogCollector(resolver = OperationLogResolver.class)
public class InspectionMgmtController implements BaseController {

    @Resource
    private IInspectionService inspectionService;

    @Resource
    private InspectionRecordRepositoryImpl inspectionRecordRepository;

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateService validateService;

    @PutMapping("/execute")
    @Operation(summary = "执行系统巡检", description = EXECUTE_INSPECTION_I18N_KEY)
    public RestResponse<Long> executeInspection(@RequestParam String name) {
        // 执行巡检
        Long recordId = inspectionService.executeInspection(name);
        return RestResponse.success(recordId);
    }

    @GetMapping("/items")
    @Operation(summary = "查询巡检项列表项", description = QUERY_INSPECTION_ITEM_LIST_I18N_KEY)
    public RestResponse<InspectionItemsResponse> queryItems() {
        InspectionItemsResponse itemsResponse = new InspectionItemsResponse(inspectionService.getInspectionItems());
        return RestResponse.success(itemsResponse);
    }

    @GetMapping("/record/list")
    @Operation(summary = "查询巡检记录列表", description = QUERY_INSPECTION_RECORD_LIST_I18N_KEY)
    public RestResponse<IPage<InspectionRecordListResponse>> queryPageList(PageInfo pageInfo,
                                                                           InspectionRecordListRequest listRequest) {
        validateService.validate(listRequest);
        IPage<InspectionRecordDO> page = pageInfo.toPage();
        QueryWrapper<InspectionRecordDO> queryWrapper = listRequest.toQueryWrapper();

        // 获取排序字段，如果未指定则按照最后更新时间排序
        String sortField = listRequest.getSortField();
        queryWrapper.lambda().orderByDesc(StringUtils.isBlank(sortField), InspectionRecordDO::getUpdateTime);

        // 执行查询
        IPage<InspectionRecordDO> dbResponse = inspectionRecordRepository.page(page, queryWrapper);

        // 将DO转成列表响应
        IPage<InspectionRecordListResponse> responsePage = dbResponse.convert(
            k -> convertService.convert(k, InspectionRecordListResponse.class)
        );

        return RestResponse.success(responsePage);
    }

    @GetMapping("/record/detail/{id}")
    @Operation(summary = "查询巡检记录详情", description = QUERY_INSPECTION_RECORD_DETAIL_I18N_KEY)
    public RestResponse<InspectionRecordDetailResponse> queryRecordDetail(@PathVariable Long id) {
        InspectionRecordDO inspectionRecord = inspectionRecordRepository.getById(id);
        if (inspectionRecord == null) {
            throw ManagementInternalError.DB_QUERY_ERROR.toException(DATA_IS_EMPTY_I18N_KEY);
        }

        InspectionRecordDetailResponse detailResponse = new InspectionRecordDetailResponse();
        detailResponse.setId(inspectionRecord.getId());
        detailResponse.setName(inspectionRecord.getName());
        detailResponse.setCostMills(inspectionRecord.getCostMills());
        detailResponse.setStartTime(inspectionRecord.getStartTime());
        detailResponse.setEndTime(inspectionRecord.getEndTime());
        detailResponse.setCreateBy(inspectionRecord.getCreateBy());
        // 巡检结果
        List<InspectionGroupedResults> inspectionResults =
            inspectionService.groupInspectionResultsByType(inspectionRecord.getItemResults());
        detailResponse.setItemResults(inspectionResults);

        return RestResponse.success(detailResponse);
    }

    @DeleteMapping("/record/delete")
    @Operation(summary = "删除巡检记录", description = DELETE_INSPECTION_RECORD_I18N_KEY)
    public RestResponse<Void> deleteRecord(@RequestBody IdListRequest idListRequest) {
        validateService.validate(idListRequest);
        List<Long> ids = idListRequest.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return RestResponse.fail(ManagementValidationError.PARAM_ERROR.toException());
        }

        // 执行删除
        inspectionRecordRepository.removeBatchByIds(ids);

        return RestResponse.success();
    }

    @GetMapping("/record/export/{id}")
    @Operation(summary = "导出巡检报告", description = EXPORT_INSPECTION_RECORD_I18N_KEY)
    public ResponseEntity<byte[]> exportRecord(@PathVariable Long id, @RequestParam String reportType) {
        // 生成巡检报告
        InspectionReportInfo reportInfo = inspectionService.generateInspectionReport(id, reportType);
        CheckUtils.notNull(reportInfo, ManagementInternalError.DB_QUERY_ERROR.toException(DATA_IS_EMPTY_I18N_KEY));

        // 以HTTP附件下载方式返回文件
        return FileDownloadHelper.getResponseEntityByFile(reportInfo.getReportContent(), reportInfo.getReportName());
    }
}
