package kl.npki.base.management.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.auth.core.SpiMgr;
import kl.nbase.auth.sso.ISsoAuthSpi;
import kl.nbase.auth.sso.PreAuthnConfig;
import kl.nbase.auth.token.store.TokenStore;
import kl.nbase.auth.utils.SsoAuthUtils;
import kl.nbase.auth.utils.TokenUtils;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.configs.LoginConfig;
import kl.npki.base.core.configs.LoginTypeConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.LoginConstant;
import kl.npki.base.core.constant.LoginType;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.admin.request.LoginAdminNumRequest;
import kl.npki.base.management.model.admin.request.LoginRequest;
import kl.npki.base.management.model.admin.response.CaptchaPayloadResponse;
import kl.npki.base.management.model.admin.response.RandomResponse;
import kl.npki.base.management.utils.AuthzUtil;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.common.log.resolver.SecurityOperationLogResolver;
import kl.npki.management.core.biz.admin.model.LoginResponse;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.info.CaptchaPayloadInfo;
import kl.npki.management.core.biz.admin.model.info.LoginInfo;
import kl.npki.management.core.common.cache.AuthCache;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.service.auth.IAuthnService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 * @date 2022/6/23
 * @desc 认证控制器
 */
@RestController
@Tag(name = "认证控制器")
@LogCollector(resolver = SecurityOperationLogResolver.class, maskFields = {"password", "captcha"})
public class AuthenticateController implements BaseController {

    @Resource
    private ValidateHelper validateUtil;

    @Resource
    private IAuthnService authnService;

    @Resource
    private AdminCertEntity adminCertEntity;

    @Resource
    private ConvertService convertService;

    private final TokenStore tokenStore = SpiMgr.getInstance().getServices(TokenStore.class).get(0);

    private static final String DEPLOYER_NAME = "deployOper";

    /**
     * 获取登录签名数据原文
     *
     * @api {GET} /getRandom 获取登录签名数据原文
     * @apiDescription 获取登录签名数据原文
     * @apiPermission none
     * @apiGroup login
     * @apiName getRandom
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @GetMapping("/getRandom")
    @Operation(description = RETRIEVE_THE_ORIGINAL_LOGIN_SIGNATURE_DATA_I18N_KEY, summary = "获取登录签名数据原文")
    public RestResponse<RandomResponse> getRandom(HttpServletResponse response) {
        String authRefId = TokenUtils.generateToken();
        String random = TokenUtils.generateToken();
        // 缓存存储临时会话id和签名源数据
        AuthCache.INSTANCE.set(authRefId, random, LoginConstant.DEFAULT_SESSION_ID_TTL);
        // 清除认证token cookie
        AuthzUtil.removeAuthorization(response);
        RandomResponse randomResponse = new RandomResponse()
                .setAuthRefId(authRefId)
                .setRandom(random);
        return RestResponse.success(randomResponse);
    }

    /**
     * 多选登录模式下，需要插入几个管理员key
     *
     * @return
     */
    @GetMapping("/getLoginAdminNum")
    @Operation(description = OBTAIN_THE_REQUIRED_NUMBER_OF_ADMINISTRATORS_FOR_LOGIN_I18N_KEY, summary = "获取登录时所需管理员数量")
    public RestResponse<Integer> getLoginAdminNum(LoginAdminNumRequest loginAdminNumRequest) {
        return RestResponse.success(authnService.getLoginAdminNum(loginAdminNumRequest.getCertSn()));
    }

    /**
     * 管理员登录，key下拉列表展示时，只显示我们系统中存在的管理员证书,故将管理员签名证书Sn返回给前端进行过滤
     *
     * @return
     */
    @GetMapping("/getSignCertSnList")
    @Operation(description = OBTAIN_THE_SERIAL_NUMBER_OF_THE_ADMINISTRATOR_SIGNATURE_CERTIFICATE_I18N_KEY, summary = "获取管理员签名证书序列号")
    public RestResponse<List<String>> getAdminLoginSn() {
        List<String> signCertSnList = adminCertEntity.getSignCertSnList();
        return RestResponse.success(signCertSnList);
    }

    /**
     * SSL-client 登录 ，建立ssl连接，返回网关中的证书序列号
     *
     * @param request
     * @return
     */
    @PostMapping("/verifySSl")
    @Operation(description = VERIFY_SSL_CHANNEL_I18N_KEY, summary = "验证SSL通道")
    public RestResponse<String> verifySslCoon(HttpServletRequest request) {
        String certSn = authnService.verifySslClient(request);
        return RestResponse.success(certSn);
    }

    /**
     * 管理员登录
     *
     * @api {POST} /login 管理员登录
     * @apiDescription 管理员登录
     * @apiPermission none
     * @apiGroup login
     * @apiName login
     * @apiBody {String} signData 签名值
     * @apiBody {String} certSn 证书序列号
     * @apiParamExample {json} Request-Example:
     * {
     * "signData": "MEUCIFHrhRmW8Vd0FUY9AYgjTOOg8FEgx5y4EdcqgQfc8hjSAiEAvi2iZpHwVGHK6hg+D91Lvy5IUbvU9d6DMP45dlDFIAE=",
     * "certSn": "578400000000000000000097"
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @RequestMapping(value = "/login", method = {RequestMethod.GET, RequestMethod.POST})
    @Operation(description = ADMINISTRATOR_LOGIN_I18N_KEY, summary = "管理员登录")
    public RestResponse<LoginResponse> login(@RequestBody LoginRequest loginRequest, HttpServletRequest request, HttpServletResponse response) {
        // 参数检查
        validateUtil.validate(loginRequest);
        // 登录模式校验
        if (!loginRequest.getAuthnType().equalsIgnoreCase(BaseConfigWrapper.getLoginTypeConfig().getLoginModel())) {
            return RestResponse.fail(ManagementValidationError.INVALID_AUTH_TYPE.toException());
        }

        LoginConfig loginConfig = BaseConfigWrapper.getLoginConfig();
        boolean captchaCheckEnable = loginConfig.isCaptchaCheckEnabled();
        // 账密模式下 且验证码开关开启   则对图形验证码进行校验
        if (LoginType.PWD.getType().equals(loginRequest.getAuthnType()) && captchaCheckEnable) {
            // 部署操作员无需检验验证码
            String username = loginRequest.getUsername();
            if (!DEPLOYER_NAME.equals(username)) {
                authnService.verifyCaptcha(loginRequest.getAuthRefId(), loginRequest.getCaptcha());
            }
        }
        LoginInfo loginInfo = convertService.convert(loginRequest, LoginInfo.class);
        LoginResponse loginResponse = authnService.adminLogin(loginInfo, request);
        Set<String> roleIdSet = loginResponse.getAdminInfo().getRoleId();
        // 管理员角色不存在
        if (CollectionUtils.isEmpty(roleIdSet)) {
            return RestResponse.fail(ManagementValidationError.ADMIN_ROLE_NOT_EXISTS.toException());
        }
        // 司法恢复管理员不允许登录
        if (roleIdSet.size() == 1 && roleIdSet.contains(RoleCodeEnum.LAW_ADMIN.getRoleCode())) {
            return RestResponse.fail(ManagementValidationError.ADMIN_ROLE_NOT_EXISTS.toException());
        }
        AuthzUtil.setAuthorization(loginResponse.getToken(), response);
        // 回写UserId和userName用于记录审计日志
        if (Objects.nonNull(loginResponse.getAdminInfo()) && StringUtils.isNotBlank(loginResponse.getAdminInfo().getUserId())) {
            RequestContextHolder.getContext().setUserId(Long.parseLong(loginResponse.getAdminInfo().getUserId()));
            RequestContextHolder.getContext().setUsername(loginResponse.getAdminInfo().getUsername());
        }
        return RestResponse.success(loginResponse);
    }

    /**
     * 获取单点登录认证平台信息
     * <p>
     *     如果单点登录失败，则前端会调用此接口跳转到SSO认证平台的地址
     * </p>
     *
     * @return 单点登录认证前置配置信息，例如单点登录认证平台的地址
     */
    @GetMapping("/getPreSsoAuthConf")
    @Operation(description = SSO_AUTH_ADDR_LOGIN_I18N_KEY, summary = "获取单点登录认证平台信息")
    public RestResponse<PreAuthnConfig> getPreSsoAuthConf() {
        LoginTypeConfig loginTypeConfig = BaseConfigWrapper.getLoginTypeConfig();
        String loginModel = loginTypeConfig.getLoginModel();
        ISsoAuthSpi ssoAuth = SsoAuthUtils.getSsoAuthSpiByType(loginModel.toLowerCase());
        PreAuthnConfig preAuthConfig = ssoAuth.getPreAuthConfig();
        return RestResponse.success(preAuthConfig);
    }


    /**
     * 注销登录接口
     *
     * @return
     * @throws
     */
    @GetMapping("/logout")
    @LogCollector(resolver = SecurityOperationLogResolver.class)
    @Operation(description = LOG_OUT_OF_LOGIN_I18N_KEY, summary = "注销登录")
    public RestResponse<String> logout(HttpServletRequest request, HttpServletResponse response) {
        tokenStore.delete(AuthzUtil.getAuthorization(request));
        AuthzUtil.removeAuthorization(response);
        return RestResponse.success();
    }

    /**
     * 生成验证码图片并返回Base64编码
     * @return base64的图形验证码字符串
     */
    @GetMapping("/getCaptcha")
    @Operation(
        summary = "获取验证码图片Base64编码",
        description = GET_CAPTCHA_I18N_KEY
    )
    public RestResponse<CaptchaPayloadResponse> getCaptcha() {
        CaptchaPayloadInfo loginCaptcha = authnService.getLoginCaptcha();
        return RestResponse.success(convertService.convert(loginCaptcha, CaptchaPayloadResponse.class));
    }


}