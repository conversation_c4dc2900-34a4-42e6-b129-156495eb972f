package kl.npki.base.management.controller.administrator;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.log.request.AuditLogRequest;
import kl.npki.base.management.model.log.response.AuditLogResponse;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.AuditExemptLogResolver;
import kl.npki.base.service.repository.entity.TOpLogDO;
import kl.npki.base.service.repository.service.IOpLogRepositoryService;
import kl.npki.management.core.biz.log.model.AuditLogInfo;
import kl.npki.management.core.service.log.IAuditLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 * @date 2022/8/31
 * @desc 审计员管理
 */
@RestController
@RequestMapping("audit")
@Tag(name = "审计员管理")
public class AuditController implements BaseController {

    @Resource
    private ConvertService convertService;

    @Resource
    private IAuditLogService auditLogService;

    @Resource
    private IOpLogRepositoryService opLogRepository;

    @Resource
    private ValidateHelper validateHelper;

    /**
     * 审计日志列表查询
     *
     * @return
     */
    @GetMapping("/auditLogList")
    @Operation(description = AUDIT_LOG_LIST_QUERY_I18N_KEY, summary = "审计日志列表查询")
    public RestResponse<IPage<AuditLogResponse>> auditLogList(PageInfo pageInfo, AuditLogRequest auditLogRequest) {
        validateHelper.validate(auditLogRequest);
        QueryWrapper<TOpLogDO> queryWrapper = auditLogRequest.toQueryWrapper();
        // 排除不需要审计的日志
        queryWrapper.lambda().and(wrapper -> wrapper
            .ne(TOpLogDO::getAuditStatus, AuditStatusEnum.NO_AUDIT.getCode()));
        IPage<TOpLogDO> page = pageInfo.toPage();
        queryWrapper.lambda().orderByDesc(TOpLogDO::getLogWhen);
        IPage<TOpLogDO> auditLogPage = opLogRepository.page(page, queryWrapper);
        IPage<AuditLogResponse> auditLogResponsePage = auditLogPage.convert(opLogDO -> convertService.convert(opLogDO, AuditLogResponse.class));
        return RestResponse.success(auditLogResponsePage);
    }

    /**
     * 日志审计
     *
     * @return
     */
    @PostMapping("/audit/{logId}")
    @Operation(description = LOG_AUDIT_I18N_KEY, summary = "日志审计")
    @LogCollector(resolver = AuditExemptLogResolver.class)
    public RestResponse<Boolean> audit(@PathVariable("logId") Long logId) {
        boolean auditLog = auditLogService.auditLog(logId);
        return RestResponse.success(auditLog);
    }

    /**
     * 审计日志详情查询
     *
     * @return
     */
    @GetMapping("/auditLogDetail/{logId}")
    @Operation(description = AUDIT_LOG_DETAILS_QUERY_I18N_KEY, summary = "审计日志详情查询")
    public RestResponse<AuditLogResponse> auditLogDetail(@PathVariable("logId") Long logId) {
        AuditLogInfo auditLogInfo = auditLogService.getAuditLogResponse(logId);
        AuditLogResponse auditLogResponse = convertService.convert(auditLogInfo, AuditLogResponse.class);
        return RestResponse.success(auditLogResponse);
    }
}