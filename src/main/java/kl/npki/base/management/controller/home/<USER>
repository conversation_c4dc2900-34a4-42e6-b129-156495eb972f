
/**
 * @projectName npki-base-management
 * @package kl.npki.base.management.controller.home
 * @className kl.npki.base.management.controller.home.HomePageController
 */
package kl.npki.base.management.controller.home;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.npki.base.core.biz.home.model.SelfCheckStatisticInfo;
import kl.npki.base.core.biz.home.service.SelfCheckStatisticService;
import kl.npki.base.service.common.RestResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static kl.npki.base.management.constant.I18nOperationLogConstant.SERVICE_SELF_INSPECTION_STATISTICS_RESULTS_I18N_KEY;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/22 16:47
 * @description
 */
@RestController
@RequestMapping("home")
@Tag(name = "首页统计")
public class BaseHomePageController {

    @Resource
    private SelfCheckStatisticService selfCheckStatisticService;

    @GetMapping("/selfCheckStatistic")
    @Operation(description = SERVICE_SELF_INSPECTION_STATISTICS_RESULTS_I18N_KEY, summary = "服务自检统计结果")
    public RestResponse<SelfCheckStatisticInfo> showsSelfCheckStatistic() {
        SelfCheckStatisticInfo selfCheckStatistic = selfCheckStatisticService.getSelfCheckStatistic();
        return RestResponse.success(selfCheckStatistic);
    }
}