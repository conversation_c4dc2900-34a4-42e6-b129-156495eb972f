package kl.npki.base.management.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.log.annotation.JsonFieldFilter;
import kl.nbase.log.annotation.JsonFieldFilters;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.cache.CacheConfigRequest;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.config.model.CacheConfigInfo;
import kl.npki.management.core.service.config.CacheConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * @Author: guoq
 * @Date: 2024/1/29
 * @description: 缓存配置
 */
@RestController
@RequestMapping("/config")
@Tag(name = "缓存配置")
@LogCollector(resolver = OperationLogResolver.class, maskFields = {"username", "password", "sentinelPassword"})
public class CacheConfigController implements BaseController {

    @Resource
    private CacheConfigService cacheConfigService;

    @Resource
    private ValidateService validateService;

    @Resource
    private ConvertService convertService;

    @GetMapping("/cache")
    @Operation(description = QUERY_CACHE_CONFIGURATION_I18N_KEY, summary = "查询缓存配置")
    @JsonFieldFilters(value = {@JsonFieldFilter(exclude = {"password"}, value = CacheConfigInfo.class, type = JsonFieldFilter.JacksonFilterType.RESPONSE)})
    public RestResponse<CacheConfigInfo> queryDbConfig() {
        return RestResponse.success(cacheConfigService.queryCacheConfig());
    }

    /**
     * 保存缓存配置信息
     *
     * @param cacheConfigRequest
     * @return
     */
    @PostMapping("/cache")
    @Operation(description = SAVE_CACHE_CONFIGURATION_I18N_KEY, summary = "保存缓存配置")
    public RestResponse<Void> addDbConfig(@RequestBody CacheConfigRequest cacheConfigRequest) {
        if (cacheConfigRequest.isEnabled()) {
            validateService.validate(cacheConfigRequest);
        }
        CacheConfigInfo configInfo = convertService.convert(cacheConfigRequest, CacheConfigInfo.class);
        cacheConfigService.saveCacheConfig(configInfo);
        return RestResponse.success();
    }

    /**
     * 缓存配置信息测试
     *
     * @param cacheConfigRequest
     * @return
     */
    @PostMapping("/cache/test")
    @Operation(description = TEST_CACHE_CONNECTION_I18N_KEY, summary = "测试缓存连接")
    public RestResponse<Void> testDbConnection(@RequestBody CacheConfigRequest cacheConfigRequest) {
        if (cacheConfigRequest.isEnabled()) {
            validateService.validate(cacheConfigRequest);
            CacheConfigInfo configInfo = convertService.convert(cacheConfigRequest, CacheConfigInfo.class);
            cacheConfigService.testCacheConnection(configInfo);
        }
        return RestResponse.success();
    }
}