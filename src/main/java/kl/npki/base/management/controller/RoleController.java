package kl.npki.base.management.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.permission.request.AddRoleRequest;
import kl.npki.base.management.model.permission.request.PermissionTreeSaveRequest;
import kl.npki.base.management.model.permission.request.RoleListRequest;
import kl.npki.base.management.repository.entity.TRoleDO;
import kl.npki.base.management.repository.impl.RoleMgrRepositoryImpl;
import kl.npki.base.management.repository.impl.RoleRepositoryImpl;
import kl.npki.base.management.service.impl.UrlRoleCacheService;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import kl.npki.management.core.biz.permission.response.RoleListResponse;
import kl.npki.management.core.service.SecurityService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <p>
 * 角色列表
 * 角色权限绑定
 * 角色新增
 * </p>
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@RestController
@RequestMapping("/role")
@Tag(name = "角色管理")
@LogCollector(resolver = OperationLogResolver.class)
public class RoleController implements BaseController {

    @Resource
    private SecurityService securityService;

    @Resource
    private UrlRoleCacheService urlRoleCacheService;

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateHelper validateUtil;

    @Resource
    private RoleMgrRepositoryImpl roleMgrRepository;

    @Resource
    private RoleRepositoryImpl roleRepository;

    /**
     * 新增角色
     *
     * @api {POST} /security/addRole 新增角色
     * @apiDescription 新增角色
     * @apiPermission 安全管理员
     * @apiGroup security
     * @apiName addRole
     * @apiBody {String} roleName 角色名
     * @apiBody {String} [remark] 备注
     * @apiParamExample {json} Request-Example:
     * {
     * "roleName": "张三",
     * "remark": "老师"
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PostMapping("/addRole")
    @Operation(description = ADD_NEW_ROLE_I18N_KEY, summary = "新增角色")
    public RestResponse<String> addRole(@RequestBody AddRoleRequest addRoleRequest) {
        // 参数检查
        validateUtil.validate(addRoleRequest);
        CheckUtils.isNumeric(addRoleRequest.getRoleCode(), ManagementValidationError.ROLE_CODE_IS_NOT_NUMERIC.toException());
        // 校验角色编码不能等于父级角色编码
        CheckUtils.isTrue(!StringUtils.equals(addRoleRequest.getRoleCode(), addRoleRequest.getParentRoleCode()), ManagementValidationError.ROLE_CODE_CANNOT_EQUALS_PARENT_CODE.toException());
        RoleEntity roleEntity = convertService.convert(addRoleRequest, RoleEntity.class);
        // 记录创建者
        Long userId = RequestContextHolder.getContext().getUserId();
        roleEntity.setCreateBy(userId);
        securityService.addRole(roleEntity);
        return RestResponse.success();
    }

    /**
     * 删除角色
     *
     * @api {DELETE} /security/deleteRole/:roleId 删除角色
     * @apiDescription 删除角色
     * @apiPermission 安全管理员
     * @apiGroup security
     * @apiName deleteRole
     * @apiParam {Number} roleId 角色id
     * @apiParamExample {json} Request-Example:
     * {
     * "roleId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @DeleteMapping("/deleteRole/{roleId}")
    @Operation(description = DISABLE_ROLE_I18N_KEY, summary = "禁用角色")
    public RestResponse<String> deleteRole(@PathVariable("roleId") Long roleId) {
        securityService.deleteRole(roleId);
        urlRoleCacheService.refreshUrlRolesCache();
        return RestResponse.success();
    }

    /**
     * 所有角色列表查询
     *
     * @api {GET} /security/roleList 所有角色列表查询
     * @apiDescription 所有角色列表查询
     * @apiPermission 安全管理员
     * @apiGroup security
     * @apiName roleList
     * @apiUse page
     * @apiParam {string} [roleName] 角色名
     * @apiParam {Number} [is_custom] 角色类型, 1:自定义角色, 0:系统角色
     * @apiParam {string} [roleName] 角色名
     * @apiParamExample {json} Request-Example:
     * {
     * "currentPage": 1,
     * "pageSize": 10,
     * "is_custom": 1,
     * "roleName": "业务管理员",
     * "leCreateAt": "2022-10-09 10:00:50",
     * "geCreateAt": "2022-10-10 10:00:50",
     * "quick":true
     * <p>
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @GetMapping("/roleList")
    @Operation(description = ROLE_LIST_QUERY_I18N_KEY, summary = "角色列表查询")
    public RestResponse<IPage<RoleListResponse>> roleList(PageInfo pageInfo, RoleListRequest roleListRequest) {
        // 参数检查
        validateUtil.validate(roleListRequest);
        IPage<TRoleDO> page = pageInfo.toPage();
        QueryWrapper<TRoleDO> queryWrapper = roleListRequest.toQueryWrapper();
        IPage<RoleListResponse> query = roleMgrRepository.queryRoleList(page, queryWrapper);
        return RestResponse.success(query);
    }


    /**
     * 保存角色权限树
     *
     * @api {POST} /security/savePermissionTree 保存角色权限树
     * @apiDescription 保存角色权限树
     * @apiPermission 安全管理员
     * @apiGroup security
     * @apiName savePermissionTree
     * @apiBody {Number[]} chooseList 选中id集合
     * @apiBody {Number} roleId 角色id
     * @apiBody {String} roleName 角色名称
     * @apiBody {String} [remark] 备注
     * @apiParamExample {json} Request-Example:
     * {
     * "chooseList": [
     * 1,2,3
     * ],
     * "roleId": 123,
     * "roleName": "张三",
     * "remark":"老师"
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PostMapping("/savePermissionTree")
    @Operation(description = SAVE_ROLE_PERMISSION_TREE_I18N_KEY, summary = "保存角色权限树")
    public RestResponse<Void> savePermissionTree(@RequestBody PermissionTreeSaveRequest permissionTreeSaveRequest) {
        permissionTreeSaveRequest.setRemark(null); // 角色权限树保存时不需要备注字段
        // 参数检查
        validateUtil.validate(permissionTreeSaveRequest);
        securityService.savePermissionTree(RequestContextHolder.getContext().getUserId(), permissionTreeSaveRequest.getRoleCode(), permissionTreeSaveRequest.getRoleName(), permissionTreeSaveRequest.getChooseList());
        urlRoleCacheService.refreshUrlRolesCache();
        return RestResponse.success();
    }

    /**
     * 获取角色的统一资源权限
     */
    @GetMapping("/{roleCode}/unifiedResources")
    @Operation(
        description = GET_ROLE_UNIFIED_RESOURCES_I18N_KEY,
        summary = "获取角色的统一资源权限"
    )
    public RestResponse<List<String>> getRoleUnifiedResources(@PathVariable String roleCode) {
        return RestResponse.success(roleRepository.getChosenResourceCodesByRoleCode(roleCode));
    }

}