package kl.npki.base.management.controller.administrator;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.auth.core.SpiMgr;
import kl.nbase.auth.token.store.TokenStore;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.common.org.OrgCache;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.management.common.cache.AdminIdBlocklistCache;
import kl.npki.base.management.common.cache.AdminIdCache;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.admin.request.*;
import kl.npki.base.management.model.admin.response.AdminCertStatusResponse;
import kl.npki.base.management.model.admin.response.AdminInfoDetailResponse;
import kl.npki.base.management.model.admin.response.AdminInfoListResponse;
import kl.npki.base.management.model.admin.response.AdminUserStatusResponse;
import kl.npki.base.management.model.cert.request.ImportCertChainRequest;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.common.log.resolver.SecurityOperationLogResolver;
import kl.npki.base.service.util.EnvironmentUtil;
import kl.npki.management.core.biz.admin.model.dto.AdminInfoListDTO;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import kl.npki.management.core.biz.admin.model.info.AdminInfoDetailInfo;
import kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo;
import kl.npki.management.core.biz.cert.model.ImportAdminCertInfo;
import kl.npki.management.core.biz.cert.model.ImportExtendAdminCertInfo;
import kl.npki.management.core.biz.permission.response.RoleListResponse;
import kl.npki.management.core.biz.permission.response.RoleResponse;
import kl.npki.management.core.constants.AdminGroupEnum;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.repository.IAdminCertRepository;
import kl.npki.management.core.repository.IAdminInfoMgrRepository;
import kl.npki.management.core.repository.IRoleRepository;
import kl.npki.management.core.service.AdminMgrService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.*;
import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 * @date 2022/8/24
 * @desc 超级管理员、审计管理员、安全管理员 管理
 * @apiDefine FailResponse
 * @apiErrorExample RestResponse (fail):
 * {
 * "code": "30000510B000", // 错误码
 * "message": "远端服务错误", // 错误描述
 * "traceId": "a101a65e-e19a-4bb7-ace1-79ac61f3ed35", // 链路id
 * "timestamp": 1661321323225 // 服务器的时间戳
 * }
 * @apiDefine SuccessResponse
 * @apiSuccessExample RestResponse (success):
 * {
 * "code": "0", // 正常响应code=0
 * "traceId": "85bdb539-02e3-41ae-9d95-82eadf3a820f", // 链路id
 * "timestamp": 1661310184772, // 服务器的时间戳
 * "data": "success" // 具体对象
 * }
 * @apiDefine page
 * @apiParam {Number} currentPage 当前页
 * @apiParam {Number} pageSize 页大小
 * @apiParam {boolean} [quick] 是否快速查询,快速查询-查询条件结果取并集
 * @apiParam {Date} [leCreateAt] 创建时间-开始,yyyy-MM-dd HH:mm:ss
 * @apiParam {Date} [geCreateAt] 创建时间-结束,yyyy-MM-dd HH:mm:ss
 * @apiDefine FailResponse
 * @apiErrorExample RestResponse (fail):
 * {
 * "code": "30000510B000", // 错误码
 * "message": "远端服务错误", // 错误描述
 * "traceId": "a101a65e-e19a-4bb7-ace1-79ac61f3ed35", // 链路id
 * "timestamp": 1661321323225 // 服务器的时间戳
 * }
 * @apiDefine SuccessResponse
 * @apiSuccessExample RestResponse (success):
 * {
 * "code": "0", // 正常响应code=0
 * "traceId": "85bdb539-02e3-41ae-9d95-82eadf3a820f", // 链路id
 * "timestamp": 1661310184772, // 服务器的时间戳
 * "data": "success" // 具体对象
 * }
 * @apiDefine page
 * @apiParam {Number} currentPage 当前页
 * @apiParam {Number} pageSize 页大小
 * @apiParam {boolean} [quick] 是否快速查询,快速查询-查询条件结果取并集
 * @apiParam {Date} [leCreateAt] 创建时间-开始,yyyy-MM-dd HH:mm:ss
 * @apiParam {Date} [geCreateAt] 创建时间-结束,yyyy-MM-dd HH:mm:ss
 */
/**
 * @apiDefine FailResponse
 * @apiErrorExample RestResponse (fail):
 * {
 * "code": "30000510B000", // 错误码
 * "message": "远端服务错误", // 错误描述
 * "traceId": "a101a65e-e19a-4bb7-ace1-79ac61f3ed35", // 链路id
 * "timestamp": 1661321323225 // 服务器的时间戳
 * }
 */
/**
 * @apiDefine SuccessResponse
 * @apiSuccessExample RestResponse (success):
 * {
 * "code": "0", // 正常响应code=0
 * "traceId": "85bdb539-02e3-41ae-9d95-82eadf3a820f", // 链路id
 * "timestamp": 1661310184772, // 服务器的时间戳
 * "data": "success" // 具体对象
 * }
 */
/**
 * @apiDefine page
 * @apiParam {Number} currentPage 当前页
 * @apiParam {Number} pageSize 页大小
 * @apiParam {boolean} [quick] 是否快速查询,快速查询-查询条件结果取并集
 * @apiParam {Date} [leCreateAt] 创建时间-开始,yyyy-MM-dd HH:mm:ss
 * @apiParam {Date} [geCreateAt] 创建时间-结束,yyyy-MM-dd HH:mm:ss
 */

/**
 * <AUTHOR>
 * @date 2022/9/29
 */
@RestController
@RequestMapping("admin")
@Tag(name = "管理员管理")
@LogCollector(resolver = SecurityOperationLogResolver.class, maskFields = {"oldPwdHash", "newPwdHash", "passwordHash", "certValue", "email"})
public class AdminMgrController implements BaseController {

    @Resource
    private AdminMgrService adminMgrService;

    @Resource
    private IAdminInfoMgrRepository adminMgrRepostiry;

    @Resource
    private IAdminCertRepository adminCertRepository;

    @Resource
    private IRoleRepository roleRepository;

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateHelper validateHelper;

    private final TokenStore tokenStore = SpiMgr.getInstance().getServices(TokenStore.class).get(0);

    /**
     * @api {POST} /admin/registerAdmin 注册管理员
     * @apiDescription 注册管理员
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName registerAdmin
     * @apiBody {Number} roleId 角色id
     * @apiBody {String} subjectCn 用户名
     * @apiBody {String} [organization]  组织
     * @apiBody {String} [organizationUnit]  机构
     * @apiBody {String} [province] 省
     * @apiBody {String} [city]  市
     * @apiBody {String} email 邮箱
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PostMapping("/registerAdmin")
    @Operation(description = REGISTRATION_ADMINISTRATOR_I18N_KEY, summary = "注册管理员")
    public RestResponse<Void> registerAdmin(@RequestBody RegisterAdminRequest registerAdminRequest) {
        // 参数检查
        validateHelper.validate(registerAdminRequest, registerAdminRequest.getRoleCode());
        AdminEntity adminEntity = convertService.convert(registerAdminRequest, AdminEntity.class);
        AdminCertEntity adminCertEntity = convertService.convert(registerAdminRequest, AdminCertEntity.class);
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet) || !roleCodeSet.contains(registerAdminRequest.getRoleCode())) {
            throw ManagementValidationError.NO_PERMISSION.toException(NO_PERMISSION_TO_REGISTER_AS_AN_ADMINISTRATOR_FOR_THIS_TYPE_I18N_KEY);
        }
        adminMgrService.register(adminEntity, adminCertEntity);
        return RestResponse.success();
    }

    @GetMapping("/operableRoleList")
    @Operation(description = QUERY_THE_LIST_OF_OPERABLE_ROLES_I18N_KEY, summary = "查询可操作的角色列表")
    public RestResponse<List<RoleResponse>> queryOperableRoleList() {
        Set<String> loginUserRoles = RequestContextHolder.getLoginUserRoles();
        // 可操作的角色列表
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(loginUserRoles);
        // 可操作的角色列表为空
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            return RestResponse.success(Collections.emptyList());
        }
        List<RoleResponse> roleResponseList = roleCodeSet.stream()
            // 管理员角色code排序，从小到大
            .sorted(Comparator.comparingInt(Integer::parseInt))
            .map(roleCode -> {
                RoleResponse roleResponse = new RoleResponse();
                roleResponse.setRoleCode(roleCode);
                roleResponse.setRoleName(roleRepository.getRoleNameByCode(roleCode));
                return roleResponse;
            }).collect(Collectors.toList());

        return RestResponse.success(roleResponseList);
    }

    /**
     * 导入管理员证书
     *
     * @api {POST} /admin/importCert 导入管理员证书
     * @apiDescription 导入管理员证书
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName importCert
     * @apiBody {Number} roleId 角色id
     * @apiBody {String[]} certs 证书集合
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PostMapping("/importCert")
    @Operation(description = IMPORT_ADMINISTRATOR_CERTIFICATE_I18N_KEY, summary = "导入管理员证书")
    public RestResponse<Void> importCert(@RequestBody ImportAdminCertRequest certRequest) {
        // 参数检查
        validateHelper.validate(certRequest);
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        roleCodeSet = appendPermissionByDnv(roleCodeSet);
        if (CollectionUtils.isEmpty(roleCodeSet) || !roleCodeSet.contains(certRequest.getRoleCode())) {
            throw ManagementValidationError.NO_PERMISSION.toException(NO_PERMISSION_TO_IMPORT_THIS_TYPE_OF_ADMINISTRATOR_I18N_KEY);
        }
        ImportAdminCertInfo importAdminCertInfo = convertService.convert(certRequest, ImportAdminCertInfo.class);
        adminMgrService.importCert(importAdminCertInfo);
        return RestResponse.success();
    }


    @PostMapping("/importExtendCert")
    @Operation(description = IMPORT_EXTENDED_ADMINISTRATOR_CERTIFICATE_I18N_KEY, summary = "导入延期管理员证书")
    public RestResponse<Void> importExtendCert(@RequestBody ImportExtendAdminCertRequest certRequest) {
        // 参数检查
        validateHelper.validate(certRequest);
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet) || !roleCodeSet.contains(certRequest.getRoleCode())) {
            throw ManagementValidationError.NO_PERMISSION.toException(NO_PERMISSION_TO_IMPORT_THIS_TYPE_OF_ADMINISTRATOR_I18N_KEY);
        }
        ImportExtendAdminCertInfo importExtendAdminCertInfo = convertService.convert(certRequest, ImportExtendAdminCertInfo.class);
        adminMgrService.importExtendCert(importExtendAdminCertInfo);
        return RestResponse.success();
    }

    /**
     * 请求废除证书
     *
     * @api {POST} /admin/requestRevokeCert/:adminInfoId 请求废除证书
     * @apiDescription 请求废除证书
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName requestRevokeCert
     * @apiParam {Number} adminInfoId 管理员信息id
     * @apiParamExample {json} Request-Example:
     * {
     * "adminInfoId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PutMapping("/requestRevokeCert/{adminInfoId}")
    @Operation(description = REQUEST_TO_REVOKE_CERTIFICATE_I18N_KEY, summary = "请求废除证书")
    public RestResponse<Void> requestRevokeCert(@PathVariable(value = "adminInfoId") Long adminInfoId) {
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            throw ManagementValidationError.NO_PERMISSION.toException(NO_PERMISSION_TO_REQUEST_REVOCATION_OF_THIS_TYPE_OF_ADMINISTRATOR_CERTIFICATE_I18N_KEY);
        }
        adminMgrService.requestRevokeCert(adminInfoId, roleCodeSet);
        return RestResponse.success();
    }

    /**
     * 废除证书
     *
     * @api {DELETE} /admin/requestRevokeCert/:adminInfoId 废除证书
     * @apiDescription 废除证书
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName revokeCert
     * @apiParam {Number} adminInfoId 管理员信息id
     * @apiParamExample {json} Request-Example:
     * {
     * "adminInfoId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PutMapping("/revokeCert/{adminInfoId}")
    @Operation(description = ABOLISH_CERTIFICATE_I18N_KEY, summary = "废除证书")
    public RestResponse<Void> revokeCert(@PathVariable(value = "adminInfoId") Long adminInfoId) {
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            throw ManagementValidationError.NO_PERMISSION.toException(NO_PERMISSION_TO_REQUEST_REVOCATION_OF_THIS_TYPE_OF_ADMINISTRATOR_CERTIFICATE_I18N_KEY);
        }
        adminMgrService.revokeCert(adminInfoId, roleCodeSet);
        // 如果该用户处于登录状态，需注销对应的会话,注: JWT模式下无效
        String token = AdminIdCache.INSTANCE.get(String.valueOf(adminInfoId));
        tokenStore.delete(token);
        // 将管理员ID加入黑名单
        long ttl = BaseConfigWrapper.getLoginConfig().getAccessTokenLifecycle();
        AdminIdBlocklistCache.INSTANCE.add(String.valueOf(adminInfoId), ttl);
        return RestResponse.success();
    }

    @PutMapping("/cancelSuper/{adminInfoId}")
    @Operation(description = ABOLISH_SUPER_ADMINISTRATORS_I18N_KEY, summary = "废除顶级管理员")
    public RestResponse<Void> cancelSuperAdmin(@PathVariable(value = "adminInfoId") Long adminInfoId) {
        Set<String> roleCodeSet = RequestContextHolder.getLoginUserRoles();
        String loginUserId = RequestContextHolder.getLoginUserId();
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            throw ManagementValidationError.NO_PERMISSION.toException(NO_PERMISSION_TO_REQUEST_REVOCATION_OF_THIS_TYPE_OF_ADMINISTRATOR_CERTIFICATE_I18N_KEY);
        }
        if (adminInfoId.toString().equals(RequestContextHolder.getLoginUserId())) {
            throw ManagementValidationError.NO_PERMISSION.toException(DO_NOT_ALLOW_ADMINISTRATORS_TO_ABOLISH_THEMSELVES_I18N_KEY);
        }
        adminMgrService.cancelSuperAdmin(adminInfoId, roleCodeSet, loginUserId);
        // 如果该用户处于登录状态，需注销对应的会话,注: JWT模式下无效
        String token = AdminIdCache.INSTANCE.get(String.valueOf(adminInfoId));
        tokenStore.delete(token);
        // 将管理员ID加入黑名单
        long ttl = BaseConfigWrapper.getLoginConfig().getAccessTokenLifecycle();
        AdminIdBlocklistCache.INSTANCE.add(String.valueOf(adminInfoId), ttl);
        return RestResponse.success();
    }

    /**
     * 撤销证书废除申请
     *
     * @api {put} /admin/cancelCertRevoke/:adminInfoId 撤销证书废除申请
     * @apiDescription 撤销证书废除申请
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName cancelCertRevoke
     * @apiParam {Number} adminInfoId 管理员信息id
     * @apiParamExample {json} Request-Example:
     * {
     * "adminInfoId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PutMapping("/cancelCertRevoke/{adminInfoId}")
    @Operation(description = REVOCATION_OF_CERTIFICATE_REVOCATION_APPLICATION_I18N_KEY, summary = "撤销证书废除申请")
    public RestResponse<Void> cancelCertRevoke(@PathVariable(value = "adminInfoId") Long adminInfoId) {
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            throw ManagementValidationError.NO_PERMISSION.toException(DO_NOT_HAVE_THE_AUTHORITY_TO_REVOKE_THE_ADMINISTRATOR_CERTIFICATE_REVOCATION_APPLICATION_FOR_THIS_TYPE_I18N_KEY);
        }
        adminMgrService.cancelCertRevoke(adminInfoId, roleCodeSet);
        return RestResponse.success();
    }

    /**
     * 查询系统管理员列表()
     *
     * @api {GET} /admin/adminList 查询系统管理员列表
     * @apiDescription 查询系统管理员列表
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName adminList
     * @apiUse page
     * @apiParam {String} [username] 管理员姓名
     * @apiParam {String} [roleName] 角色名称
     * @apiParam {Number} [status] 用户状态,
     * @apiParam {Number} [certStatus] 证书状态,
     * @apiParam {String} [issuerCn] 颁发者名称
     * @apiParamExample {json} Request-Example:
     * {
     * "currentPage": 1,
     * "pageSize": 10,
     * "username": "张三",
     * "roleName": "业务管理员",
     * "status": 1,
     * "certStatus": 0,
     * "leCreateAt": "2022-10-09 10:00:50",
     * "geCreateAt": "2022-10-10 10:00:50",
     * "issuerCn": "超级管理员",
     * "quick":true
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @GetMapping("/adminList")
    @Operation(description = QUERY_THE_LIST_OF_SYSTEM_ADMINISTRATORS_I18N_KEY, summary = "查询系统管理员列表")
    public RestResponse<IPage<AdminInfoListResponse>> adminList(PageInfo pageInfo, AdminInfoListRequest adminInfoListRequest) {
        validateHelper.validate(adminInfoListRequest);
        AdminInfoListDTO convert = convertService.convert(adminInfoListRequest, AdminInfoListDTO.class);
        IPage<AdminInfoListInfo> page = pageInfo.toPage();
        Set<String> loginUserRoles = RequestContextHolder.getLoginUserRoles();
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(loginUserRoles);
        appendPermissionByDnv(roleCodeSet);

        if (CollectionUtils.isEmpty(roleCodeSet)) {
            return RestResponse.success();
        }
        IPage<AdminInfoListInfo> adminInfoPages = adminMgrRepostiry.queryForList(page, convert, roleCodeSet);
        IPage<AdminInfoListResponse> listResponseIPage = adminInfoPages.convert(this::convertAdminInfoList);
        return RestResponse.success(listResponseIPage);
    }

    /**
     * 备份管理员列表
     *
     * @param pageInfo
     * @param adminInfoListRequest
     * @return
     */
    @GetMapping("/backup/adminList")
    @Operation(description = QUERY_THE_LIST_OF_BACKUP_ADMINISTRATORS_I18N_KEY, summary = "查询备份管理员列表")
    public RestResponse<IPage<AdminInfoListResponse>> backupAdminList(PageInfo pageInfo, AdminInfoListRequest adminInfoListRequest) {
        validateHelper.validate(adminInfoListRequest);
        AdminInfoListDTO convert = convertService.convert(adminInfoListRequest, AdminInfoListDTO.class);
        IPage<AdminInfoListInfo> page = pageInfo.toPage();
        Set<String> loginUserRoles = RequestContextHolder.getLoginUserRoles();
        if (!RoleCodeEnum.getDeployedRoleCodeEnum().containsAll(loginUserRoles)) {
            throw ManagementValidationError.QUERY_BACKUP_ADMIN_ERROR.toException();
        }
        // 查询另一组的管理员
        String loginUserId = RequestContextHolder.getLoginUserId();
        AdminEntity loginAdmin = adminMgrRepostiry.getById(Long.valueOf(loginUserId));
        AdminGroupEnum otherGroup = AdminGroupEnum.getOtherGroup(loginAdmin.getAdminGroup());
        convert.setAdminGroup(otherGroup.getId());
        IPage<AdminInfoListInfo> adminInfoPages = adminMgrRepostiry.queryForList(page, convert, loginUserRoles);
        IPage<AdminInfoListResponse> listResponseIPage = adminInfoPages.convert(adminInfoPage -> convertService.convert(adminInfoPage, AdminInfoListResponse.class));
        return RestResponse.success(listResponseIPage);
    }

    /**
     * 系统管理员信息详情
     *
     * @api {GET} /admin/adminDetail 系统管理员信息详情
     * @apiDescription 系统管理员信息详情
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName adminDetail
     * @apiParam {Number} adminInfoId 管理员信息id
     * @apiParamExample {json} Request-Example:
     * {
     * "adminInfoId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @GetMapping("/adminDetail")
    @Operation(description = SYSTEM_ADMINISTRATOR_INFORMATION_DETAILS_I18N_KEY, summary = "系统管理员信息详情")
    public RestResponse<AdminInfoDetailResponse> adminDetail(@RequestParam("adminInfoId") Long adminInfoId) {
        Set<String> loginUserRoles = RequestContextHolder.getLoginUserRoles();
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(loginUserRoles, true);
        roleCodeSet = appendPermissionByDnv(roleCodeSet);
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            return RestResponse.success();
        }
        AdminInfoDetailInfo adminDetail = adminMgrRepostiry.queryAdminDetail(adminInfoId, roleCodeSet);
        AdminInfoDetailResponse detailResponse = convertAdminInfoDetail(adminDetail);
        TrustCertEntity manageCertEntity = MgrHolder.getManageCertMgr().getManageCertEntity();
        if (Objects.nonNull(manageCertEntity) && Objects.nonNull(manageCertEntity.getCertAlgo())) {
            detailResponse.setAsymAlgo(manageCertEntity.getCertAlgo().getAlgoName());
        }
        return RestResponse.success(detailResponse);
    }

    @GetMapping("/adminCertSn")
    @Operation(description = SYSTEM_ADMINISTRATOR_CERTIFICATE_SN_I18N_KEY, summary = "系统管理员证书SN")
    public RestResponse<String> adminCertSn(@RequestParam("adminInfoId") Long adminInfoId) {
        AdminCertEntity adminCertEntity = adminCertRepository.getByAdminInfoId(adminInfoId);
        return RestResponse.success(adminCertEntity == null ? "" : adminCertEntity.getSignCertSn());
    }

    @PutMapping("/requestCancelAdmin/{adminInfoId}")
    @Operation(description = APPLY_FOR_CANCELLATION_OF_ADMINISTRATOR_I18N_KEY, summary = "申请注销管理员")
    public RestResponse<Void> requestCancelAdmin(@PathVariable(value = "adminInfoId") Long adminInfoId) {
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            throw ManagementValidationError.NO_PERMISSION.toException(DO_NOT_HAVE_PERMISSION_TO_LOG_OUT_OF_THIS_TYPE_OF_ADMINISTRATOR_I18N_KEY);
        }
        adminMgrService.requestCancelAdmin(adminInfoId, roleCodeSet);
        return RestResponse.success();
    }

    /**
     * 注销管理员
     *
     * @api {DELETE} /admin/cancelAdmin/:adminInfoId 注销管理员
     * @apiDescription 注销管理员
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName cancelAdmin
     * @apiParam {Number} adminInfoId 管理员信息id
     * @apiParamExample {json} Request-Example:
     * {
     * "adminInfoId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @DeleteMapping("/cancelAdmin/{adminInfoId}")
    @Operation(description = CANCEL_ADMINISTRATOR_I18N_KEY, summary = "注销管理员")
    public RestResponse<Void> cancelAdmin(@PathVariable(value = "adminInfoId") Long adminInfoId) {
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            throw ManagementValidationError.NO_PERMISSION.toException(DO_NOT_HAVE_PERMISSION_TO_LOG_OUT_OF_THIS_TYPE_OF_ADMINISTRATOR_I18N_KEY);
        }
        adminMgrService.cancelAdmin(adminInfoId, roleCodeSet);
        // 如果该用户处于登录状态，需注销对应的会话,注: JWT模式下无效
        String token = AdminIdCache.INSTANCE.get(String.valueOf(adminInfoId));
        tokenStore.delete(token);
        // 将管理员ID加入黑名单
        long ttl = BaseConfigWrapper.getLoginConfig().getAccessTokenLifecycle();
        AdminIdBlocklistCache.INSTANCE.add(String.valueOf(adminInfoId), ttl);
        return RestResponse.success();
    }

    @PutMapping("/requestExtendAdmin/{adminInfoId}")
    @Operation(description = APPLY_FOR_EXTENSION_ADMINISTRATOR_I18N_KEY, summary = "申请延期管理员")
    public RestResponse<Void> requestExtendAdmin(@PathVariable(value = "adminInfoId") Long adminInfoId) {
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            throw ManagementValidationError.NO_PERMISSION.toException(NO_EXTENSION_OF_ADMINISTRATOR_PRIVILEGES_FOR_THIS_TYPE_I18N_KEY);
        }
        adminMgrService.requestExtendAdmin(adminInfoId, roleCodeSet);
        return RestResponse.success();
    }

    /**
     * 导入信任证书链
     *
     * @api {POST} /importCertChain 导入信任证书链
     * @apiDescription 导入信任证书链
     * @apiPermission 部署时
     * @apiGroup deploy
     * @apiName importCertChain
     * @apiBody {String[]} certs 证书集合
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PostMapping("/importCertChain")
    @Operation(description = IMPORT_CERTIFICATE_CHAIN_I18N_KEY, summary = "导入证书链")
    public RestResponse<Void> importCertChain(@RequestBody ImportCertChainRequest certChain) {
        // 参数检查
        validateHelper.validate(certChain);
        // 简单判断证书不重复
        List<String> collection = certChain.removeDuplicate();
        adminMgrService.importCertChain(collection);
        return RestResponse.success();
    }

    /**
     * 部署时签发管理员证书，获取内置的管理员列表
     *
     * @return
     */
    @GetMapping("/initRoleList")
    @Operation(description = DURING_DEPLOYMENT_RETRIEVE_THE_BUILT_IN_ADMINISTRATOR_LIST_I18N_KEY, summary = "部署时，获取内置的管理员列表")
    public RestResponse<List<RoleListResponse>> getInitRoleList() {
        List<RoleEntity> roleList = adminMgrService.availableRoleList();
        List<RoleListResponse> roleListResponses = convertService.convert(roleList, RoleListResponse.class);
        return RestResponse.success(roleListResponses);
    }

    @PostMapping("/updatePwd")
    @Operation(description = CHANGE_PASSWORD_I18N_KEY, summary = "修改密码")
    public RestResponse<Void> updatePwd(@RequestBody AdminUpdatePwdRequest adminUpdatePwdRequest) {
        adminMgrService.updatePwd(adminUpdatePwdRequest.getAdminInfoId(), adminUpdatePwdRequest.getOldPwdHash(), adminUpdatePwdRequest.getNewPwdHash());
        return RestResponse.success();
    }

    @PutMapping("/unlock/{adminInfoId}")
    @Operation(description = UNLOCK_THE_ACCOUNT_I18N_KEY, summary = "解除账号锁定")
    public RestResponse<Void> unlockAccount(@PathVariable(value = "adminInfoId") Long adminInfoId) {
        adminMgrService.unlockAccount(adminInfoId);
        return RestResponse.success();
    }

    @GetMapping("/userStatus")
    @Operation(description = GET_ADMIN_USER_STATUS_I18N_KEY, summary = "获取管理员用户状态")
    public RestResponse<AdminUserStatusResponse> getAdminUserStatus() {
        return RestResponse.success(AdminUserStatusResponse.instance());
    }

    @GetMapping("/certStatus")
    @Operation(description = GET_ADMIN_CER_STATUS_I18N_KEY, summary = "获取管理员证书状态")
    public RestResponse<AdminCertStatusResponse> getAdminCertStatus() {
        return RestResponse.success(AdminCertStatusResponse.instance());
    }

    /**
     * 根据系统当前环境为某些管理员追加权限。比如在deploying场景下需要允许部署操作员对初始化三员进行导入与查询等操作。
     */
    private Set<String> appendPermissionByDnv(Set<String> roleCodeSet) {
        Set<String> loginUserRoles = RequestContextHolder.getLoginUserRoles();
        // deploying场景下，允许部署操作员对初始化三员进行操作
        if (EnvironmentUtil.isDeploying() && loginUserRoles.contains(RoleCodeEnum.DEPLOY_OPER.getRoleCode())) {
            roleCodeSet.add(RoleCodeEnum.SUPER_ADMIN.getRoleCode());
            roleCodeSet.add(RoleCodeEnum.SECURITY_ADMIN.getRoleCode());
            roleCodeSet.add(RoleCodeEnum.AUDIT_ADMIN.getRoleCode());
        }
        // demo环境下，允许业务操作员对初始化三员进行操作
        if (!SystemUtil.isDeployed() && loginUserRoles.contains(RoleCodeEnum.BIZ_OPER.getRoleCode())) {
            roleCodeSet.add(RoleCodeEnum.SUPER_ADMIN.getRoleCode());
            roleCodeSet.add(RoleCodeEnum.SECURITY_ADMIN.getRoleCode());
            roleCodeSet.add(RoleCodeEnum.AUDIT_ADMIN.getRoleCode());
        }
        return roleCodeSet;
    }

    private AdminInfoListResponse convertAdminInfoList(AdminInfoListInfo adminInfo) {
        AdminInfoListResponse response = convertService.convert(adminInfo, AdminInfoListResponse.class);
        response.setOrgName(OrgCache.INSTANCE.getOrgById(adminInfo.getOrgId()).getOrgName());
        return response;
    }

    private AdminInfoDetailResponse convertAdminInfoDetail(AdminInfoDetailInfo detailInfo) {
        AdminInfoDetailResponse detailResponse = convertService.convert(detailInfo, AdminInfoDetailResponse.class);
        detailResponse.setOrgName(OrgCache.INSTANCE.getOrgById(detailInfo.getOrgId()).getOrgName());
        return detailResponse;
    }

}