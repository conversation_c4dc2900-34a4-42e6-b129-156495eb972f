package kl.npki.base.management.controller.cert;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.netty.conf.NettyTcpServerConfig;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.model.ServerCertRequestInfo;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.cert.request.CertSignRequest;
import kl.npki.base.management.model.cert.response.CertInfoResponse;
import kl.npki.base.management.model.ssl.response.SslServerCertResponse;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.cert.model.KeyIndexInfo;
import kl.npki.management.core.biz.ssl.model.SslServerCertInfo;
import kl.npki.management.core.service.cert.IKeyIndexService;
import kl.npki.management.core.service.cert.ISslCertService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 签发服务端站点证书
 *
 * <AUTHOR>
 * @date 2023/4/24
 */
@RequestMapping("/sslServerCert")
@RestController
@Tag(name = "服务器站点证书管理")
@LogCollector(resolver = OperationLogResolver.class, maskFields = "keyStorePwd")
public class SslServerCertController implements BaseController {

    @Resource
    private ValidateHelper validateUtil;

    @Resource
    private ISslCertService sslCertService;

    @Resource
    private ConvertService convertService;

    @Resource
    private IKeyIndexService keyIndexService;

    /**
     * 自签发站点证书(更新站点证书)
     *
     * @param certSignRequest
     * @return
     */
    @PostMapping("/signSSLCert")
    @Operation(description = SELF_ISSUED_SITE_CERTIFICATE_I18N_KEY, summary = "签发站点证书")
    public RestResponse<Boolean> signSiteCert(@RequestBody CertSignRequest certSignRequest) {
        validateUtil.validate(certSignRequest);
        NettyTcpServerConfig nettyTcpServerConfig = ConfigHolder.get().get(NettyTcpServerConfig.class);
        if (StringUtils.isBlank(certSignRequest.getKeyStorePwd())) {
            certSignRequest.setKeyStorePwd(nettyTcpServerConfig.getSslKeyStorePassword());
        }
        CertRequestInfo certRequestInfo = convertService.convert(certSignRequest, CertRequestInfo.class);
        // 修改tcp接口ssl协议
        String sslProtocol;
        if (Boolean.TRUE.equals(certSignRequest.getSelfSign())) {
            sslProtocol = sslCertService.signSslServerCert(certRequestInfo);
        } else {
            sslProtocol = sslCertService.signSslServerCertByCa(certRequestInfo);
        }
        sslCertService.signSslServerCertPostHandle(sslProtocol);
        return RestResponse.success(Boolean.TRUE);
    }

    @GetMapping("/getRootCertKeyList")
    @Operation(description = OBTAIN_THE_KEY_LIST_FOR_ISSUING_SITE_CERTIFICATES_I18N_KEY, summary = "获取签发站点证书的密钥列表")
    public RestResponse<KeyIndexInfo> getRootCertKeyType() {
        AsymAlgo certAlgo = MgrHolder.getManageCertMgr().getManageCertEntity().getCertAlgo();
        return RestResponse.success(keyIndexService.getKeyIndexInfoByKeyType(certAlgo));
    }

    @GetMapping("/getCaCertKeyList/{keyType}")
    @Operation(description = OBTAIN_THE_KEY_LIST_FOR_ISSUING_SITE_CERTIFICATES_I18N_KEY, summary = "获取CA签发站点证书的密钥列表")
    public RestResponse<KeyIndexInfo> getCaRootCertKeyType(@PathVariable(name = "keyType") String keyType) {
        AsymAlgo certAlgo = BaseConfigWrapper.getSysAlgoConfig().getSystemCertAsymAlgo();
        return RestResponse.success(keyIndexService.getKeyIndexInfoByKeyType(certAlgo));
    }

    @GetMapping("/getSslServerCert")
    @Operation(description = VIEW_DETAILED_INFORMATION_OF_SERVER_SITE_CERTIFICATE_I18N_KEY, summary = "查看服务器站点证书详细信息")
    public RestResponse<SslServerCertResponse> getSslServerCert() {
        SslServerCertInfo sslServerCertInfo = sslCertService.getSslServerCert();
        SslServerCertResponse sslServerCertResponse = convertService.convert(sslServerCertInfo, SslServerCertResponse.class);
        return RestResponse.success(sslServerCertResponse);
    }

    @GetMapping("/getSslCertRequestInfo")
    @Operation(description = VIEW_DETAILED_INFORMATION_OF_SERVER_SSl_CERTIFICATE_INFO_I18N_KEY, summary = "查看ssl证书请求信息")
    public RestResponse<CertInfoResponse> getSslCertRequestInfo() {
        ServerCertRequestInfo sslCertRequestInfo = sslCertService.getSslCertRequestInfo();
        CertInfoResponse response = convertService.convert(sslCertRequestInfo, CertInfoResponse.class);
        return RestResponse.success(response);
    }

}