package kl.npki.base.management.controller.config;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.netty.conf.NettyHttpServerConfig;
import kl.nbase.netty.conf.NettyTcpServerConfig;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.configs.ManagementSslConfig;
import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.model.system.request.PortConfigRequest;
import kl.npki.base.management.model.system.response.PortConfigResponse;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static kl.npki.base.management.constant.I18nOperationLogConstant.GET_PORT_CONFIGURATION_I18N_KEY;
import static kl.npki.base.management.constant.I18nOperationLogConstant.MODIFY_PORT_CONFIGURATION_I18N_KEY;

/**
 * <AUTHOR> Guiyu
 * @Date 2023/12/13
 */
@RestController
@RequestMapping("/sysConfig")
@Tag(name = "端口配置")
@LogCollector(resolver = OperationLogResolver.class)
public class PortConfigController implements BaseController, IConfigStatusService {

    private static final String PORT_CONFIG_NAME = "port";

    @Resource
    private ValidateService validateService;

    @GetMapping("/port")
    @Operation(description = GET_PORT_CONFIGURATION_I18N_KEY, summary = "获取端口配置")
    public RestResponse<PortConfigResponse> getPortConfig() {
        PortConfigResponse portConfigResponse = new PortConfigResponse();
        // mgmtHttp
        portConfigResponse.setMgmtHttpPort(ConfigHolder.get().get(NpkiServerConfig.class).getPort());
        // mgmtSsl
        portConfigResponse.setMgmtHttpSSLEnable(ConfigHolder.get().get(ManagementSslConfig.class).getEnabled());
        // nettyHttp
        portConfigResponse.setServiceHttpPort(ConfigHolder.get().get(NettyHttpServerConfig.class).getPort());
        portConfigResponse.setServiceHttpEnable(ConfigHolder.get().get(NettyHttpServerConfig.class).isEnabled());
        portConfigResponse.setServiceHttpSSLEnable(ConfigHolder.get().get(NettyHttpServerConfig.class).isSslEnabled());
        // nettyTcp
        portConfigResponse.setServiceTcpPort(ConfigHolder.get().get(NettyTcpServerConfig.class).getPort());
        portConfigResponse.setServiceTcpEnable(ConfigHolder.get().get(NettyTcpServerConfig.class).isEnabled());
        portConfigResponse.setServiceTcpSSLEnable(ConfigHolder.get().get(NettyTcpServerConfig.class).isSslEnabled());
        return RestResponse.success(portConfigResponse);
    }

    @PutMapping("/port")
    @Operation(description = MODIFY_PORT_CONFIGURATION_I18N_KEY, summary = "修改端口配置")
    public RestResponse<String> updatePortConfig(@RequestBody PortConfigRequest request) {
        validateService.validate(request);
        // 当前参数端口是否重复
        checkPortRepeat(request);
        // mgmtHttp
        NpkiServerConfig npkiServerConfig = ConfigHolder.get().get(NpkiServerConfig.class);
        npkiServerConfig.setPort(request.getMgmtHttpPort());
        ConfigHolder.get().save(npkiServerConfig);
        // mgmtSsl
        ManagementSslConfig managementSslConfig = ConfigHolder.get().get(ManagementSslConfig.class);
        managementSslConfig.setEnabled(request.getMgmtHttpSSLEnable());
        ConfigHolder.get().save(managementSslConfig);
        // nettyHttp
        NettyHttpServerConfig nettyHttpServerConfig = ConfigHolder.get().get(NettyHttpServerConfig.class);
        nettyHttpServerConfig.setEnabled(request.getServiceHttpEnable());
        nettyHttpServerConfig.setPort(request.getServiceHttpPort());
        nettyHttpServerConfig.setSslEnabled(request.getServiceHttpSSLEnable());
        ConfigHolder.get().save(nettyHttpServerConfig);
        // nettyTcp
        NettyTcpServerConfig nettyTcpServerConfig = ConfigHolder.get().get(NettyTcpServerConfig.class);
        nettyTcpServerConfig.setEnabled(request.getServiceTcpEnable());
        nettyTcpServerConfig.setPort(request.getServiceTcpPort());
        nettyTcpServerConfig.setSslEnabled(request.getServiceTcpSSLEnable());
        ConfigHolder.get().save(nettyTcpServerConfig);
        // 设置端口配置完成
        npkiServerConfig.setPortConfigured(true);
        ConfigHolder.get().save(npkiServerConfig);
        return RestResponse.success();
    }

    private void checkPortRepeat(PortConfigRequest request) {
        if (Objects.isNull(request)) {
            return;
        }
        Integer mgmtHttpPort = request.getMgmtHttpPort();
        Integer serviceHttpPort = request.getServiceHttpPort();
        Integer serviceTcpPort = request.getServiceTcpPort();
        List<Integer> portList = Lists.newArrayList(mgmtHttpPort, serviceHttpPort, serviceTcpPort);
        Set<Integer> portSet = new HashSet<>();
        portList.forEach(port -> {
            if (Objects.isNull(port)) {
                return;
            }
            if (!portSet.add(port)) {
                throw ManagementInternalError.PORT_USED_ERROR.toException(port.toString());
            }
        });
    }

    @Override
    public ConfigStatus queryConfigStatus() {
        NpkiServerConfig npkiServerConfig = ConfigHolder.get().get(NpkiServerConfig.class);
        return new ConfigStatus(PORT_CONFIG_NAME, Boolean.TRUE.equals(npkiServerConfig.getPortConfigured()));
    }
}