package kl.npki.base.management.controller.org;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.org.model.OrgAddInfo;
import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.core.biz.org.model.OrgTraceEntity;
import kl.npki.base.core.biz.org.model.OrgUpdateInfo;
import kl.npki.base.core.biz.org.service.IOrgService;
import kl.npki.base.core.common.org.OrgCache;
import kl.npki.base.core.common.structure.TreeNode;
import kl.npki.base.core.repository.IOrgTraceRepository;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.batch.BaseBatchRequest;
import kl.npki.base.management.model.batch.BatchResponse;
import kl.npki.base.management.model.org.*;
import kl.npki.base.management.repository.service.IOrgManagementService;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.util.List;

/**
 * 机构管理
 *
 * <AUTHOR>
 * @create 2024/3/21 15:05
 */
@RestController
@RequestMapping("/org")
@Tag(name = "机构管理")
public class OrgManagementController implements BaseController {
    @Resource
    private IOrgService orgService;

    @Resource
    private IOrgManagementService orgManagementService;

    @Resource
    private ValidateService validateService;

    @Resource
    private ConvertService convertService;

    @Resource
    private IOrgTraceRepository orgTraceRepository;
    
    /**
     * 查询机构信息列表
     *
     * @param orgListRequest 高级查询封装
     * @return
     */
    @GetMapping("/list")
    //message = 查询机构信息列表
    @Operation(description = "kl.npki.base.management.i18n_annotation.query_institution_information_list", summary = "查询机构信息列表")
    public RestResponse<IPage<OrgListResponse>> queryOrgPageList(PageInfo pageInfo, OrgListRequest orgListRequest) {
        IPage<OrgListResponse> orgList = orgManagementService.queryOrgPageList(pageInfo, orgListRequest);
        return RestResponse.success(orgList);
    }

    @GetMapping("/tree")
    @Operation(description = "kl.npki.base.management.i18n_annotation.query_institution_information_tree", summary = "查询机构树结构")
    public RestResponse<OrgTreeResponse> tree() {
        TreeNode<Long, OrgEntity> orgTree = OrgCache.INSTANCE.getOrgTree();
        return RestResponse.success(new OrgTreeResponse(orgTree));
    }

    /**
     * 查询所有机构
     *
     * @param pageInfo 分页条件
     * @return
     */
    @GetMapping("/all")
    //message = 查询所有机构
    @Operation(description = "kl.npki.base.management.i18n_annotation.search_all_institutions", summary = "查询所有机构")
    public RestResponse<IPage<OrgListResponse>> queryAllOrg(PageInfo pageInfo) {
        IPage<OrgListResponse> orgList = orgManagementService.queryAllOrg(pageInfo);
        return RestResponse.success(orgList);
    }

    /**
     * 查询根机构
     *
     * @return
     */
    @GetMapping("/queryRootOrg")
    //message = 查询根机构
    @Operation(description = "kl.npki.base.management.i18n_annotation.query_the_root_institution", summary = "查询根机构")
    public RestResponse<OrgListResponse> queryRootOrg() {
        return RestResponse.success(orgManagementService.queryRootOrg());
    }

    /**
     * 查询子机构列表
     *
     * @param parentCode 父机构编码
     * @return
     */
    @GetMapping("/querySubOrg/{parentCode}")
    //message = 查询子机构
    @Operation(description = "kl.npki.base.management.i18n_annotation.query_sub_institutions", summary = "查询子机构")
    public RestResponse<List<OrgListResponse>> querySubOrgList(@PathVariable(value = "parentCode") String parentCode) {
        List<OrgListResponse> orgList = orgManagementService.querySubOrg(parentCode);
        return RestResponse.success(orgList);
    }

    /**
     * 查询机构信息
     *
     * @param id 需要查询的机构id
     * @return
     */
    @GetMapping("/detail/{id}")
    //message = 查询信息详情
    @Operation(description = "kl.npki.base.management.i18n_annotation.query_information_details", summary = "查询信息详情")
    public RestResponse<OrgDetailResponse> getOrgDetail(@PathVariable(value = "id") Long id) {
        OrgDetailResponse orgDetailResponse = orgManagementService.getOrgDetailById(id);
        return RestResponse.success(orgDetailResponse);
    }

    /**
     * 查询机构信息
     *
     * @param orgCode 需要查询的机构编码
     * @return
     */
    @GetMapping("/detail")
    //message = 根据机构编码查询信息详情
    @Operation(description = "kl.npki.base.management.i18n_annotation.query_information_details_based_on_institutional_code", summary = "根据机构编码查询信息详情")
    public RestResponse<OrgDetailResponse> getOrgDetail(@RequestParam String orgCode) {
        OrgDetailResponse orgDetailResponse = orgManagementService.getOrgDetailByCode(orgCode);
        return RestResponse.success(orgDetailResponse);
    }

    /**
     * 新增机构
     *
     * @param orgAddRequest 新增加机构请求
     * @return
     */
    @LogCollector(resolver = OperationLogResolver.class)
    @PostMapping("/add")
    //message = 新增机构
    @Operation(description = "kl.npki.base.management.i18n_annotation.newly_added_institutions", summary = "新增机构")
    public RestResponse<Boolean> orgAdd(@RequestBody OrgAddRequest orgAddRequest) {
        validateService.validate(orgAddRequest);
        OrgAddInfo orgUpdateInfo = convertService.convert(orgAddRequest, OrgAddInfo.class);
        return RestResponse.success(orgService.add(orgUpdateInfo));
    }


    /**
     * 更新机构
     *
     * @param id               机构标识id
     * @param orgUpdateRequest 机构更新信息
     * @return
     */
    @LogCollector(resolver = OperationLogResolver.class)
    @PutMapping(value = "/update/{id}")
    //message = 机构更新
    @Operation(description = "kl.npki.base.management.i18n_annotation.institutional_update", summary = "机构更新")
    public RestResponse<Boolean> orgUpdate(@PathVariable(value = "id") @NotNull Long id,
                                           @RequestBody OrgUpdateRequest orgUpdateRequest) {
        validateService.validate(orgUpdateRequest);
        OrgUpdateInfo orgUpdateInfo = convertService.convert(orgUpdateRequest, OrgUpdateInfo.class);
        return RestResponse.success(orgService.update(id, orgUpdateInfo));
    }

    /**
     * 废除机构
     *
     * @param id 待废除的机构id
     * @return
     */
    @LogCollector(resolver = OperationLogResolver.class)
    @PutMapping("/revoke/{id}")
    //message = 废除机构
    @Operation(description = "kl.npki.base.management.i18n_annotation.abolish_institutions", summary = "废除机构")
    public RestResponse<Boolean> orgRevoke(@PathVariable(value = "id") @NotNull Long id) {
        return RestResponse.success(orgService.revoke(id));
    }

    /**
     * 删除机构
     *
     * @param id 待删除机构id
     * @return
     */
    @LogCollector(resolver = OperationLogResolver.class)
    @DeleteMapping("/delete/{id}")
    //message = 删除机构
    @Operation(description = "kl.npki.base.management.i18n_annotation.delete_institution", summary = "删除机构")
    public RestResponse<Boolean> orgDelete(@PathVariable(value = "id") @NotNull Long id) {
        return RestResponse.success(orgService.delete(id));
    }

    @LogCollector(resolver = OperationLogResolver.class)
    @DeleteMapping(("/batch/delete"))
    @Operation(description = "kl.npki.base.management.i18n_annotation.batch_deletion_of_institutions", summary = "批量删除机构")
    public RestResponse<BatchResponse> batchOrgDelete(@RequestBody BaseBatchRequest batchRequest) {
        return RestResponse.success(convertService.convert(orgService.batchDelete(batchRequest.getIdList()), BatchResponse.class));
    }

    @PostMapping("/export")
    //message = 批量导出机构
    @Operation(description = "kl.npki.base.management.i18n_annotation.batch_export_of_institutions", summary = "批量导出机构")
    public ResponseEntity<byte[]> batchExport(@RequestBody OrgListRequest orgListRequest) {
        // 批量导出机构信息
        File resultFile = orgManagementService.batchExport(orgListRequest);
        return FileDownloadHelper.getResponseEntityByFile(resultFile, true);
    }

    /**
     * 获取机构历史轨迹
     */
    @GetMapping("/trace/{id}")
    //message = 获取机构历史轨迹
    @Operation(description = "kl.npki.base.management.i18n_annotation.obtain_the_historical_trajectory_of_the_institution", summary = "获取机构历史轨迹")
    public RestResponse<List<OrgTraceResponse>> getTraceByOrgId(@PathVariable(value = "id") Long entityId) {
        List<OrgTraceEntity> orgTraceEntities = orgTraceRepository.searchByEntityId(entityId);
        CheckUtils.notNull(orgTraceEntities, ManagementValidationError.TRACE_NOT_FOUND_ERROR.toException());
        List<OrgTraceResponse> result = convertService.convert(orgTraceEntities, OrgTraceResponse.class);
        return RestResponse.success(result);
    }
}
