package kl.npki.base.management.controller.cert;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.model.cert.request.CertParseRequest;
import kl.npki.base.management.model.cert.response.CertPreviewResponse;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import org.apache.commons.codec.binary.Base64;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;

import static kl.npki.base.management.constant.I18nOperationLogConstant.CERTIFICATE_PREVIEW_ANALYSIS_I18N_KEY;
import static kl.npki.base.management.constant.I18nOperationLogConstant.PARSE_CERTIFICATE_I18N_KEY;

/**
 * 证书通用功能
 *
 * <AUTHOR>
 * @date 2024/8/16 16:07
 */
@RestController
@RequestMapping("/cert")
@Tag(name = "证书通用功能")
@LogCollector(resolver = OperationLogResolver.class)
public class CertController implements BaseController {

    @Resource
    private ValidateHelper validateUtil;

    @PostMapping("/preview")
    @Operation(description = CERTIFICATE_PREVIEW_ANALYSIS_I18N_KEY, summary = "证书预览解析")
    public RestResponse<CertPreviewResponse> certPreview(@RequestBody String certificate) {
        return RestResponse.success(CertPreviewResponse.viewX509Cert(certificate));
    }

    @PostMapping("/parse")
    @Operation(description = PARSE_CERTIFICATE_I18N_KEY, summary = "解析证书")
    public RestResponse<Object> parseCert(@RequestBody CertParseRequest request) {
        validateUtil.validate(request);
        Certificate cert = Certificate.getInstance(Base64.decodeBase64(request.getB64Cert()));
        SubjectPublicKeyInfo subPubKeyInfo = cert.getSubjectPublicKeyInfo();
        try {
            return RestResponse.success(Base64.encodeBase64String(subPubKeyInfo.getEncoded()));
        } catch (IOException e) {
            throw ManagementInternalError.CERT_ENCODE_ERROR.toException(e);
        }
    }
}