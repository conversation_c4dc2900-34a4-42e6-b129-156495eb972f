package kl.npki.base.management.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.constant.EmTypeEnum;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.engine.request.GroupEngineConfigRequest;
import kl.npki.base.management.model.engine.response.EngineTypeResponse;
import kl.npki.base.management.model.engine.response.GroupEngineConfigResponse;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.cert.model.KeyIndexInfo;
import kl.npki.management.core.biz.config.model.EngineAsymAlgoResponse;
import kl.npki.management.core.biz.config.model.EngineTypeInfo;
import kl.npki.management.core.service.cert.IKeyIndexService;
import kl.npki.management.core.service.config.EngineConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 密码机配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/engineConfig")
@Tag(name = "加密机配置")
@LogCollector(resolver = OperationLogResolver.class, maskFields = {"username", "engineCred", "defaultKeyCred"})
public class EngineConfigController implements BaseController {

    @Resource
    private ValidateHelper validateUtil;

    @Resource
    private EngineConfigService engineConfigService;

    @Resource
    private IKeyIndexService keyIndexService;

    @GetMapping("/info")
    @Operation(description = QUERY_ENCRYPTION_MACHINE_CONFIGURATION_I18N_KEY, summary = "查询加密机配置")
    public RestResponse<GroupEngineConfigResponse> queryEngineConfig() {
        Optional<ClusterEmConfig> clusterEmConfig = engineConfigService.queryEngineConfig();
        return clusterEmConfig.map(emConfig -> RestResponse.success(new GroupEngineConfigResponse(emConfig))).orElseGet(RestResponse::success);
    }

    @PostMapping("/save")
    @Operation(description = SAVE_ENCRYPTION_MACHINE_CONFIGURATION_I18N_KEY, summary = "保存加密机配置")
    public RestResponse<Void> saveEngineConfig(@RequestBody GroupEngineConfigRequest engineConfigRequest) {
        validateUtil.validate(engineConfigRequest);
        engineConfigService.saveEngineConfig(engineConfigRequest.toClusterEmConfig());
        return RestResponse.success();
    }

    @GetMapping("/type/list")
    @Operation(description = QUERY_THE_LIST_OF_ENCRYPTION_MACHINE_TYPES_I18N_KEY, summary = "查询加密机类型列表")
    public RestResponse<EngineTypeResponse> queryEngineType() {
        List<EngineTypeInfo> engineTypeList = engineConfigService.queryEngineType();
        return RestResponse.success(new EngineTypeResponse(engineTypeList));
    }

    @PostMapping("/valid")
    @Operation(description = TEST_ENCRYPTION_MACHINE_CONNECTION_I18N_KEY, summary = "测试加密机连接")
    public RestResponse<Void> validEngineConfig(@RequestBody @Valid GroupEngineConfigRequest engineConfigRequest) {
        validateUtil.validate(engineConfigRequest);
        engineConfigService.validEngineConfig(engineConfigRequest.toClusterEmConfig());
        return RestResponse.success();
    }

    @GetMapping("/keyType")
    @Operation(description = QUERY_THE_LIST_OF_ASYMMETRIC_KEY_TYPES_SUPPORTED_BY_THE_CURRENT_CONFIGURED_ENCRYPTION_MACHINE_I18N_KEY, summary = "查询当前配置加密机支持的非对称密钥类型列表")
    public RestResponse<List<EngineAsymAlgoResponse>> queryKeyType() {
        List<EngineAsymAlgoResponse> keyTypeEnumList = engineConfigService.querySignKeyType();
        return RestResponse.success(keyTypeEnumList);
    }

    @GetMapping("/keyType/pqc")
    @Operation(description = QUERY_THE_LIST_OF_ANTI_QUANTUM_ENCRYPTION_KEY_TYPES_SUPPORTED_BY_THE_CURRENT_CONFIGURED_ENCRYPTION_MACHINE_I18N_KEY, summary = "查询当前配置加密机支持的抗量子加密密钥类型列表")
    public RestResponse<List<EngineAsymAlgoResponse>> queryPqcEncKeyType() {
        List<EngineAsymAlgoResponse> keyTypeEnumList = engineConfigService.queryPqEncKeyType();
        return RestResponse.success(keyTypeEnumList);
    }

    @GetMapping("/{engineType}/keyType/list")
    @Operation(description = QUERY_THE_LIST_OF_SUPPORTED_ASYMMETRIC_KEY_TYPES_BASED_ON_THE_ENCRYPTION_MACHINE_TYPE_I18N_KEY, summary = "根据加密机类型查询支持的非对称密钥类型列表")
    public RestResponse<List<EngineAsymAlgoResponse>> queryKeyTypeByEngineType(@PathVariable(name = "engineType") String engineType) {
        List<EngineAsymAlgoResponse> asymAlgoList = engineConfigService.queryKeyTypeByEngineType(engineType);
        return RestResponse.success(asymAlgoList);
    }

    /**
     * 根据主加密机类型获取辅助密码机支持的算法
     *
     * @param engineTypes 加密机类型集合 {@link EmTypeEnum#getEngineType}
     * @return {@link RestResponse }<{@link List }<{@link EngineAsymAlgoResponse }>>
     */
    @GetMapping("/backup/keyType/list")
    @Parameter(name = "engineTypes", description = "加密机类型集合，值来源于EmTypeEnum#engineType", example = "sansec_fips")
    @Operation(description = QUERY_THE_LIST_OF_SUPPORTED_ASYMMETRIC_KEY_TYPES_BASED_ON_THE_ENCRYPTION_MACHINE_TYPE_I18N_KEY, summary = "根据加密机类型查询支持的非对称密钥类型列表")
    public RestResponse<List<EngineAsymAlgoResponse>> queryKeyTypeByBackup(@RequestParam List<String> engineTypes) {
        List<EngineAsymAlgoResponse> asymAlgoList = engineConfigService.queryKeyTypeByBackup(engineTypes);
        return RestResponse.success(asymAlgoList);
    }


    @GetMapping("/{keyType}/keyIndex")
    @Operation(description = QUERY_KEY_INDEX_LIST_I18N_KEY, parameters = {@Parameter(name = "keyType", required = true, in = ParameterIn.QUERY, description = "非对称算法类型，如RSA_1024，SM2")}, summary = "查询密钥索引列表")
    public RestResponse<KeyIndexInfo> queryKeyIndexByKeyType(@PathVariable(name = "keyType") String keyType) {
        // 检测密码机是否配置
        ConfigStatus configStatus = engineConfigService.queryConfigStatus();
        if (configStatus.isStatus()) {
            AsymAlgo asymAlgo = new AsymAlgo(keyType);
            KeyIndexInfo keyIndexInfo = keyIndexService.getKeyIndexInfoByKeyType(asymAlgo);
            return RestResponse.success(keyIndexInfo);
        } else {
            return RestResponse.success();
        }
    }

    @GetMapping("/status")
    @Operation(description = CHECK_IF_THE_CONFIGURATION_IS_COMPLETE_I18N_KEY, summary = "查询配置是否完成")
    public RestResponse<ConfigStatus> queryConfigStatus() {
        return RestResponse.success(engineConfigService.queryConfigStatus());
    }
}
