package kl.npki.base.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.rpc.core.http.KlHttpRequestType;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.CaLevel;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.management.model.constant.response.BaseConstantResponse;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.base.management.constant.I18nOperationLogConstant;
import kl.npki.management.core.constants.ResourceType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 常量管理
 *
 * <AUTHOR> Guiyu
 * @date 2025/5/7
 */
@RestController
@RequestMapping("/constant")
@Tag(name = "常量管理")
@LogCollector(resolver = OperationLogResolver.class)
public class BaseConstantController implements BaseController {

    @GetMapping("/asymAlgo")
    @Operation(
        description = I18nOperationLogConstant.CONSTANT_ASYM_ALGO_I18N_KEY,
        summary = "查询非对称算法常量"
    )
    public RestResponse<List<BaseConstantResponse>> asymAlgo() {
        // AsymAlgo.getAsymAlgoList()返回的数据太多，因此改为用配置文件配置的算法返回，同时前端改用支持输入的下拉框以便用户能输入选项中没有的算法
        List<String> asymAlgoList = BaseConfigWrapper.getSysAlgoConfig().getSupport().extractAsymmetricList();
        List<BaseConstantResponse> responses = asymAlgoList.stream()
                .map(asymAlgo -> new BaseConstantResponse(null, asymAlgo, null))
                .collect(Collectors.toList());
        return RestResponse.success(responses);
    }

    @GetMapping("/certStatus")
    @Operation(
        description = I18nOperationLogConstant.CONSTANT_CERT_STATUS_I18N_KEY,
        summary = "查询证书状态常量"
    )
    public RestResponse<List<BaseConstantResponse>> certStatus() {
        List<BaseConstantResponse> responses = new ArrayList<>();
        for (CertStatus certStatus : CertStatus.values()) {
            responses.add(new BaseConstantResponse(certStatus.getCode(), null, certStatus.getDesc()));
        }
        return RestResponse.success(responses);
    }

    @GetMapping("/userStatus")
    @Operation(
        description = I18nOperationLogConstant.CONSTANT_USER_STATUS_I18N_KEY,
        summary = "查询用户状态常量"
    )
    public RestResponse<List<BaseConstantResponse>> userStatus() {
        List<BaseConstantResponse> responses = new ArrayList<>();
        for (UserStatus userStatus : UserStatus.values()) {
            responses.add(new BaseConstantResponse(userStatus.getCode(), null, userStatus.getDesc()));
        }
        return RestResponse.success(responses);
    }

    @GetMapping("/caLevel")
    @Operation(
        description = I18nOperationLogConstant.CONSTANT_CA_LEVEL_I18N_KEY,
        summary = "查询CA等级常量"
    )
    public RestResponse<List<BaseConstantResponse>> caLevel() {
        List<BaseConstantResponse> responses = new ArrayList<>();
        for (CaLevel caLevel : CaLevel.values()) {
            responses.add(new BaseConstantResponse(caLevel.getCode(), null, caLevel.getDesc()));
        }
        return RestResponse.success(responses);
    }

    @GetMapping("/entityStatus")
    @Operation(
        description = I18nOperationLogConstant.CONSTANT_ENTITY_STATUS_I18N_KEY,
        summary = "查询实体状态常量"
    )
    public RestResponse<List<BaseConstantResponse>> entityStatus() {
        List<BaseConstantResponse> responses = new ArrayList<>();
        for (EntityStatus entityStatus : EntityStatus.values()) {
            responses.add(new BaseConstantResponse(entityStatus.getId(), null, entityStatus.getDesc()));
        }
        return RestResponse.success(responses);
    }

    @GetMapping("/httpMethod")
    @Operation(
        description = I18nOperationLogConstant.CONSTANT_HTTP_METHOD_I18N_KEY,
        summary = "查询HTTP请求类型列表"
    )
    public RestResponse<List<String>> httpMethod() {
        List<String> responses = new ArrayList<>();
        for (KlHttpRequestType httpMethod : KlHttpRequestType.values()) {
            responses.add(httpMethod.getName());
        }
        return RestResponse.success(responses);
    }

    @GetMapping("/resourceTypes")
    @Operation(
        description = I18nOperationLogConstant.CONSTANT_RESOURCE_TYPES_I18N_KEY,
        summary = "查询统一资源类型常量"
    )
    public RestResponse<List<BaseConstantResponse>> resourceTypes() {
        List<BaseConstantResponse> responses = new ArrayList<>();
        for (ResourceType value : ResourceType.values()) {
            responses.add(new BaseConstantResponse(value.getType(), null, value.getDescription()));
        }
        return RestResponse.success(responses);
    }

}