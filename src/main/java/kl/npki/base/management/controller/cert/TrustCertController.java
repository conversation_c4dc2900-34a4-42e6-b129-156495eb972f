package kl.npki.base.management.controller.cert;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.netty.conf.NettyTcpServerConfig;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.model.ImportTrustCertInfo;
import kl.npki.base.core.biz.cert.model.ServerCertRequestInfo;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.biz.cert.service.ICertExportService;
import kl.npki.base.core.biz.cert.service.ITrustCertService;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.cert.request.CertSignRequest;
import kl.npki.base.management.model.cert.request.ExportCertRequest;
import kl.npki.base.management.model.cert.request.ImportTrustCertRequest;
import kl.npki.base.management.model.cert.response.CertInfoResponse;
import kl.npki.base.management.model.cert.response.CertKeyTypeResponse;
import kl.npki.base.management.model.cert.response.ManagerCertResponse;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/trustCert")
@Tag(name = "可信证书管理")
@LogCollector(resolver = OperationLogResolver.class, maskFields = "keyStorePwd")
public class TrustCertController implements BaseController {

    @Resource
    private ValidateHelper validateUtil;

    @Resource
    private ConvertService convertService;

    @Resource
    private ITrustCertService trustCertService;

    @Resource
    private ICertExportService certExportService;

    private static final String CERT_ZIP_NAME_SUFFIX = ".AllCert.zip";

    @GetMapping("/query/manage")
    @Operation(description = QUERY_MANAGEMENT_ROOT_CERTIFICATE_I18N_KEY, summary = "查询管理根证书")
    public RestResponse<ManagerCertResponse> queryManageCert() {
        TrustCertEntity manageCertEntity = MgrHolder.getManageCertMgr().getManageCertEntity();
        if (ObjectUtils.isEmpty(manageCertEntity)) {
            return RestResponse.success();
        }
        return RestResponse.success(convertService.convert(manageCertEntity, ManagerCertResponse.class));
    }

    @GetMapping("/asymAlgo")
    @Operation(description = QUERY_MANAGEMENT_ROOT_CERTIFICATE_ALGORITHM_I18N_KEY, summary = "查询管理根证书算法")
    public RestResponse<CertKeyTypeResponse> queryManageCertAsymAlgo() {
        AsymAlgo certAlgo = BaseConfigWrapper.getSysAlgoConfig().getSystemCertAsymAlgo();
        return RestResponse.success(new CertKeyTypeResponse(certAlgo));
    }

    @GetMapping("/caAsymAlgo/{keyType}")
    @Operation(description = QUERY_MANAGEMENT_ROOT_CERTIFICATE_ALGORITHM_I18N_KEY, summary = "查询根证书算法")
    public RestResponse<CertKeyTypeResponse> queryCaCertAsymAlgo(@PathVariable(name = "keyType") String keyType) {
        AsymAlgo certAlgo = MgrHolder.getCaCertMgr().getCaCertEntity(keyType).getCertAlgo();
        return RestResponse.success(new CertKeyTypeResponse(certAlgo));
    }

    @PostMapping("/issue/manage")
    @Operation(description = SELF_SIGNED_HAIR_ROOT_CERTIFICATE_I18N_KEY, summary = "自签发根证书")
    public RestResponse<String> selfIssueManageCert(@RequestBody CertSignRequest certSignRequest) {
        validateUtil.validate(certSignRequest);
        if (StringUtils.isBlank(certSignRequest.getKeyStorePwd())) {
            certSignRequest.setKeyStorePwd(ConfigHolder.get().get(NettyTcpServerConfig.class).getSslTrustStorePassword());
        }
        CertRequestInfo certRequestInfo = convertService.convert(certSignRequest, CertRequestInfo.class);
        String rootCert = trustCertService.issueManageCert(certRequestInfo);
        return RestResponse.success(rootCert);
    }

    @PutMapping("/reissue/manage")
    @Operation(description = RE_SIGN_THE_HAIR_ROOT_CERTIFICATE_I18N_KEY, summary = "重新签发根证书")
    public RestResponse<String> selfReissueManageCert(@RequestBody CertSignRequest certSignRequest) {
        validateUtil.validate(certSignRequest);
        if (StringUtils.isBlank(certSignRequest.getKeyStorePwd())) {
            certSignRequest.setKeyStorePwd(ConfigHolder.get().get(NettyTcpServerConfig.class).getSslTrustStorePassword());
        }
        CertRequestInfo certRequestInfo = convertService.convert(certSignRequest, CertRequestInfo.class);
        String rootCert = trustCertService.selfUpdateManageCert(certRequestInfo);
        return RestResponse.success(rootCert);
    }

    @PostMapping("/import/trust")
    @Operation(description = IMPORT_TRUSTED_CERTIFICATE_I18N_KEY, summary = "导入可信证书")
    public RestResponse<Void> importCertChain(@RequestBody ImportTrustCertRequest importTrustCertInfoRequest) {
        validateUtil.validate(importTrustCertInfoRequest);
        ImportTrustCertInfo importTrustCertInfo = convertService.convert(importTrustCertInfoRequest, ImportTrustCertInfo.class);
        String sslTrustStorePassword = ConfigHolder.get().get(NettyTcpServerConfig.class).getSslTrustStorePassword();
        trustCertService.importCert(importTrustCertInfo, sslTrustStorePassword);
        return RestResponse.success();
    }

    @DeleteMapping("/chain/{id}")
    @Operation(description = DELETE_TRUSTED_CERTIFICATE_CHAIN_I18N_KEY, summary = "删除可信证书链")
    public RestResponse<Void> deleteCertChain(@PathVariable(name = "id") Long id) {
        String sslTrustStorePassword = ConfigHolder.get().get(NettyTcpServerConfig.class).getSslTrustStorePassword();
        trustCertService.delete(id, sslTrustStorePassword);
        return RestResponse.success();
    }

    @PostMapping("/import/manage")
    @Operation(description = IMPORT_MANAGEMENT_ROOT_CERTIFICATE_I18N_KEY, summary = "导入管理根证书")
    public RestResponse<Void> importManageCert(@RequestBody String b64RootCert) {
        String sslTrustStorePassword = ConfigHolder.get().get(NettyTcpServerConfig.class).getSslTrustStorePassword();
        trustCertService.importManageCert(b64RootCert, sslTrustStorePassword);
        return RestResponse.success();
    }

    @PutMapping("/import/manage")
    @Operation(description = UPDATE_IMPORT_MANAGEMENT_ROOT_CERTIFICATE_I18N_KEY, summary = "更新导入管理根证书")
    public RestResponse<Void> importUpdateManageCert(@RequestBody String b64RootCert) {
        String sslTrustStorePassword = ConfigHolder.get().get(NettyTcpServerConfig.class).getSslTrustStorePassword();
        trustCertService.importUpdateManageCert(b64RootCert, sslTrustStorePassword);
        return RestResponse.success();
    }

    @PostMapping("/p10")
    @Operation(description = GENERATE_CERTIFICATE_REQUEST_I18N_KEY, summary = "生成证书请求")
    public RestResponse<String> genCertRequest(@RequestBody CertSignRequest certSignRequest) {
        validateUtil.validate(certSignRequest);
        CertRequestInfo certRequestInfo = convertService.convert(certSignRequest, CertRequestInfo.class);
        String request = trustCertService.genCertRequest(certRequestInfo);
        return RestResponse.success(request);
    }

    @PutMapping("/p10")
    @Operation(description = UPDATE_CERTIFICATE_REQUEST_I18N_KEY, summary = "更新证书请求")
    public RestResponse<String> updateCertRequest(@RequestBody CertSignRequest certSignRequest) {
        validateUtil.validate(certSignRequest);
        CertRequestInfo certRequestInfo = convertService.convert(certSignRequest, CertRequestInfo.class);
        String request = trustCertService.updateCertRequest(certRequestInfo);
        return RestResponse.success(request);
    }

    @GetMapping("/export/p10")
    @LogCollector(resolver = OperationLogResolver.class)
    @Operation(description = EXPORT_CERTIFICATE_REQUEST_I18N_KEY, summary = "导出证书请求")
    public RestResponse<String> exportCertRequest() {
        String p10 = trustCertService.exportCertRequest();
        return RestResponse.success(p10);
    }

    @GetMapping("/export/cert/{hexSn}")
    @LogCollector(resolver = OperationLogResolver.class)
    @Operation(description = EXPORT_CERTIFICATE_I18N_KEY, summary = "导出证书")
    public void exportCert(@PathVariable(name = "hexSn") String hexSn, HttpServletResponse response) throws IOException {
        byte[] zipData = trustCertService.exportCertAsZip(hexSn);
        TrustCertEntity certEntity = RepositoryFactory.get(ITrustCertRepository.class).getCert(hexSn);
        response.setHeader(FileDownloadHelper.CONTENT_DISPOSITION, FileDownloadHelper.buildAttachmentVal(certEntity.getSubjectCn() + CERT_ZIP_NAME_SUFFIX));
        response.getOutputStream().write(zipData);
    }

    @PostMapping("/export/cert")
    @Operation(description = EXPORT_CERTIFICATE_I18N_KEY, summary = "导出证书")
    public ResponseEntity<byte[]> exportCert(@RequestBody ExportCertRequest request) {
        TrustCertEntity certEntity = RepositoryFactory.get(ITrustCertRepository.class).getCert(request.getHexSn());
        File file = certExportService.exportCert(certEntity.getCertValue(), request.getExportCertTypeEnum());
        return FileDownloadHelper.getResponseEntityByFile(file, true);
    }

    @GetMapping("/list")
    @Operation(description = QUERY_THE_LIST_OF_TRUSTED_CERTIFICATES_I18N_KEY, summary = "查询可信证书列表")
    public RestResponse<List<ManagerCertResponse>> list() {
        List<TrustCertEntity> allTrustCert = trustCertService.getAllTrustCert();
        if (CollectionUtils.isEmpty(allTrustCert)) {
            return RestResponse.success();
        }
        return RestResponse.success(convertService.convert(allTrustCert, ManagerCertResponse.class));
    }

    @GetMapping("/getRootCertRequestInfo")
    @Operation(description = VIEW_DETAILED_INFORMATION_OF_SERVER_ROOT_CERTIFICATE_INFO_I18N_KEY, summary = "查看根证书请求信息")
    public RestResponse<CertInfoResponse> getRootCertRequestInfo() {
        ServerCertRequestInfo rootCertRequestInfo = trustCertService.getRootCertRequestInfo();
        CertInfoResponse response = convertService.convert(rootCertRequestInfo, CertInfoResponse.class);
        return RestResponse.success(response);
    }

}