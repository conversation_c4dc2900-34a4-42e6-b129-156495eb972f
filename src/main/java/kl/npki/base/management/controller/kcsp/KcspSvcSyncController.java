package kl.npki.base.management.controller.kcsp;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import kl.npki.base.core.biz.kcsp.model.KcspRestResponse;
import kl.npki.management.core.biz.kcsp.model.KcspServiceSyncRequest;
import kl.npki.base.management.controller.BaseController;
import kl.npki.management.core.service.kcsp.KcspSvcSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import kl.npki.base.management.constant.I18nOperationLogConstant;

/**
 * KCSP平台公共服务同步后端控制器（KCSP平台新增和删除平台公共服务时调用）
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@RestController
@Tag(name = "KCSP服务同步控制器")
@RequestMapping("/cipher/v1/service")
public class KcspSvcSyncController implements BaseController {

    private final KcspSvcSyncService kcspSvcSyncService;

    @Autowired
    public KcspSvcSyncController(KcspSvcSyncService kcspSvcSyncService) {
        this.kcspSvcSyncService = kcspSvcSyncService;
    }

    @PostMapping("/sync/thrid")
    @Operation(
        summary = "KCSP平台服务同步（新增、更新、删除平台公共服务）",
        description = I18nOperationLogConstant.KCSP_SYNC_SERVICE_I18N_KEY
    )
    public ResponseEntity<KcspRestResponse> syncService(@RequestBody KcspServiceSyncRequest request) {
        try {
            switch (request.getOpType()) {
                case "service.add":
                    return ResponseEntity.ok(kcspSvcSyncService.activateService(request.getData()));
                case "service.update":
                    return ResponseEntity.ok(kcspSvcSyncService.updateService(request.getData()));
                case "service.delete":
                    return ResponseEntity.ok(kcspSvcSyncService.disableService(request.getData().getServiceId()));
                default:
                    return ResponseEntity
                        .ok(KcspRestResponse.failed("Unsupported operation: " + request.getOpType()));
            }
        } catch (Exception e) {
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(KcspRestResponse.failed("Service sync failed: " + e.getMessage()));
        }
    }
}