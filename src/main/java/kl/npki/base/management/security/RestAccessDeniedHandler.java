package kl.npki.base.management.security;

import kl.nbase.helper.utils.JsonUtils;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.service.common.RestResponse;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 自定义权限不足处理器
 * 当用户已认证但没有访问特定资源的权限时触发，返回403错误
 */
@Component
public class RestAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                       AccessDeniedException accessDeniedException) throws IOException {

        // 设置响应内容类型和状态码
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);

        // 创建统一的错误响应
        RestResponse<Object> errorResponse = RestResponse.fail(
            ManagementValidationError.NO_PERMISSION.toException(accessDeniedException)
        );

        // 写入响应
        response.getWriter().write(JsonUtils.toJson(errorResponse));
    }
}
