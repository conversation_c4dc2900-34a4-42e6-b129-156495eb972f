package kl.npki.base.management.security;

import kl.nbase.helper.utils.JsonUtils;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.service.common.RestResponse;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 自定义未授权处理器
 * 当用户未认证（未登录或token无效）时触发，返回401错误
 *
 * <AUTHOR> href="mailto:<EMAIL>">dingqi</a>
 */
@Component
public class RestAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, 
                        AuthenticationException authException) throws IOException, ServletException {
        
        // 设置响应内容类型和状态码
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        
        // 创建统一的错误响应
        RestResponse<Object> errorResponse = RestResponse.fail(
            ManagementValidationError.LOGIN_STATUS_EXPIRED.toException(authException)
        );

        // 写入响应
        response.getWriter().write(JsonUtils.toJson(errorResponse));
    }
}