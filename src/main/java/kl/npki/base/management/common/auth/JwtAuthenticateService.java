package kl.npki.base.management.common.auth;

import kl.nbase.auth.core.IAuthenticateService;
import kl.nbase.auth.core.SpiMgr;
import kl.nbase.auth.credential.Credential;
import kl.nbase.auth.credential.CredentialFactory;
import kl.nbase.auth.entity.AuthenticationResult;
import kl.nbase.auth.entity.AuthnRequest;
import kl.nbase.auth.exception.AuthInternalError;
import kl.nbase.auth.exception.AuthValidationError;
import kl.nbase.auth.identifier.CredentialIdentifier;
import kl.nbase.auth.principal.Principal;
import kl.nbase.auth.token.AuthenticationToken;
import kl.nbase.auth.token.DefaultAuthenticationToken;
import kl.nbase.auth.token.store.TokenStore;
import kl.nbase.auth.utils.JwtHelper;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.management.utils.JwtSecretKeyUtil;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.util.EnvironmentUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.INCONSISTENT_IDENTITY_CREDENTIALS_FOR_MULTI_FACTOR_AUTHENTICATION_I18N_KEY;

/**
 * <AUTHOR>
 * Created on 2022/09/13 17:28
 */
@Service
public class JwtAuthenticateService implements IAuthenticateService {

    @Override
    public AuthenticationResult authenticate(AuthnRequest authnRequest) {
        // 构建认证凭证
        List<Credential> credentials = CredentialFactory.build(authnRequest);
        Principal principal = null;
        // 开启多因子认证，循环进行所有认证
        for (Credential credential : credentials) {
            // 选择认证处理器
            CredentialIdentifier identifier = chooseIdentifier(credential);
            // 认证获取身份信息
            Principal singlePrincipal = identifier.identifier(credential);
            if (principal != null) {
                // 多因子认证的身份凭证需要一致
                if (!principal.equals(singlePrincipal)) {
                    throw AuthValidationError.MULTIFACTORIAL_IDENTIFIER_DISCREPANCY_ERROR.toException(INCONSISTENT_IDENTITY_CREDENTIALS_FOR_MULTI_FACTOR_AUTHENTICATION_I18N_KEY, principal.getType(), singlePrincipal.getType());
                }
            } else {
                principal = singlePrincipal;
            }
        }

        // 对于多因子认证，进行必要的校验
        AuthenticationToken oldToken = checkOldToken(authnRequest);
        // 进行多因子判断并生成会话token
        AuthenticationToken authenticationToken = checkMfaAndGenerateToken(oldToken, authnRequest, principal);
        // 保存token
        SpiMgr.getInstance().getServices(TokenStore.class).get(0).save(authenticationToken);
        // 返回结果
        return AuthenticationResult.build(authenticationToken, null);
    }

    private CredentialIdentifier chooseIdentifier(Credential credential) {
        return SpiMgr.getInstance().getServices(CredentialIdentifier.class).stream().filter(identifier -> identifier.isSupport(credential)).findFirst().orElseThrow(AuthInternalError.UNSUPPORTED_CREDENTIAL::toException);
    }

    private AuthenticationToken checkOldToken(AuthnRequest authnRequest) {
        if (StringUtils.isNotEmpty(authnRequest.getToken())) {
            AuthenticationToken oldToken = SpiMgr.getInstance().getServices(TokenStore.class).get(0).get(authnRequest.getToken());
            CheckUtils.notNull(oldToken, AuthInternalError.INVALID_TOKEN.toException());
            return oldToken;
        }
        return null;
    }

    /**
     * 返回多因子成功的认证方式组合，例子：
     * 1）返回空不进行多因子认证
     * 2）返回口令+短信，证书两个List，表示这两种组合的任一种认证通过都算认证成功
     */
    protected List<Set<String>> getGoalAuthnTypes() {
        // 可配置
        return Collections.emptyList();
    }

    private AuthenticationToken checkMfaAndGenerateToken(AuthenticationToken oldToken, AuthnRequest authnRequest, Principal principal) {
        List<Set<String>> goalAuthnTypes = getGoalAuthnTypes();
        Set<String> successAuthnTypes = new HashSet<>();
        if (oldToken != null) {
            successAuthnTypes.addAll(oldToken.getAuthnTypes());
        }
        successAuthnTypes.addAll(authnRequest.getAuthnTypes());
        boolean isSuccess = false;
        if (goalAuthnTypes != null && !goalAuthnTypes.isEmpty()) {
            for (Set<String> goalAuthnType : goalAuthnTypes) {
                // 已成功的认证方式包含了所有需要认证的方式
                if (successAuthnTypes.containsAll(goalAuthnType)) {
                    isSuccess = true;
                    break;
                }
            }
        } else {
            // 未开启多因子认证，认证成功
            isSuccess = true;
        }
        if (oldToken == null) {
            String token = JwtHelper.createToken(JwtSecretKeyUtil.getSecretKey(),
                EnvironmentUtil.getTokenTimeByEnv(),
                successAuthnTypes, principal, isSuccess, RequestContextHolder.getContext().getIp());
            return new DefaultAuthenticationToken(token, successAuthnTypes, principal, isSuccess);
        } else {
            return new DefaultAuthenticationToken(oldToken.getToken(), successAuthnTypes, principal, isSuccess);
        }
    }
}
