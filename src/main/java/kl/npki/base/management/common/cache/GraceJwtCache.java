package kl.npki.base.management.common.cache;

import kl.npki.base.core.common.ICommonCache;


/**
 * 宽限期JWT缓存
 *
 * <AUTHOR>
 * @since 2025/05/09 10:06
 */
public enum GraceJwtCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:jwt:graceList";

    public void addToGraceList(String token, long expiration) {
        if (expiration > 0) {
            getClient().set(wrapCacheKey(token), 1, expiration);
        }
    }

    public boolean isInGracePeriod(String token) {
        return getClient().get(wrapCacheKey(token)) != null;
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }
}
