package kl.npki.base.management.common.gmcustom.tongweb;

import com.tongweb.connector.http11.AbstractHttp11JsseProtocol;
import com.tongweb.container.connector.Connector;
import com.tongweb.springboot.starter.TongWebServletWebServerFactory;
import com.tongweb.web.util.net.SSLHostConfig;
import com.tongweb.web.util.net.SSLHostConfigCertificate;
import kl.nbase.gmssl.tongweb.GMSSLImplementation;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.web.server.Ssl;
import org.springframework.boot.web.server.WebServerException;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import java.io.FileNotFoundException;

/**
 * 东方通Web容器国密SSL服务定制
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
public class GmSslTongWebServerCustomizer implements WebServerFactoryCustomizer<TongWebServletWebServerFactory> {

    private final int httpsPort;
    private final Ssl ssl;

    public GmSslTongWebServerCustomizer(ServerProperties serverProperties, int httpsPort) {
        this.httpsPort = httpsPort;
        this.ssl = serverProperties.getSsl();
    }

    @Override
    public void customize(TongWebServletWebServerFactory factory) {
        factory.addAdditionalTongWebConnectors(createConnector());
    }

    private Connector createConnector() {
        Connector connector = new Connector("HTTP/1.1");
        connector.setScheme("https");
        connector.setPort(httpsPort);
        connector.setSecure(true);

        AbstractHttp11JsseProtocol protocol = (AbstractHttp11JsseProtocol) connector.getProtocolHandler();
        protocol.setSSLEnabled(true);

        SSLHostConfig sslHostConfig = getGmSslHostConfig(protocol, ssl);

        protocol.addSslHostConfig(sslHostConfig);
        // 设置国密SSL实现
        protocol.setSslImplementationName(GMSSLImplementation.class.getName());

        return connector;
    }

    /**
     * 创建支持国密SSL的SSLHostConfig对象
     *
     * @param protocol
     * @return
     */
    private SSLHostConfig getGmSslHostConfig(AbstractHttp11JsseProtocol protocol, Ssl ssl) {
        SSLHostConfig sslHostConfig = new SSLHostConfig();
        sslHostConfig.setHostName(protocol.getDefaultSSLHostConfigName());
        sslHostConfig.setProtocols(ssl.getProtocol());
        sslHostConfig.setSslProtocol(ssl.getProtocol());
        configureSslClientAuth(sslHostConfig, ssl);
        String ciphers = StringUtils.arrayToCommaDelimitedString(ssl.getCiphers());
        if (StringUtils.hasText(ciphers)) {
            sslHostConfig.setCiphers(ciphers);
        }
        SSLHostConfigCertificate certificate = new SSLHostConfigCertificate(sslHostConfig, SSLHostConfigCertificate.Type.UNDEFINED);
        certificate.setCertificateKeystorePassword(ssl.getKeyStorePassword());
        certificate.setCertificateKeyAlias(ssl.getKeyAlias());
        sslHostConfig.addCertificate(certificate);

        if (ssl.getKeyStore() != null) {
            try {
                certificate.setCertificateKeystoreFile(ResourceUtils.getURL(ssl.getKeyStore()).toString());
            } catch (Exception ex) {
                throw new WebServerException("Could not load key store '" + ssl.getKeyStore() + "'", ex);
            }
        }
        if (ssl.getKeyStoreType() != null) {
            certificate.setCertificateKeystoreType(ssl.getKeyStoreType());
        }
        if (ssl.getKeyStoreProvider() != null) {
            certificate.setCertificateKeystoreProvider(ssl.getKeyStoreProvider());
        }
        if (ssl.getTrustStore() != null) {
            try {
                sslHostConfig.setTruststoreFile(ResourceUtils.getURL(ssl.getTrustStore()).toString());
            } catch (FileNotFoundException e) {
                throw new WebServerException("Could not load trust key store '" + ssl.getKeyStore() + "'", e);
            }
        }
        if (ssl.getTrustStorePassword() != null) {
            sslHostConfig.setTruststorePassword(ssl.getTrustStorePassword());
        }
        if (ssl.getTrustStoreType() != null) {
            sslHostConfig.setTruststoreType(ssl.getTrustStoreType());
        }
        if (ssl.getTrustStoreProvider() != null) {
            sslHostConfig.setTruststoreProvider(ssl.getTrustStoreProvider());
        }
        return sslHostConfig;
    }

    private void configureSslClientAuth(SSLHostConfig config, Ssl ssl) {
        if (ssl.getClientAuth() == Ssl.ClientAuth.NEED) {
            config.setCertificateVerification("required");
        }
        else if (ssl.getClientAuth() == Ssl.ClientAuth.WANT) {
            config.setCertificateVerification("optional");
        }
    }
}
