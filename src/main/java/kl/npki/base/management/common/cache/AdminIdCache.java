package kl.npki.base.management.common.cache;

import kl.npki.base.core.common.ICommonCache;

/**
 * 管理员id缓存，key为管理员id，value为token，用于限制同个账号不能同时有多人进行登录使用。
 *
 * <AUTHOR>
 * @Date 2023/9/13
 */
public enum AdminIdCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:adminId";

    public void set(String adminId, String token) {
        getClient().set(wrapCacheKey(adminId), token);
    }

    /**
     * 写入到指定环境的缓存中
     *
     * @param tenantId
     * @param adminId
     * @param token
     */
    public void set(String tenantId, String adminId, String token) {
        getClient(tenantId).set(wrapCacheKey(adminId, tenantId), token);
    }

    public String get(String adminId) {
        return getClient().get(wrapCacheKey(adminId));
    }

    /**
     * 从指定环境的缓存中获取
     *
     * @param tenantId
     * @param adminId
     * @return
     */
    public String get(String tenantId, String adminId) {
        return getClient(tenantId).get(wrapCacheKey(adminId));
    }

    public void remove(String adminId) {
        getClient().delete(wrapCacheKey(adminId));
    }

    /**
     * 从指定环境的缓存中移除
     *
     * @param tenantId
     * @param adminId
     */
    public void remove(String tenantId, String adminId) {
        getClient(tenantId).delete(wrapCacheKey(adminId));
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }

}
