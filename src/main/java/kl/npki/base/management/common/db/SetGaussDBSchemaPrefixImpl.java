package kl.npki.base.management.common.db;

import kl.nbase.db.support.spi.SetGaussDBSchemaPrefix;
import kl.npki.base.management.configurations.BaseManagementInitAutoConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 高斯数据库需要动态默认设置Schema,以便于在同一中间件中支持部署多个应用，
 * @ClassName:   SetGaussDBSchemaPrefixImpl
 * @Author:      zhangxiao
 * @Date:        2025-07-18 10:29:12
 * @Version:     v1.0.0
 * @Description:  这里的实现逻辑：通过不通的服务上下文，去区分不同的应用服务，以方式通过System.getProperty（）方式获取动态Schema冲突问题
 **/
public class SetGaussDBSchemaPrefixImpl implements SetGaussDBSchemaPrefix {

    private static final Logger logger = LoggerFactory.getLogger(SetGaussDBSchemaPrefixImpl.class);

    @Override
    public String getSchemaPrefix() {
        String contextPath = BaseManagementInitAutoConfiguration.contextPath;
        logger.info("gauss db schema prefix: {}", contextPath);
        return contextPath;
    }
}
