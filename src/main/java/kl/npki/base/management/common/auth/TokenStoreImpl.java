package kl.npki.base.management.common.auth;


import com.alibaba.druid.util.StringUtils;
import kl.nbase.auth.token.AuthenticationToken;
import kl.nbase.auth.token.store.TokenStore;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.constant.LoginConstant;
import kl.npki.base.management.common.cache.AdminIdCache;
import kl.npki.base.management.common.cache.TokenCache;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.service.util.EnvironmentUtil;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/9/14
 * @desc token存储实现类
 */
public class TokenStoreImpl implements TokenStore {

    /**
     * token缓存
     */
    private static final TokenCache TOKEN_CACHE = TokenCache.INSTANCE;

    /**
     * 使用该缓存在管理员id与token间建立映射关系
     */
    private static final AdminIdCache ADMIN_ID_CACHE = AdminIdCache.INSTANCE;

    /**
     * 记录下为部署者临时生成的token，以便在部署完成后从缓存移除token
     */
    private static final Set<String> TEMP_DEPLOYER_TOKENS = new HashSet<>();

    @Override
    public void save(AuthenticationToken authenticationToken) {
        // 刷新管理员id与token的映射
        ADMIN_ID_CACHE.set(authenticationToken.getPrincipal().getId(), authenticationToken.getToken());
        // 缓存token
        TOKEN_CACHE.set(authenticationToken, BaseConfigWrapper.getLoginConfig().getAccessTokenLifecycle());
    }

    @Override
    public AuthenticationToken get(String token) {
        // 获取token关联的信息
        AuthenticationToken authenticationToken = TOKEN_CACHE.get(token);
        // token缓存中没有该token相关联的信息，则认为登录状态已过期
        CheckUtils.notNull(authenticationToken, ManagementValidationError.LOGIN_STATUS_EXPIRED.toException());
        // 获取当前管理员最近一次缓存的token
        String latestToken = ADMIN_ID_CACHE.get(authenticationToken.getPrincipal().getId());
        // 当前管理员token和最近一次该管理员缓存的token不相等，则认为该账号已在其他地方登录
        CheckUtils.isTrue(StringUtils.equals(token, latestToken), ManagementValidationError.LOGIN_STATUS_HAS_BEEN_REPLACED.toException());
        // 刷新token缓存过期时间
        TOKEN_CACHE.set(authenticationToken, EnvironmentUtil.getTokenTimeByEnv());
        return authenticationToken;
    }

    @Override
    public void delete(String token) {
        AuthenticationToken authenticationToken = TOKEN_CACHE.get(token);
        if (Objects.isNull(authenticationToken)) {
            return;
        }
        // 移除token
        TOKEN_CACHE.remove(token);

        String id = ADMIN_ID_CACHE.get(authenticationToken.getPrincipal().getId());
        if (StringUtils.equals(token, ADMIN_ID_CACHE.get(id))) {
            // 移除管理员id与token的映射
            ADMIN_ID_CACHE.remove(id);
        }
    }

    private void delete(String tenantId, String token) {
        AuthenticationToken authenticationToken = TOKEN_CACHE.get(tenantId, token);
        if (Objects.isNull(authenticationToken)) {
            return;
        }
        // 移除token
        TOKEN_CACHE.remove(tenantId, token);

        String id = ADMIN_ID_CACHE.get(tenantId, authenticationToken.getPrincipal().getId());
        if (StringUtils.equals(token, ADMIN_ID_CACHE.get(id))) {
            // 移除管理员id与token的映射
            ADMIN_ID_CACHE.remove(tenantId, id);
        }
    }

    /**
     * 开始部署前登录的token是保存在demo环境的缓存中，而开始部署后会切换到default环境，所以需要为部署者拷贝一份临时token放在default环境的缓存中
     *
     * @param token
     */
    public void copyTokenForDeployer(String token) {
        // 从demo环境的缓存中获取token
        AuthenticationToken authenticationToken = TOKEN_CACHE.get(EnvironmentEnum.DEMO.getId(), token);
        // 复制到default环境的缓存中
        if (Objects.nonNull(authenticationToken)) {
            TEMP_DEPLOYER_TOKENS.add(authenticationToken.getToken());
            ADMIN_ID_CACHE.set(EnvironmentEnum.DEFAULT.getId(), authenticationToken.getPrincipal().getId(), token);
            TOKEN_CACHE.set(EnvironmentEnum.DEFAULT.getId(), authenticationToken, LoginConstant.DEPLOYER_TOKEN_TTL);
        }
    }

    /**
     * 移除为部署者临时生成的token
     */
    public void removeDeployerToken() {
        for (String token : TEMP_DEPLOYER_TOKENS) {
            delete(EnvironmentEnum.DEMO.getId(), token);
            delete(EnvironmentEnum.DEFAULT.getId(), token);
        }
    }

}
