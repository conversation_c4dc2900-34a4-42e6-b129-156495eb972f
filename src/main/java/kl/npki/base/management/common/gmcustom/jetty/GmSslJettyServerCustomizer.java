package kl.npki.base.management.common.gmcustom.jetty;

import kl.nbase.gmssl.common.GmSslConstants;
import kl.nbase.gmssl.utils.GmSslUtils;
import kl.npki.base.service.exception.BaseServiceInternalError;
import org.eclipse.jetty.http.HttpVersion;
import org.eclipse.jetty.server.*;
import org.eclipse.jetty.util.resource.Resource;
import org.eclipse.jetty.util.ssl.SslContextFactory;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.web.embedded.jetty.JettyServerCustomizer;
import org.springframework.boot.web.server.*;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URL;

/**
 * 国密SSL服务定制
 *
 * <AUTHOR>
 * @date 2023/4/24
 */
public class GmSslJettyServerCustomizer implements JettyServerCustomizer {

    private final InetSocketAddress address;
    private final Ssl ssl;
    private final SslStoreProvider sslStoreProvider;


    public GmSslJettyServerCustomizer(ServerProperties serverProperties, int httpsPort) {
        this.address = new InetSocketAddress(serverProperties.getAddress(), httpsPort);
        this.ssl = serverProperties.getSsl();
        this.sslStoreProvider = CertificateFileSslStoreProvider.from(serverProperties.getSsl());
    }

    @Override
    public void customize(Server server) {
        if (!ssl.isEnabled()) {
            // 未启用SSL，直接返回
            return;
        }

        SslContextFactory.Server sslContextFactory = new SslContextFactory.Server();
        sslContextFactory.setEndpointIdentificationAlgorithm(null);
        configureSsl(sslContextFactory, this.ssl, this.sslStoreProvider);
        ServerConnector connector = createConnector(server, sslContextFactory, this.address);
        server.addConnector(connector);
    }

    /**
     * 创建一个 ServerConnector
     *
     * @param server
     * @param sslContextFactory
     * @param address
     * @return
     */
    private ServerConnector createConnector(Server server, SslContextFactory.Server sslContextFactory,
                                            InetSocketAddress address) {
        HttpConfiguration config = new HttpConfiguration();
        config.setSendServerVersion(false);
        config.setSecureScheme("https");
        config.setSecurePort(address.getPort());
        config.addCustomizer(new SecureRequestCustomizer());
        HttpConnectionFactory connectionFactory = new HttpConnectionFactory(config);
        ServerConnector connector = new SslValidatingServerConnector(server, sslContextFactory, this.ssl.getKeyAlias(),
            createSslConnectionFactory(sslContextFactory, HttpVersion.HTTP_1_1.asString()), connectionFactory);
        connector.setPort(address.getPort());
        connector.setHost(address.getHostString());
        return connector;
    }

    /**
     * 自定义一个ssl连接工厂
     *
     * @param sslContextFactory
     * @param protocol
     * @return
     */
    private SslConnectionFactory createSslConnectionFactory(SslContextFactory.Server sslContextFactory,
                                                            String protocol) {
        try {
            return new SslConnectionFactory(sslContextFactory, protocol);
        } catch (NoSuchMethodError ex) {
            // Jetty 10
            try {
                return SslConnectionFactory.class.getConstructor(SslContextFactory.Server.class, String.class)
                    .newInstance(sslContextFactory, protocol);
            } catch (Exception ex2) {
                throw BaseServiceInternalError.CREATE_SSL_CONNECTION_FACTORY_FAILED.toException(ex2);
            }
        }
    }

    /**
     * Configure the SSL connection.
     *
     * @param factory          the Jetty {@link Server SslContextFactory.Server}.
     * @param ssl              the ssl details.
     * @param sslStoreProvider the ssl store provider
     */
    protected void configureSsl(SslContextFactory.Server factory, Ssl ssl, SslStoreProvider sslStoreProvider) {

        String protocol = ssl.getProtocol();
        if (isGmSslProtocol(protocol)) {
            // 仅在使用GMSSL协议时添加GM提供者，否则KeyManager或者TrustManager会使用错误
            factory.setProvider(GmSslConstants.DEFAULT_SSL_PROVIDER);
        }
        factory.setProtocol(protocol);

        configureSslClientAuth(factory, ssl);
        configureSslPasswords(factory, ssl);
        factory.setCertAlias(ssl.getKeyAlias());

        if (!ObjectUtils.isEmpty(ssl.getCiphers())) {
            factory.setIncludeCipherSuites(ssl.getCiphers());
            factory.setExcludeCipherSuites();
        }
        if (ssl.getEnabledProtocols() != null) {
            factory.setIncludeProtocols(ssl.getEnabledProtocols());
        }
        if (sslStoreProvider != null) {
            try {
                factory.setKeyStore(sslStoreProvider.getKeyStore());
                factory.setTrustStore(sslStoreProvider.getTrustStore());
            } catch (Exception ex) {
                throw new IllegalStateException("Unable to set SSL store", ex);
            }
        } else {
            configureSslKeyStore(factory, ssl);
            configureSslTrustStore(factory, ssl);
        }
    }

    private void configureSslClientAuth(SslContextFactory.Server factory, Ssl ssl) {
        if (ssl.getClientAuth() == Ssl.ClientAuth.NEED) {
            factory.setNeedClientAuth(true);
            factory.setWantClientAuth(true);
        } else if (ssl.getClientAuth() == Ssl.ClientAuth.WANT) {
            factory.setWantClientAuth(true);
        }
    }

    private void configureSslPasswords(SslContextFactory.Server factory, Ssl ssl) {
        if (ssl.getKeyStorePassword() != null) {
            factory.setKeyStorePassword(ssl.getKeyStorePassword());
        }
        if (ssl.getKeyPassword() != null) {
            factory.setKeyManagerPassword(ssl.getKeyPassword());
        }
    }

    private void configureSslKeyStore(SslContextFactory.Server factory, Ssl ssl) {
        try {
            URL url = ResourceUtils.getURL(ssl.getKeyStore());
            factory.setKeyStoreResource(Resource.newResource(url));
        } catch (Exception ex) {
            throw new WebServerException("Could not load key store '" + ssl.getKeyStore() + "'", ex);
        }
        if (ssl.getKeyStoreType() != null) {
            factory.setKeyStoreType(ssl.getKeyStoreType());
        }
        if (ssl.getKeyStoreProvider() != null) {
            factory.setKeyStoreProvider(ssl.getKeyStoreProvider());
        }
    }

    private void configureSslTrustStore(SslContextFactory.Server factory, Ssl ssl) {
        if (ssl.getTrustStorePassword() != null) {
            factory.setTrustStorePassword(ssl.getTrustStorePassword());
        }
        if (ssl.getTrustStore() != null) {
            try {
                URL url = ResourceUtils.getURL(ssl.getTrustStore());
                factory.setTrustStoreResource(Resource.newResource(url));
            } catch (IOException ex) {
                throw new WebServerException("Could not find trust store '" + ssl.getTrustStore() + "'", ex);
            }
        }
        if (ssl.getTrustStoreType() != null) {
            factory.setTrustStoreType(ssl.getTrustStoreType());
        }
        if (ssl.getTrustStoreProvider() != null) {
            factory.setTrustStoreProvider(ssl.getTrustStoreProvider());
        }
    }

    private boolean isGmSslProtocol(String protocol) {
        // 通过common-base-gmssl组件进行判断，如果有其他协议需要判定为GMSSL协议，自行补充
        return GmSslUtils.isGmSslProtocol(protocol);
    }

    /**
     * A {@link ServerConnector} that validates the ssl key alias on server startup.
     */
    static class SslValidatingServerConnector extends ServerConnector {

        private final SslContextFactory sslContextFactory;

        private final String keyAlias;

        SslValidatingServerConnector(Server server, SslContextFactory sslContextFactory, String keyAlias,
                                     SslConnectionFactory sslConnectionFactory, HttpConnectionFactory connectionFactory) {
            super(server, sslConnectionFactory, connectionFactory);
            this.sslContextFactory = sslContextFactory;
            this.keyAlias = keyAlias;
        }

        SslValidatingServerConnector(Server server, SslContextFactory sslContextFactory, String keyAlias,
                                     ConnectionFactory... factories) {
            super(server, factories);
            this.sslContextFactory = sslContextFactory;
            this.keyAlias = keyAlias;
        }

        @Override
        protected void doStart() throws Exception {
            super.doStart();
            SslConfigurationValidator.validateKeyAlias(this.sslContextFactory.getKeyStore(), this.keyAlias);
        }
    }
}
