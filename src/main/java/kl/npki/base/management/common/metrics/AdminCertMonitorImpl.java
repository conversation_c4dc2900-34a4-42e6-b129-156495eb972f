package kl.npki.base.management.common.metrics;

import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.biz.metrics.model.CertValidInfo;
import kl.npki.base.core.biz.metrics.service.CertMonitor;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.repository.IAdminCertRepository;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guoq
 * @Date: 2024/10/18
 * @description: 管理员证书指标收集
 */
public class AdminCertMonitorImpl implements CertMonitor {
    private static final String CERT_USE_AS ="ADMIN";

    @Override
    public List<CertValidInfo> collectCustomCertMetrics() {
        IAdminCertRepository adminCertRepository = RepositoryFactory.get(IAdminCertRepository.class);
        List<AdminCertEntity> adminCertEntities = adminCertRepository.findValidAll();
        List<CertValidInfo> certValidInfoList = new ArrayList<>();
        for (AdminCertEntity adminCertEntity : adminCertEntities) {
            Certificate certificate = CertUtil.parseCert(adminCertEntity.getSignCertValue());
            certValidInfoList.add(new CertValidInfo(certificate, CERT_USE_AS));
        }

        return certValidInfoList;
    }
}
