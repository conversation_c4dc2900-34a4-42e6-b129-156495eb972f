package kl.npki.base.management.common.cache;

import kl.npki.base.core.biz.security.model.UrlRoleInfo;
import kl.npki.base.core.common.ICommonCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;

/**
 * URL角色权限缓存
 * 用于缓存URL路径与角色权限的映射关系，提升权限验证性能
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
public enum UrlRoleCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:urlRole";
    private static final String URL_ROLE_LIST_KEY = "urlRoleList";
    /**
     * 缓存失效时间默认30分钟（毫秒）
     */
    private static final long TIME_TO_LIVE_MILLISECONDS = 30 * 60 * 1000L;

    private static final Logger LOG = LoggerFactory.getLogger(UrlRoleCache.class);

    /**
     * 缓存URL角色列表
     *
     * @param urlRoleInfoList URL角色信息列表
     */
    public void putUrlRoleList(List<UrlRoleInfo> urlRoleInfoList) {
        try {
            getClient().set(wrapCacheKey(URL_ROLE_LIST_KEY), (Serializable) urlRoleInfoList, TIME_TO_LIVE_MILLISECONDS);
            LOG.debug("URL role list cached successfully, total {} records",
                    urlRoleInfoList != null ? urlRoleInfoList.size() : 0);
        } catch (Exception e) {
            // 缓存加载不影响业务正常执行，所以直接记录日志
            LOG.error("Failed to cache URL role list", e);
        }
    }

    /**
     * 获取缓存的URL角色列表
     *
     * @return URL角色信息列表，如果缓存不存在或异常则返回null
     */
    public List<UrlRoleInfo> getUrlRoleList() {
        try {
            return getClient().get(wrapCacheKey(URL_ROLE_LIST_KEY));
        } catch (Exception e) {
            LOG.error("Failed to get URL role list from cache", e);
            return null;
        }
    }

    /**
     * 写入到指定环境的缓存中
     *
     * @param tenantId        租户ID
     * @param urlRoleInfoList URL角色信息列表
     */
    public void putUrlRoleList(String tenantId, List<UrlRoleInfo> urlRoleInfoList) {
        try {
            getClient(tenantId).set(wrapCacheKey(URL_ROLE_LIST_KEY, tenantId),
                    (Serializable) urlRoleInfoList, TIME_TO_LIVE_MILLISECONDS);
            LOG.debug("URL role list cached successfully for tenant [{}], total {} records", tenantId,
                    urlRoleInfoList != null ? urlRoleInfoList.size() : 0);
        } catch (Exception e) {
            LOG.error("Failed to cache URL role list for tenant [{}]", tenantId, e);
        }
    }

    /**
     * 从指定环境的缓存中获取
     *
     * @param tenantId 租户ID
     * @return URL角色信息列表，如果缓存不存在或异常则返回null
     */
    public List<UrlRoleInfo> getUrlRoleList(String tenantId) {
        try {
            return getClient(tenantId).get(wrapCacheKey(URL_ROLE_LIST_KEY, tenantId));
        } catch (Exception e) {
            LOG.error("Failed to get URL role list from cache for tenant [{}]", tenantId, e);
            return null;
        }
    }

    /**
     * 清除URL角色缓存
     */
    public void clearUrlRoleList() {
        try {
            getClient().delete(wrapCacheKey(URL_ROLE_LIST_KEY));
            LOG.debug("URL role list cache cleared successfully");
        } catch (Exception e) {
            LOG.error("Failed to clear URL role list cache", e);
        }
    }

    /**
     * 清除指定环境的URL角色缓存
     *
     * @param tenantId 租户ID
     */
    public void clearUrlRoleList(String tenantId) {
        try {
            getClient(tenantId).delete(wrapCacheKey(URL_ROLE_LIST_KEY, tenantId));
            LOG.debug("URL role list cache cleared successfully for tenant [{}]", tenantId);
        } catch (Exception e) {
            LOG.error("Failed to clear URL role list cache for tenant [{}]", tenantId, e);
        }
    }

    /**
     * 检查缓存是否存在
     *
     * @return true如果缓存存在，false如果不存在
     */
    public boolean isCacheExists() {
        try {
            return getClient().get(wrapCacheKey(URL_ROLE_LIST_KEY)) != null;
        } catch (Exception e) {
            LOG.error("Exception occurred while checking if URL role list cache exists", e);
            return false;
        }
    }

    /**
     * 检查指定环境的缓存是否存在
     *
     * @param tenantId 租户ID
     * @return true如果缓存存在，false如果不存在
     */
    public boolean isCacheExists(String tenantId) {
        try {
            return getClient(tenantId).get(wrapCacheKey(URL_ROLE_LIST_KEY, tenantId)) != null;
        } catch (Exception e) {
            LOG.error("Exception occurred while checking if URL role list cache exists for tenant [{}]", tenantId, e);
            return false;
        }
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }
}
