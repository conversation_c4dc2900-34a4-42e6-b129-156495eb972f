package kl.npki.base.management.constant;

/**
 * 正则表达式常量： 复制ca-core常量类
 *
 * <AUTHOR>
 * @date 2024/6/28 上午11:11
 */
public class RegPatternConstant {

    private RegPatternConstant() {

    }

    public static final String SEPARATOR = "|";

    /**
     * IPV4 正则表达式
     */
    public static final String REG_IP_V4 = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";

    /**
     * IPV6 正则表达式
     */
    public static final String REG_IP_V6 = "^([\\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|1?\\d\\d?)$|^([\\da-fA-F]{0,4}:):([\\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|1?\\d\\d?)$|^([\\da-fA-F]{1,4}:){2}:([\\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|1?\\d\\d?)$|^([\\da-fA-F]{1,4}:){3}:([\\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|1?\\d\\d?)$|^([\\da-fA-F]{1,4}:){4}:([\\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|1?\\d\\d?)$|^([\\da-fA-F]{1,4}:){5}:((25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|1?\\d\\d?)$|^([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4}$|^[\\da-fA-F]{0,4}:((:[\\da-fA-F]{1,4}){1,6}|:)$|^([\\da-fA-F]{1,4}:){2}((:[\\da-fA-F]{1,4}){1,5}|:)$|^([\\da-fA-F]{1,4}:){3}((:[\\da-fA-F]{1,4}){1,4}|:)$|^([\\da-fA-F]{1,4}:){4}((:[\\da-fA-F]{1,4}){1,3}|:)$|^([\\da-fA-F]{1,4}:){5}((:[\\da-fA-F]{1,4}){1,2}|:)$|^([\\da-fA-F]{1,4}:){6}:([\\da-fA-F]{1,4})?$|^([\\da-fA-F]{1,4}:){6}:$";

    /**
     * IPV4和IPV6正则表达式  ^(?:IPv4_REGEX)|(?:IPv6_REGEX)$
     */
    public static final String REG_IP_V4_OR_V6 = "((?:" + REG_IP_V4 + ")" + SEPARATOR + "(?:" + REG_IP_V6 + "))";


    /**
     * 支持非必填，有值时校验IPV4和IPV6正则表达式  ^$| ^(?:IPv4_REGEX)|(?:IPv6_REGEX)$
     */
    public static final String REG_IP_V4_OR_V6_OR_EMPTY = "(^$|(?:" + REG_IP_V4 + ")" + SEPARATOR + "(?:" + REG_IP_V6 + "))";

    /**
     * 域名正则表达式
     */
    private static final String REG_DOMAIN_NAME = "^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,}(?::\\d{1,5})?$";

    /**
     * IPV4和IPV6 域名正则表达式
     */
    public static final String REG_IP_V4_AND_V6_AND_DOMAIN = "((?:" + REG_IP_V4 + ")" + SEPARATOR + "(?:" + REG_IP_V6 + ")" + SEPARATOR + "(?:" + REG_DOMAIN_NAME + "))";

    /**
     * 服务地址：host:port正则校验格式
     */
    public static final String REG_HOST_AND_PORT = "(^(((?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}|(?:\\d{1,3}\\.){3}\\d{1,3}|(?:[0-9a-fA-F:]{2,39})):(6553[0-5]|655[0-2]\\d|65[0-4]\\d{2}|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3}))$)";

    /**
     * 证书CN正则表达式
     * 允许中文、英文字母（大小写）数字、下划线、中划线、空格(但不能出现在开头和结尾）
     */
    public static final String REG_CERT_COMMON_NAME = "^(?!\\s)(?:[A-Za-z0-9_\\-\\u4e00-\\u9fa5]+(?:\\s[A-Za-z0-9_\\-\\u4e00-\\u9fa5]*)*)$(?<!\\s)";

    /**
     * 证书DN除CN项外的正则表达式
     * 允许中文、英文字母（大小写）数字、下划线、中划线、空格(但不能出现在开头和结尾） 可以时空值
     */
    public static final String REG_CERT_SUBJECT_DN_NAME = "^(?:^(?!\\s)(?:[A-Za-z0-9_\\-\\u4e00-\\u9fa5]+(?:\\s[A-Za-z0-9_\\-\\u4e00-\\u9fa5]*)*)$(?<!\\s)|^$)";

    /**
     * name通用校验规则,必填参数校验
     * 不能在开头或结尾添加以下字符：空格、%20（空格编码）、%0a（换行符编码）、%00（结束符编码) 规则来自安全测试
     */
    public static final String COMMON_NAME_REGEX_TYPE = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$";

    /**
     * 定义一个匹配cert分表的正则表达式, 匹配T_主表名_route_seed_index, 主表名可以带数字T_主表名1_route_seed_index
     */
    public static final String SHARDING_TABLE_NAME_REGEX = "(^[Tt]_[A-Za-z_-]+[a-zA-Z0-9]+)_\\d+_\\d+_\\d+$";


    /**
     * url表达式正则,匹配以下格式之一： <br>
     * <p>
     * 1.http://域名:端口<br>
     * 2.https://域名:端口 <br>
     * 3.<a href="http://IP">...</a>:端口 <br>
     * 4.<a href="https://IP">...</a>:端口 <br>
     * <p>
     * http:// 或 https:// 是必填的的； <br>
     * 支持 IPv4 和 IPv6 地址；<br>
     * 端口号为合法 TCP 端口（1~65535）；<br>
     * 如果没有协议，则不能以 : 开头或结尾。<br>
     */
    public static final String REG_URL = "^(?:https?://)(?:(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}|(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|\\[(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\\])(?::([1-9]\\d{0,4}|[1-5]\\d{4}|6[0-4]\\d{3}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5]))?(/[a-zA-Z0-9._~!$&'()*+,;=:@-]+)*$";


    /**
     * ldap url表达式正则,匹配以下格式之一： <br>
     * <p>
     * 1.ldap://域名:端口<br>
     * 2.ldaps://域名:端口 <br>
     * 3.ldap://IP:端口 <br>
     * 4.ldaps://IP:端口 <br>
     * 5.域名 <br>
     * 6.IP <br>
     * 7.域名:端口 <br>
     * 8.IP:端口 <br>
     * <p>
     * ldap:// 或 ldaps:// 是可选的； <br>
     * 支持 IPv4 和 IPv6 地址；<br>
     * 端口号为合法 TCP 端口（1~65535）；<br>
     * 如果没有协议，则不能以 : 开头或结尾。<br>
     */
    public static final String REG_LAP_URL = "^(?:(?:ldaps?://)(?:(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}|(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|\\[(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\\])(?::([1-9]\\d{0,4}|[1-5]\\d{4}|6[0-4]\\d{3}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5]))?(/[a-zA-Z0-9._~!$&'()*+,;=:@-]+)*|(?:(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}|(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|\\[(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\\])(?::([1-9]\\d{0,4}|[1-5]\\d{4}|6[0-4]\\d{3}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5]))?(/[a-zA-Z0-9._~!$&'()*+,;=:@-]+)*)$";


    /**
     * 正则表达式拼接
     *
     * @param reg 需要拼接的正则表达式
     * @return 拼接后的正则表达式
     */
    public static String mergeRge(String... reg) {
        return String.join(SEPARATOR, reg);
    }

}
