package kl.npki.base.management.constant;

/**
 * 时间日期字段常量
 *
 * <AUTHOR>
 * @since 2025/3/5 15:40
 */
public class LocalDateFieldConstant {

    private LocalDateFieldConstant() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 开始时间
     */
    public static final String BEGIN_DATE = "beginDate";


    /**
     * 结束时间
     */
    public static final String END_DATE = "endDate";


    /**
     * 小于创建时间
     */
    public static final String LE_CREATE_AT = "leCreateAt";

    /**
     * 大于创建时间
     */
    public static final String GE_CREATE_AT = "geCreateAt";

}

