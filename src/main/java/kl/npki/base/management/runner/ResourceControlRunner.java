package kl.npki.base.management.runner;

import kl.npki.management.core.service.SecurityService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 资源控制器启动器处理特定业务
 *
 * <AUTHOR>
 * @since 2025/4/8 10:36
 */
@Component
public class ResourceControlRunner implements CommandLineRunner {

    @Resource
    private SecurityService securityService;


    @Override
    public void run(String... args) throws Exception {
        securityService.updateResourceAndRoleResourceData();
    }

}

