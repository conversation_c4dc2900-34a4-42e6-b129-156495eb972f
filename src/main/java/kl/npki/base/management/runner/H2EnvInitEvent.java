package kl.npki.base.management.runner;

import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * <AUTHOR>
 */
public class H2EnvInitEvent implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    /**
     * 初始化运行时的H2数据库文件目录
     */
    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        System.setProperty("h2.baseDir", System.getProperty("user.dir"));
    }
}
