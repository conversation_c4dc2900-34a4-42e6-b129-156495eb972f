package kl.npki.base.management.runner;

import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.management.core.service.config.ProtectConfigFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import static kl.npki.management.core.service.config.ProtectConfigFileService.TOBE_PROTECT_CONFIG_FILE_PATH_LIST;

/**
 * 配置文件完整性数据检查
 *
 * <AUTHOR>
 */
@Component
public class ConfigFileFullDataCheckRunner implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(ConfigFileFullDataCheckRunner.class);

    private final ProtectConfigFileService protectConfigFileService = new ProtectConfigFileService();

    @Override
    public void run(String... args) {
        String environmentSwitchId = BaseConfigWrapper.getSysInfoConfig().getEnvironmentSwitchId();
        // 部署阶段或未开启完整性保护功能 不生成日志的完整性值
        if (!BaseConfigWrapper.getSysConfig().isOpenDataFull()
            || EnvironmentEnum.DEMO.getId().equals(environmentSwitchId)) {
            return;
        }

        for (String filePath : TOBE_PROTECT_CONFIG_FILE_PATH_LIST) {
            boolean result = protectConfigFileService.checkDataFull(filePath);
            if (!result) {
                log.error("Integrity verification failed, service exited!");
                // 完整性校验不通过
                System.exit(0);
            }
        }
    }
}
