package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.management.repository.entity.TRoleDO;
import kl.npki.base.management.repository.mapper.TRoleMapper;
import kl.npki.management.core.biz.permission.response.RoleListResponse;
import kl.npki.management.core.repository.IRoleMgrRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/10/31 14:46
 * @desc
 */
@Service
public class RoleMgrRepositoryImpl extends KlServiceImpl<TRoleMapper, TRoleDO> implements IRoleMgrRepository {
    
    @Resource
    private ConvertService convertService;
    
    public IPage<RoleListResponse> queryRoleList(IPage<TRoleDO> page, QueryWrapper<TRoleDO> queryWrapper) {
        IPage<TRoleDO> roleDoPage = page(page, queryWrapper);
        return roleDoPage.convert(v -> convertService.convert(v, RoleListResponse.class));
    }

}
