package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.LoginType;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.repository.entity.TAdminCertDO;
import kl.npki.base.management.repository.mapper.TAdminCertMapper;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.repository.IAdminCertRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.ADMINISTRATOR_CERTIFICATE_INFORMATION_INTEGRITY_VERIFICATION_FAILED_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc 系统管理员证书仓库接口实现
 */
@Service
public class AdminCertRepositoryImpl extends KlServiceImpl<TAdminCertMapper, TAdminCertDO> implements IAdminCertRepository {

    private static final Logger logger = LoggerFactory.getLogger(AdminCertRepositoryImpl.class);

    @Resource
    private ConvertService convertService;

    @Override
    public List<AdminCertEntity> findValidAll() {
        List<TAdminCertDO> adminCertDOList = baseMapper.selectList(Wrappers
            .lambdaQuery(TAdminCertDO.class)
            .in(TAdminCertDO::getStatus, CertStatus.getAllowedLoginStatusSet())
            .eq(TAdminCertDO::getIsDelete, 0)
        );
        for (TAdminCertDO tAdminCertDO : adminCertDOList) {
            tAdminCertDO.verifyIntegrity();
        }
        return convertService.convert(adminCertDOList, AdminCertEntity.class);
    }

    @Override
    public Long save(AdminCertEntity adminCertEntity) {
        CheckUtils.notNull(adminCertEntity, ManagementValidationError.PARAM_ERROR.toException());
        TAdminCertDO certDO = convertService.convert(adminCertEntity, TAdminCertDO.class);
        try {
            this.save(certDO);
        } catch (Exception e) {
            throw ManagementInternalError.DB_INSERT_ERROR.toException(e);
        }
        return certDO.getId();
    }

    @Override
    public void update(AdminCertEntity adminCertEntity) {
        CheckUtils.notNull(adminCertEntity, ManagementValidationError.PARAM_ERROR.toException());
        TAdminCertDO convert = convertService.convert(adminCertEntity, TAdminCertDO.class);
        try {
            super.updateById(convert);
        } catch (Exception e) {
            throw ManagementInternalError.DB_UPDATE_ERROR.toException(e);
        }
    }

    @Override
    public void delete(Long id) {
        try {
            removeById(id);
        } catch (Exception e) {
            throw ManagementInternalError.DB_DELETE_ERROR.toException(e);
        }
    }

    @Override
    public AdminCertEntity getByAdminInfoId(Long adminInfoId) {

        LambdaQueryWrapper<TAdminCertDO> queryWrapper = Wrappers
            .lambdaQuery(TAdminCertDO.class)
            .eq(TAdminCertDO::getAdminInfoId, adminInfoId);
        TAdminCertDO certDO;
        try {
            certDO = getOne(queryWrapper);
        } catch (Exception e) {
            throw ManagementInternalError.DB_QUERY_ERROR.toException(e);
        }
        boolean deployed = SystemUtil.isDeployed();
        if (deployed) {
            // 正式环境证书不能为空
            CheckUtils.notNull(certDO, ManagementValidationError.PARAM_ERROR.toException());
            if (certDO.isVerificationRequired()) {
                // 验证管理员证书完整性
                boolean adminCertResult = certDO.checkIntegrity();
                if (adminCertResult) {
                    logger.info("Successfully verified the integrity of the administrator's [{}] certificate", certDO.getSubjectCn());
                } else {
                    logger.error("Verification of administrator [{}] certificate integrity failed", certDO.getSubjectCn());
                    throw ManagementInternalError.VERIFY_FULL_DATA_HASH_ERROR.toException(ADMINISTRATOR_CERTIFICATE_INFORMATION_INTEGRITY_VERIFICATION_FAILED_I18N_KEY, certDO.getSubjectCn());
                }
            }
        } else if (certDO == null) {
            // 测试环境证书可以为空，是预置管理员
            return null;
        }
        return convertService.convert(certDO, AdminCertEntity.class);
    }

    @Override
    public AdminCertEntity getByCertSn(String certSn) {
        LambdaQueryWrapper<TAdminCertDO> queryWrapper = Wrappers
            .lambdaQuery(TAdminCertDO.class)
            .eq(TAdminCertDO::getSignCertSn,
                certSn.toUpperCase(Locale.ROOT));
        TAdminCertDO adminCertDO;
        try {
            adminCertDO = getOne(queryWrapper);
        } catch (Exception e) {
            throw ManagementInternalError.DB_QUERY_ERROR.toException(e);
        }
        CheckUtils.notNull(adminCertDO, ManagementValidationError.PARAM_ERROR.toException());
        // 校验数据完整性
        adminCertDO.verifyIntegrity();
        return convertService.convert(adminCertDO, AdminCertEntity.class);
    }

    /**
     * 是否存在同名的subjectCn
     *
     * @param subjectCn
     * @return
     */
    @Override
    public boolean existSubjectCn(String subjectCn) {
        LambdaQueryWrapper<TAdminCertDO> queryWrapper = Wrappers.lambdaQuery(TAdminCertDO.class);
        queryWrapper.eq(TAdminCertDO::getSubjectCn, subjectCn);
        queryWrapper.ne(TAdminCertDO::getStatus, CertStatus.TO_BE_ISSUED.getCode());
        return count(queryWrapper) != 0;
    }

    @Override
    public List<String> getSignCertSnList() {
        return baseMapper.queryLoginCertSnList();
    }
}
