package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.management.repository.entity.TSystemConfigDO;
import kl.npki.base.management.repository.mapper.TSystemConfigMapper;
import kl.npki.management.core.biz.config.model.SystemConfigEntity;
import kl.npki.management.core.repository.ISystemConfigRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc 系统管理员证书仓库接口实现
 */
@Service
public class SystemConfigRepositoryImpl extends KlServiceImpl<TSystemConfigMapper, TSystemConfigDO> implements ISystemConfigRepository {

    @Resource
    private ConvertService convertService;

    @Override
    public void save(SystemConfigEntity systemConfigEntity) {
        save(convertService.convert(systemConfigEntity, TSystemConfigDO.class));
    }

    @Override
    public void update(SystemConfigEntity systemConfigEntity) {
        updateById(convertService.convert(systemConfigEntity, TSystemConfigDO.class));
    }

    @Override
    public SystemConfigEntity getConfigByConfigFileName(String configFileName) {
        LambdaQueryWrapper<TSystemConfigDO> queryWrapper = Wrappers
            .lambdaQuery(TSystemConfigDO.class)
            .eq(TSystemConfigDO::getConfigFileName, configFileName);
        TSystemConfigDO tSystemConfigDO = baseMapper.selectOne(queryWrapper);

        return convertService.convert(tSystemConfigDO, SystemConfigEntity.class);
    }

    @Override
    public List<SystemConfigEntity> queryForList(Boolean isComplete) {
        List<TSystemConfigDO> systemConfigDOList;
        if (isComplete != null) {
            LambdaQueryWrapper<TSystemConfigDO> queryWrapper = Wrappers
                .lambdaQuery(TSystemConfigDO.class)
                .eq(TSystemConfigDO::getIsComplete, isComplete);
            systemConfigDOList = list(queryWrapper);
        } else {
            systemConfigDOList = list();
        }

        List<SystemConfigEntity> systemConfigEntities = systemConfigDOList.stream()
            .map(v -> convertService.convert(v, SystemConfigEntity.class))
            .collect(Collectors.toList());
        return systemConfigEntities;
    }

    @Override
    public void saveBatch(List<SystemConfigEntity> configEntityList) {
        List<TSystemConfigDO> systemConfigEntities = configEntityList.stream()
            .map(v -> convertService.convert(v, TSystemConfigDO.class))
            .collect(Collectors.toList());

        super.saveBatch(systemConfigEntities);
    }
}
