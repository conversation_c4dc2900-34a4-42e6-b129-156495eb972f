package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.repository.entity.TAdminInfoDO;
import kl.npki.base.management.repository.mapper.TAdminInfoMapper;
import kl.npki.management.core.biz.admin.model.dto.AdminInfoListDTO;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.info.AdminInfoDetailInfo;
import kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo;
import kl.npki.management.core.repository.IAdminInfoMgrRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.ADMINISTRATOR_CERTIFICATE_INFORMATION_INTEGRITY_VERIFICATION_FAILED_I18N_KEY;
import static kl.npki.base.management.constant.I18nExceptionInfoConstant.DATA_IS_EMPTY_I18N_KEY;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/02
 * @desc 管理员信息下存储库实现类
 */
@Service
public class AdminInfoMgrRepositoryImpl extends KlServiceImpl<TAdminInfoMapper, TAdminInfoDO> implements IAdminInfoMgrRepository {

    private static final Logger logger = LoggerFactory.getLogger(AdminInfoMgrRepositoryImpl.class);

    @Resource
    private ConvertService convertService;

    @Override
    public IPage<AdminInfoListInfo> queryForList(IPage<AdminInfoListInfo> page, AdminInfoListDTO adminInfoListQuery,
                                                 Set<String> roleCodes) {
        return baseMapper.queryForList(page, adminInfoListQuery, roleCodes, null, null);
    }

    @Override
    public IPage<AdminInfoListInfo> queryForToBeCheckedList(IPage<AdminInfoListInfo> page, AdminInfoListDTO adminInfoListQuery,
                                                 Set<String> roleCodes, Set<Integer> toBeCheckedUserStatusSet, Set<Integer> toBeCheckedCertStatusSet) {
        return baseMapper.queryForList(page, adminInfoListQuery, roleCodes, toBeCheckedUserStatusSet, toBeCheckedCertStatusSet);
    }

    @Override
    public AdminInfoDetailInfo queryAdminDetail(Long adminInfoId, Set<String> roleSet) {
        AdminInfoDetailInfo detail = baseMapper.queryForDetail(adminInfoId, roleSet);
        CheckUtils.notNull(detail, ManagementInternalError.DB_QUERY_ERROR.toException(DATA_IS_EMPTY_I18N_KEY));
        return detail;
    }

    @Override
    public AdminEntity getById(Long adminInfoId) {
        TAdminInfoDO infoDO = super.getById(adminInfoId);
        CheckUtils.notNull(infoDO, ManagementInternalError.DB_QUERY_ERROR.toException(DATA_IS_EMPTY_I18N_KEY));
        // 校验数据完整性
        infoDO.verifyIntegrity();
        return convertService.convert(infoDO, AdminEntity.class);
    }


    @Override
    public AdminEntity getByUsername(String username) {
        LambdaQueryWrapper<TAdminInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TAdminInfoDO::getUsername, username);
        TAdminInfoDO adminInfoDO = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(adminInfoDO)) {
            int count = baseMapper.checkAdminIsCancelled(username);
            if (count > 0) {
                throw ManagementValidationError.ADMIN_IS_CANCEL.toException();
            }
            throw ManagementValidationError.ADMIN_USERNAME_OR_PWD_ERROR.toException();
        }
        // 验证管理员完整性
        if (adminInfoDO.isVerificationRequired()) {
            boolean adminResult = adminInfoDO.checkIntegrity();
            if (adminResult) {
                logger.info("Successfully verified the integrity of administrator [{}]", adminInfoDO.getUsername());
            } else {
                logger.error("Verification of administrator [{}] certificate integrity failed",  adminInfoDO.getUsername());
                throw ManagementInternalError.VERIFY_FULL_DATA_HASH_ERROR.toException(ADMINISTRATOR_CERTIFICATE_INFORMATION_INTEGRITY_VERIFICATION_FAILED_I18N_KEY,  adminInfoDO.getUsername());
            }
        }
        return convertService.convert(adminInfoDO, AdminEntity.class);
    }
}
