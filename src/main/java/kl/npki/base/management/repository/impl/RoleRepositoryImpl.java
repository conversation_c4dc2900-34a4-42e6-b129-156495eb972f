package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.biz.dataprotect.service.ICustomGenOriDataService;
import kl.npki.base.core.biz.security.model.UrlRoleInfo;
import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.DataProtectUtil;
import kl.npki.base.management.common.cache.RoleCache;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.repository.entity.TRoleDO;
import kl.npki.base.management.repository.entity.TRoleResourceLinkDO;
import kl.npki.base.management.repository.mapper.TRoleMapper;
import kl.npki.base.management.repository.mapper.TRoleResourceLinkMapper;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.constants.RoleType;
import kl.npki.management.core.repository.IRoleRepository;
import kl.npki.management.core.repository.IRoleResourceLinkMgrRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.CHARACTER_INFORMATION_INTEGRITY_VERIFICATION_FAILED_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc 系统管理员证书仓库接口实现
 */
@Service
public class RoleRepositoryImpl extends KlServiceImpl<TRoleMapper, TRoleDO> implements IRoleRepository, ICustomGenOriDataService<TRoleDO> {
    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(RoleRepositoryImpl.class);

    @Resource
    private ConvertService convertService;

    @Resource
    private TRoleResourceLinkMapper tRoleResourceLinkMapper;

    @Resource
    private NpkiServerConfig npkiServerConfig;

    @Override
    public List<RoleEntity> getAllRole() {
        // 先尝试从缓存获取
        List<RoleEntity> cachedRoles = RoleCache.INSTANCE.getAllRoles();
        if (cachedRoles != null) {
            logger.debug("Retrieved {} roles from cache", cachedRoles.size());
            return cachedRoles;
        }
        return refreshRoleCache();
    }

    @Override
    public List<RoleEntity> getAllSystemRole() {
        // 先尝试从缓存获取
        List<RoleEntity> cachedRoles = RoleCache.INSTANCE.getAllSystemRoles();
        if (cachedRoles != null) {
            logger.debug("Retrieved {} system roles from cache", cachedRoles.size());
            return cachedRoles;
        }
        // 缓存中没有角色列表，从数据库查询
        LambdaQueryWrapper<TRoleDO> queryWrapper = Wrappers.lambdaQuery(TRoleDO.class);
        queryWrapper.eq(TRoleDO::getIsCustom, RoleType.SYS_ROLE.getCode());
        List<TRoleDO> roleDOList = baseMapper.selectList(queryWrapper);
        List<RoleEntity> roles = roleDOList
            .stream()
            .map(v -> convertService.convert(v, RoleEntity.class))
            .collect(Collectors.toList());

        // 刷新缓存结果
        if (CollectionUtils.isNotEmpty(roles)) {
            refreshRoleCache();
            logger.debug("Cached {} system roles after database query", roles.size());
        } else {
            logger.debug("No system roles found in database");
        }
        return roles;
    }


    @Override
    public Boolean checkRoleNameRepeat(String roleName) {
        try {
            RoleCodeEnum roleCodeEnum = RoleCodeEnum.getByRoleName(roleName);
            return roleCodeEnum != null;
        } catch (IllegalArgumentException e) {
            long count = count(Wrappers
                .lambdaQuery(TRoleDO.class)
                .eq(TRoleDO::getRoleName, roleName));
            return 0 != count;
        }
    }

    @Override
    public Boolean checkRoleExists(String roleCode) {
        RoleCodeEnum roleCodeEnum = RoleCodeEnum.getRoleCodeEnumByRoleCode(roleCode);
        if (roleCodeEnum != null) {
            return true;
        }
        long count = count(Wrappers
            .lambdaQuery(TRoleDO.class)
            .eq(TRoleDO::getRoleCode, roleCode));
        return 0 != count;
    }

    @Override
    public Boolean saveRole(RoleEntity roleEntity) {
        CheckUtils.notNull(roleEntity, ManagementValidationError.PARAM_ERROR.toException());
        TRoleDO roleDO = convertService.convert(roleEntity, TRoleDO.class);
        try {
            boolean result = save(roleDO);
            if (result) {
                // 清除相关缓存
                RoleCache.INSTANCE.clearAllRoles();
                logger.debug("Cleared role caches after saving role [{}]", roleEntity.getRoleCode());
            }
            return result;
        } catch (Exception e) {
            throw ManagementInternalError.DB_INSERT_ERROR.toException(e);
        }
    }

    @Override
    public RoleEntity getRoleById(Long roleId) {
        // 先尝试从缓存获取
        RoleEntity cachedRole = RoleCache.INSTANCE.getRoleById(roleId);
        if (cachedRole != null) {
            logger.debug("Retrieved role [{}] from cache", roleId);
            return cachedRole;
        }

        // 缓存中没有角色列表或查找不到该角色，从数据库查询
        TRoleDO roleDO;
        try {
            roleDO = getById(roleId);
        } catch (Exception e) {
            throw ManagementInternalError.DB_QUERY_ERROR.toException(e);
        }
        CheckUtils.notNull(roleDO, ManagementValidationError.PARAM_ERROR.toException());
        
        RoleEntity role = convertService.convert(roleDO, RoleEntity.class);
        
        // 清除缓存，这样下次查询 getAllRoles() 时会重新从数据库加载最新的角色列表
        RoleCache.INSTANCE.clearAllRoles();
        logger.debug("Retrieved role [{}] from database and cleared cache for refresh", roleId);
        
        return role;
    }

    @Override
    public RoleEntity getRoleByCode(String roleCode) {
        CheckUtils.notNull(roleCode, ManagementValidationError.PARAM_ERROR.toException());
        
        // 先尝试从缓存获取
        RoleEntity cachedRole = RoleCache.INSTANCE.getRoleByCode(roleCode);
        if (cachedRole != null) {
            logger.debug("Retrieved role [{}] from cache", roleCode);
            return cachedRole;
        }

        // 缓存中没有角色列表或查找不到该角色，从数据库查询
        TRoleDO tRoleDO = getOne(Wrappers
            .lambdaQuery(TRoleDO.class)
            .eq(TRoleDO::getRoleCode, roleCode)
            .eq(TRoleDO::getIsDelete, 0));
        if (ObjectUtils.isNotEmpty(tRoleDO) && tRoleDO.isVerificationRequired()) {
            boolean roleResult = tRoleDO.checkIntegrity();
            if (roleResult) {
                logger.info("Successfully verified the integrity of role [{}]", tRoleDO.getRoleName());
            } else {
                logger.error("Failed to verify the integrity of role [{}]", tRoleDO.getRoleName());
                throw ManagementInternalError.VERIFY_FULL_DATA_HASH_ERROR.toException(CHARACTER_INFORMATION_INTEGRITY_VERIFICATION_FAILED_I18N_KEY, tRoleDO.getRoleName());
            }
        }

        RoleEntity role = convertService.convert(tRoleDO, RoleEntity.class);
        
        // 清除缓存，这样下次查询 getAllRoles() 时会重新从数据库加载最新的角色列表
        if (role != null) {
            RoleCache.INSTANCE.clearAllRoles();
            logger.debug("Retrieved role [{}] from database and cleared cache for refresh", roleCode);
        }

        return role;
    }

    @Override
    public String getRoleNameByCode(String roleCode) {
        // 支持系统内置角色的快速获取，适用部署阶段
        RoleCodeEnum roleCodeEnumByRoleCode = RoleCodeEnum.getRoleCodeEnumByRoleCode(roleCode);
        if (roleCodeEnumByRoleCode != null) {
            return roleCodeEnumByRoleCode.getDesc();
        }
        RoleEntity roleByCode = getRoleByCode(roleCode);
        if (roleByCode == null) {
            throw ManagementValidationError.ROLE_NOT_EXISTS_BY_CODE.toException(roleCode);
        }
        return roleByCode.getRoleName();
    }

    @Override
    @KlTransactional
    public void forbidRole(RoleEntity roleEntity) {
        CheckUtils.notNull(roleEntity, ManagementValidationError.PARAM_ERROR.toException());
        // 设置状态为删除
        TRoleDO roleDO = convertService.convert(roleEntity, TRoleDO.class);
        getBaseMapper().deleteById(roleDO);
        // 删除角色对应的资源链接和API资源链接
        try {
            tRoleResourceLinkMapper.delete(Wrappers.lambdaQuery(TRoleResourceLinkDO.class)
                .eq(TRoleResourceLinkDO::getRoleCode, roleEntity.getRoleCode()));
            refreshRoleCache();
            logger.debug("Cleared role caches after forbidding role [{}]", roleEntity.getRoleCode());
        } catch (Exception e) {
            throw ManagementInternalError.DB_DELETE_ERROR.toException(e);
        }
    }

    private List<RoleEntity> refreshRoleCache() {
        LambdaQueryWrapper<TRoleDO> queryWrapper = Wrappers.lambdaQuery(TRoleDO.class);
        // 逻辑删除，添加状态进行筛选
        queryWrapper.eq(TRoleDO::getIsDelete, false);
        List<TRoleDO> roleDOList = baseMapper.selectList(queryWrapper);
        List<RoleEntity> roles = roleDOList
            .stream()
            .map(v -> convertService.convert(v, RoleEntity.class))
            .collect(Collectors.toList());

        // 缓存结果
        RoleCache.INSTANCE.putAllRoles(roles);
        logger.debug("Cached {} roles after database query", roles.size());
        return roles;
    }

    @Override
    public void updateRoleByCode(RoleEntity roleEntity) {
        CheckUtils.notNull(roleEntity, ManagementValidationError.PARAM_ERROR.toException());
        TRoleDO roleDO = convertService.convert(roleEntity, TRoleDO.class);
        try {
            LambdaUpdateWrapper<TRoleDO> updateWrapper = Wrappers.lambdaUpdate(TRoleDO.class);
            updateWrapper.eq(TRoleDO::getRoleCode, roleEntity.getRoleCode());
            update(roleDO, updateWrapper);
            refreshRoleCache();
        } catch (Exception e) {
            throw ManagementInternalError.DB_UPDATE_ERROR.toException(e);
        }
    }

    /**
     * 检查角色编码是否重复
     *
     * @param roleCode 角色编码
     * @return true 为重复 false 为未重复
     */
    @Override
    public Boolean checkRoleCodeRepeat(String roleCode) {
        RoleCodeEnum adminRole = RoleCodeEnum.getRoleCodeEnumByRoleCode(roleCode);
        if (ObjectUtils.isEmpty(adminRole)) {
            long count = count(Wrappers
                .lambdaQuery(TRoleDO.class)
                .eq(TRoleDO::getRoleCode, roleCode));
            return 0 != count;
        }
        return true;
    }

    @Override
    public List<String> getChosenResourceCodesByRoleCode(String roleCode) {
        // 首先根据roleCode查询角色
        RoleEntity role = getRoleByCode(roleCode);
        if (role == null) {
            throw ManagementValidationError.ROLE_NOT_EXISTS_BY_CODE.toException(roleCode);
        }
        // 根据角色编码查询关联的资源编码
        LambdaQueryWrapper<TRoleResourceLinkDO> lambdaQueryWrapper = new LambdaQueryWrapper<TRoleResourceLinkDO>().select(TRoleResourceLinkDO::getResourceCode)
            .eq(TRoleResourceLinkDO::getRoleCode, roleCode);
        List<TRoleResourceLinkDO> roleResourceLinks = tRoleResourceLinkMapper.selectList(lambdaQueryWrapper);
        return roleResourceLinks.stream()
            .map(TRoleResourceLinkDO::getResourceCode)
            .collect(Collectors.toList());
    }

    @Override
    public List<RoleEntity> getSystemRoleList() {
        LambdaQueryWrapper<TRoleDO> queryWrapper = Wrappers.lambdaQuery(TRoleDO.class);
        queryWrapper.eq(TRoleDO::getIsCustom, RoleType.SYS_ROLE.getCode());
        List<TRoleDO> roleDOList = baseMapper.selectList(queryWrapper);
        return roleDOList
            .stream()
            .map(v -> convertService.convert(v, RoleEntity.class))
            .collect(Collectors.toList());
    }

    @Override
    public String genOriData(TRoleDO roleDO) {
        // 角色信息的原文值
        String preOriData = DataProtectUtil.genDataValue(roleDO);
        // 查找该角色对应的资源信息
        List<String> resourceList = RepositoryFactory.get(IRoleResourceLinkMgrRepository.class).queryResourceByRoles(roleDO.getRoleCode());
        String resource = StringUtils.join(resourceList, ",");

        String oriData = preOriData + resource;
        // 拼接角色与资源，作为角色表的完整性原文值
        logger.debug("roleId: {}, corresponding to the original value of data integrity: {}", roleDO.getId(), oriData);
        return oriData;
    }

    @Override
    public List<RoleEntity> getRoleByResourceCode(String resourceCode) {
        List<TRoleDO> roleDOList = baseMapper.selectRoleByResourceCode(resourceCode);
        return convertService.convert(roleDOList, RoleEntity.class);
    }

    @Override
    public List<RoleEntity> getRoleByParentRoleCode(String parentRoleCode) {
        CheckUtils.notEmpty(parentRoleCode, ManagementValidationError.PARAM_ERROR.toException());
        // 先尝试从缓存获取
        List<RoleEntity> cachedRoles = RoleCache.INSTANCE.getRoleByParentRoleCode(parentRoleCode);
        if (cachedRoles != null) {
            logger.debug("Retrieved roles by parent role code [{}] from cache", parentRoleCode);
            return cachedRoles;
        }
        // 缓存中没有角色列表或查找不到该角色，从数据库查询
        LambdaQueryWrapper<TRoleDO> queryWrapper = Wrappers.lambdaQuery(TRoleDO.class);
        queryWrapper.eq(TRoleDO::getParentRoleCode, parentRoleCode)
            .eq(TRoleDO::getIsDelete, false);
        List<TRoleDO> roleDOList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(roleDOList)) {
            return Collections.emptyList();
        }
        List<RoleEntity> roles = roleDOList
            .stream()
            .map(v -> convertService.convert(v, RoleEntity.class))
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(roles)) {
            // 缓存需要刷新
            RoleCache.INSTANCE.clearAllRoles();
        }
        return roles;
    }

    @Override
    public List<UrlRoleInfo> listUrlRoles() {
        String apiPrefix = npkiServerConfig.getApiPrefix();
        List<UrlRoleInfo> urlRoleInfos = this.getBaseMapper().listUrlRoles();
        if (CollectionUtils.isEmpty(urlRoleInfos)) {
            // 如果没有URL角色信息，直接返回空列表
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(apiPrefix)) {
            // 如果没有配置API前缀，则直接返回原始的URL角色信息
            return urlRoleInfos;
        }
        // 添加API前缀
        return urlRoleInfos.stream()
            .map(urlRoleInfo -> {
                String url = urlRoleInfo.getUrl();
                if (StringUtils.isNotBlank(url) && !url.startsWith(apiPrefix)) {
                    urlRoleInfo.setUrl(apiPrefix + url);
                }
                return urlRoleInfo;
            })
            .collect(Collectors.toList());
    }

    @Override
    public void batchUpdateI18N() {
        super.updateBatchById(super.list());
    }

    @Override
    public Set<String> getQueryRoleCodeSet(Collection<String> roleCodes) {
        return getQueryRoleCodeSet(roleCodes, false);
    }

    @Override
    public Set<String> getQueryRoleCodeSet(Collection<String> roleCodes, boolean includeOneself) {
        List<RoleEntity> roleEntityList = getAllRole();
        // 获取除顶级管理员的所有角色编码
        Set<String> roleCodesExcludeSupervisorRoles = roleEntityList.stream()
            .map(RoleEntity::getRoleCode)
            .filter(code -> !RoleCodeEnum.getDeployedRoleCodeEnum().contains(code))
            .collect(Collectors.toSet());
        // 定义结果集合
        Set<String> roleCodeResult = new HashSet<>();
        for (String roleCode : roleCodes) {
            // 包含顶级管理员自身
            if (includeOneself && RoleCodeEnum.getDeployedRoleCodeEnum().contains(roleCode)) {
                roleCodeResult.add(roleCode);
            }

            if (roleCode.equals(RoleCodeEnum.SECURITY_ADMIN.getRoleCode())) {
                // 安全管理员可以除顶级管理员外的所有角色
                roleCodeResult.addAll(roleCodesExcludeSupervisorRoles);
                continue;
            }

            // 子角色集合
            List<RoleEntity> roleByParentRoleCode = getRoleByParentRoleCode(roleCode);
            if (CollectionUtils.isNotEmpty(roleByParentRoleCode)) {
                roleCodeResult.addAll(roleByParentRoleCode.stream()
                    .map(RoleEntity::getRoleCode)
                    .collect(Collectors.toSet()));
            }

        }
        return roleCodeResult;
    }
}
