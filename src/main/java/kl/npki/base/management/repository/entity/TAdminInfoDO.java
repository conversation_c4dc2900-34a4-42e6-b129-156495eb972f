package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-08-26 14:26:11
 * 管理员用户表
 */

@TableName("t_admin_info")
@KlDbTable(tbName = "t_admin_info", indexes = {@Index(name = "idx_tai_role_code", columnList = {"role_code"}),
    @Index(name = "idx_tai_username", columnList = "username")})
public class TAdminInfoDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = -7751563846187582565L;

    /**
     * 人员标识
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true, remarks = "人员标识")
    private String username;

    /**
     * 机构ID
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "机构ID")
    private Long orgId;

    /**
     * 密码摘要
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, remarks = "密码摘要")
    private String passwordHash;

    /**
     * 盐值
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, remarks = "盐值")
    private String passwordSalt;

    /**
     * 角色ID
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, notNull = true, remarks = "角色ID")
    private String roleCode;

    /**
     * 用户状态
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "-1", remarks = "用户状态")
    private Integer status;

    /**
     * 省
     */
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "省")
    private String province;

    /**
     * 市
     */
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "市")
    private String city;

    /**
     * 组织
     */
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "组织")
    private String organization;

    /**
     * 机构
     */
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "机构")
    private String organizationUnit;

    /**
     * 电子邮箱
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, remarks = "电子邮箱")
    private String email;

    /**
     * 登录重试次数
     */
    @KlDbField(type = DataType.INTEGER, size = 10, notNull = true, defaultValue = "0", remarks = "登录重试次数")
    private Integer loginRetries;

    /**
     * 账号锁定时间
     */
    @KlDbField(type = DataType.DATE, remarks = "账号锁定时间", size = 6)
    private LocalDateTime lockTime;

    /**
     * 管理员组，分为初始化管理员，备份管理员
     */
    @KlDbField(type = DataType.VARCHAR, size = 128, remarks = "管理员组，分为初始化管理员，备份管理员")
    private String adminGroup;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "扩展项1")
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "扩展项2")
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "扩展项3")
    private String ext3;

    public String getUsername() {
        return username;
    }

    public TAdminInfoDO setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public TAdminInfoDO setRoleCode(String roleCode) {
        this.roleCode = roleCode;
        return this;
    }

    public Long getOrgId() {
        return orgId;
    }

    public TAdminInfoDO setOrgId(Long orgId) {
        this.orgId = orgId;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public TAdminInfoDO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getOrganizationUnit() {
        return organizationUnit;
    }

    public void setOrganizationUnit(String organizationUnit) {
        this.organizationUnit = organizationUnit;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getLoginRetries() {
        return loginRetries;
    }

    public void setLoginRetries(Integer loginRetries) {
        this.loginRetries = loginRetries;
    }

    public LocalDateTime getLockTime() {
        return lockTime;
    }

    public void setLockTime(LocalDateTime lockTime) {
        this.lockTime = lockTime;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getPasswordSalt() {
        return passwordSalt;
    }

    public void setPasswordSalt(String passwordSalt) {
        this.passwordSalt = passwordSalt;
    }

    public String getAdminGroup() {
        return adminGroup;
    }

    public void setAdminGroup(String adminGroup) {
        this.adminGroup = adminGroup;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    @Override
    public String toString() {
        return "TAdminInfoDO{" +
            "username='" + username + '\'' +
            ", orgId=" + orgId +
            ", passwordHash='" + passwordHash + '\'' +
            ", passwordSalt='" + passwordSalt + '\'' +
            ", roleCode='" + roleCode + '\'' +
            ", status=" + status +
            ", province='" + province + '\'' +
            ", city='" + city + '\'' +
            ", organization='" + organization + '\'' +
            ", organizationUnit='" + organizationUnit + '\'' +
            ", email='" + email + '\'' +
            ", loginRetries=" + loginRetries +
            ", lockTime=" + lockTime +
            ", adminGroup='" + adminGroup + '\'' +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
