package kl.npki.base.management.repository.mapper;

import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.base.management.repository.entity.TResourceDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @desc
 */
public interface TResourceMapper extends KlBaseMapper<TResourceDO> {


    /**
     * 更新角色资源状态
     *
     * @param resourceList 资源集合
     * @param status       状态
     */
    void updateResourceStatus(@Param("resourceList") List<String> resourceList, @Param("status") Integer status);


}
