package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.core.annotation.DataProtect;
import kl.npki.base.management.repository.impl.RoleRepositoryImpl;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 * @date 2023/5/26
 */
@TableName("t_role")
@KlDbTable(tbName = "t_role")
@DataProtect(genOriDataClass = RoleRepositoryImpl.class)
public class TRoleDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = -2117012753635386228L;

    /**
     * 角色名
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "角色名")
    private String roleName;

    /**
     * 角色名国际化key
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "角色名国际化key")
    private String roleNameI18nKey;

    /**
     * 角色编码
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "角色编码")
    private String roleCode;

    /**
     * 角色编码
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "角色编码")
    private String parentRoleCode;

    /**
     * 角色备注
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 512, remarks = "角色备注")
    private String remark;

    /**
     * 创建者id
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "创建者id")
    private Long createBy;

    /**
     * 更新者id
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "更新者id")
    private Long updateBy;

    /**
     * 自定义类型0:系统角色,1:自定义角色
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, defaultValue = "1", remarks = "自定义类型0:系统角色,1:自定义角色")
    private Integer isCustom;

    public String getRoleName() {
        if (StringUtils.isBlank(roleNameI18nKey)) {
            return roleName;
        }
        String i18NValue = I18nUtil.tr(roleNameI18nKey);
        if(StringUtils.isNotBlank(i18NValue) && !i18NValue.equalsIgnoreCase(roleNameI18nKey) && i18NValue.length()<64){
            return i18NValue;
        }else{
            return roleName;
        }
    }

    public TRoleDO setRoleName(String roleName) {
        this.roleName = roleName;
        return this;
    }

    public String getRoleNameI18nKey() {
        return roleNameI18nKey;
    }

    public void setRoleNameI18nKey(String roleNameI18nKey) {
        this.roleNameI18nKey = roleNameI18nKey;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public TRoleDO setRoleCode(String roleCode) {
        this.roleCode = roleCode;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public TRoleDO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public TRoleDO setCreateBy(Long createBy) {
        this.createBy = createBy;
        return this;
    }

    public String getParentRoleCode() {
        return parentRoleCode;
    }

    public TRoleDO setParentRoleCode(String parentRoleCode) {
        this.parentRoleCode = parentRoleCode;
        return this;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public TRoleDO setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public Integer getIsCustom() {
        return isCustom;
    }

    public TRoleDO setIsCustom(Integer isCustom) {
        this.isCustom = isCustom;
        return this;
    }

    @Override
    public String toString() {
        return "TRoleDO{" +
            "roleName='" + roleName + '\'' +
            ", roleNameI18nKey='" + roleNameI18nKey + '\'' +
            ", roleCode='" + roleCode + '\'' +
            ", parentRoleCode='" + parentRoleCode + '\'' +
            ", remark='" + remark + '\'' +
            ", createBy=" + createBy +
            ", updateBy=" + updateBy +
            ", isCustom=" + isCustom +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}

