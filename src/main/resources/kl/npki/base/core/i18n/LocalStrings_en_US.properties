# BaseDbError
kl.npki.base.core.i18n_db_query_time_out=Database query time out!

# BaseInternalError
kl.npki.base.core.i18n_public_key_convert_error=Certificate public key conversion failed!
kl.npki.base.core.i18n_asym_algo_convert_error=Public key asymmetric algorithm conversion failed!
kl.npki.base.core.i18n_signing_cert_data_error=Failed to generate certificate signature!
kl.npki.base.core.i18n_cert_issue_error=Failed to issue certificate!
kl.npki.base.core.i18n_root_cert_issuer_error=Failed to issue management root!
kl.npki.base.core.i18n_cert_req_gen_error=Certificate request generation failed!
kl.npki.base.core.i18n_root_cert_already_exist=Root certificate already exists!
kl.npki.base.core.i18n_gen_pkcs10_request_error=Failed to generate P10 request!
kl.npki.base.core.i18n_dn_build_error=DN construction failed!
kl.npki.base.core.i18n_id_cert_already_exist=Identity certificate already exists!
kl.npki.base.core.i18n_cert_encode_error=Certificate ASN structure encoding failed!
kl.npki.base.core.i18n_cert_decode_error=Certificate ASN structure decoding failed!
kl.npki.base.core.i18n_cert_parse_error==Failed to parse certificate!
kl.npki.base.core.i18n_certificate_parse_error_tips=The certificate format is incorrect. Please make sure that you have entered a valid Base64 encoded certificate.
kl.npki.base.core.i18n_root_not_exist=Management root certificate does not exist!
kl.npki.base.core.i18n_pkcs10_request_encode_error=P10 request encoding failed!
kl.npki.base.core.i18n_cert_already_exist=Certificate already exists!
kl.npki.base.core.i18n_cert_pub_key_not_matched=Public key information mismatch!
kl.npki.base.core.i18n_cert_req_not_gen=Certificate request not generated!
kl.npki.base.core.i18n_cert_publickey_ecode_error=Certificate public key encode failed!
kl.npki.base.core.i18n_keystore_already_exists=Keystore file already exists!
kl.npki.base.core.i18n_generate_keystore_error=Failed to generate Keystore file!
kl.npki.base.core.i18n_not_found_cert_from_keystore=Target certificate not found from keystore file!
kl.npki.base.core.i18n_found_cert_from_keystore_failed=Exception occurred while finding certificate from keystore file!
kl.npki.base.core.i18n_export_ssl_site_cert_error=Failed to export SSL site certificate!
kl.npki.base.core.i18n_root_cert_init_get_lock_error=Failed to acquire lock during root certificate initialization!
kl.npki.base.core.i18n_trust_cert_not_exist_error=Failed to delete certificate, trust certificate does not exist!
kl.npki.base.core.i18n_ssl_cert_init_get_lock_error=Failed to acquire lock during SSL certificate initialization!
kl.npki.base.core.i18n_id_cert_init_get_lock_error=Failed to acquire lock during identity certificate initialization!
kl.npki.base.core.i18n_local_trust_cert_deleter_error=Management root certificate cannot be deleted!
kl.npki.base.core.i18n_cert_request_parsing_error=Certificate request parsing failed!
kl.npki.base.core.i18n_mainkey_import_error=Failed to import main key!
kl.npki.base.core.i18n_mainkey_export_error=Failed to export main key!
kl.npki.base.core.i18n_mainkey_not_found_error=Main key with corresponding ID not found!
kl.npki.base.core.i18n_mainkey_config_null_error=Main key ID configuration is empty!
kl.npki.base.core.i18n_mainkey_init_error=Failed to initialize main key cache!
kl.npki.base.core.i18n_mainkey_init_get_lock_error=Failed to acquire lock during main key initialization!
kl.npki.base.core.i18n_key_pair_get_error=Failed to get key pair!
kl.npki.base.core.i18n_sign_alg_get_error=Failed to get signature algorithm!
kl.npki.base.core.i18n_keytype_not_support_error=Engine service does not support this key type!
kl.npki.base.core.i18n_engine_service_init_error=Engine service initialization failed!
kl.npki.base.core.i18n_engine_service_not_find_error=Failed to load engine service!
kl.npki.base.core.i18n_algorithm_not_support_error=Engine service does not support this algorithm!
kl.npki.base.core.i18n_asymmetric_encrypt_error=Asymmetric encryption failed!
kl.npki.base.core.i18n_asymmetric_decrypt_error=Asymmetric decryption failed!
kl.npki.base.core.i18n_symmetric_encrypt_error=Symmetric encryption failed!
kl.npki.base.core.i18n_symmetric_decrypt_error=Symmetric decryption failed!
kl.npki.base.core.i18n_engine_init_error=Failed to initialize encryption engine!
kl.npki.base.core.i18n_data_padding_error=Failed to pad data!
kl.npki.base.core.i18n_data_unpadded_error=Failed to unpad data!
kl.npki.base.core.i18n_engine_cluster_already_exist=Cluster name already exists!
kl.npki.base.core.i18n_engine_cluster_not_found=Encryption engine cluster with this name does not exist!
kl.npki.base.core.i18n_signed_verify_error=Signature verification failed!
kl.npki.base.core.i18n_sign_error=Failed to sign!
kl.npki.base.core.i18n_engine_lb_config_error=Encryption device load balancing policy configuration error!
kl.npki.base.core.i18n_asymmetric_config_error=The symmetric algorithm has been configured and cannot be changed. If adjustments are needed, please redeploy the system!
kl.npki.base.core.i18n_license_verify_error=License verification failed!
kl.npki.base.core.i18n_license_import_error=Failed to import license!
kl.npki.base.core.i18n_license_is_null=License is empty!
kl.npki.base.core.i18n_date_parse_error=Failed to parse date!
kl.npki.base.core.i18n_long_parse_error=Failed to parse Long!
kl.npki.base.core.i18n_login_type_conversion_error=Login type conversion error!
kl.npki.base.core.i18n_get_public_key_error=Failed to get public key!
kl.npki.base.core.i18n_get_private_key_error=Failed to get private key!
kl.npki.base.core.i18n_build_sm2_cipher_blob_error=Failed to build SM2 cipher blob!
kl.npki.base.core.i18n_file_create_fail=Failed to create file!
kl.npki.base.core.i18n_gen_full_data_ori_data_error=Failed to generate full data original value!
kl.npki.base.core.i18n_class_init_method_empty=Default class initialization method is missing!
kl.npki.base.core.i18n_lambda_column_name_error=Failed to parse column name by lambda!
kl.npki.base.core.i18n_system_language_error=System language configuration error!
kl.npki.base.core.i18n_unsupported_language_error=Unsupported languages!
kl.npki.base.core.i18n_algorithm_identifier_encode_error= Encoding algorithm identifier failed!
kl.npki.base.core.i18n_trust_cert_not_found=Trust certificate not found
kl.npki.base.core.i18n_cert_p7b_error=Trust certificate not found
kl.npki.base.core.i18n_file_write_fail=File write failed
kl.npki.base.core.i18n_socket_connection_failed=Socket connection failed!
kl.npki.base.core.i18n_socket_stop_failed=Socket stop failed!
kl.npki.base.core.i18n_audit_ip_reach_failed=Audit server IP unavailable!
kl.npki.base.core.i18n_audit_connection_failed=Audit service connection failed!
kl.npki.base.core.i18n_audit_switch_unknown_protocol_error=Audit switch unknown protocol error
kl.npki.base.core.i18n_audit_switch_appender_not_found_error=Audit switch syslogAppender not exist error
kl.npki.base.core.i18n_org_batch_import_excel_gen_error=Failure to generate the result of institution import
kl.npki.base.core.i18n_org_batch_import_file_operate_error=Error in the operation of the result file during bulk import of institutions
kl.npki.base.core.i18n_org_delete_binding_error=Failed to delete the binding between the institution and the certificate!
kl.npki.base.core.i18n_org_not_exist_error=Org does not exist!
# BaseRemoteError
kl.npki.base.core.i18n_upload_log_to_kcsp_failed=Failed to upload logs to KCSP!
kl.npki.base.core.i18n_crl_parse_error=CRL parsing failed.
kl.npki.base.core.i18n_microsoft_cert_template_parse_error=Microsoft certificate template parsing failed!

# BaseValidationError
kl.npki.base.core.i18n_cert_not_yet_valid_error=Certificate is not yet valid!
kl.npki.base.core.i18n_cert_expired_error=Certificate has expired!
kl.npki.base.core.i18n_license_is_empty=License is empty!
kl.npki.base.core.i18n_license_content_error=License content error!
kl.npki.base.core.i18n_validity_gt_issue_cert=Certificate validity exceeds issuer certificate!
kl.npki.base.core.i18n_issuer_authority_key_id_is_null=Issuer authority key ID is null!
kl.npki.base.core.i18n_subject_key_id_is_null=subject key ID is null!
kl.npki.base.core.i18n_mgr_root_key_algorithm_mismatch=Requested key algorithm does not match management root key algorithm!
kl.npki.base.core.i18n_not_trust_cert_error=Certificate is not trusted!
kl.npki.base.core.i18n_trust_cert_is_empty=Trusted certificate is empty!
kl.npki.base.core.i18n_export_cert_error=Failed to export certificate!
kl.npki.base.core.i18n_pq_enc_key_type_is_null=Post-quantum encryption certificate key type is null!
kl.npki.base.core.i18n_pq_enc_key_type_purpose_error=Post-quantum encryption certificate key usage type must be [encryption]!
kl.npki.base.core.i18n_sans_parsing_error_from_ext=Failed to read SAN list from certificate extension!
kl.npki.base.core.i18n_cert_parse_failed=Failed to parse certificate!
kl.npki.base.core.i18n_algorithm_not_supported=Unsupported algorithm type!
kl.npki.base.core.i18n_incorrect_recipient_info_number=At least one RecipientInfo is required!
kl.npki.base.core.i18n_encrypted_content_info_is_null=EncryptedContentInfo is null!
kl.npki.base.core.i18n_incorrect_signer_info_number=At least one SignerInfo is required!
kl.npki.base.core.i18n_self_check_not_supported=Unsupported self-check type!
kl.npki.base.core.i18n_user_status_error=User status error!
kl.npki.base.core.i18n_user_cert_status_error=User certificate status error!
kl.npki.base.core.i18n_file_config_entity_params_is_invalid=File configuration entity parameters are invalid!
kl.npki.base.core.i18n_param_error=Parameter error!
kl.npki.base.core.i18n_unsupported_charset=Unsupported charset
kl.npki.base.core.i18n_integrity_verification_failed=Data integrity verification failed, possible tampering!
kl.npki.base.core.i18n_general_verification_error=Error occurred during data integrity verification!
kl.npki.base.core.i18n_upload_file_format_error=The format of the uploaded file does not match the interface.
kl.npki.base.core.i18n_upload_file_header_is_incorrect=The batch upload file header is incorrect. Please download the template again.                                                                                               
kl.npki.base.core.i18n_upload_file_check_data_empty_error=The header of the inspection template for the uploaded file is empty
kl.npki.base.core.i18n_upload_file_search_status_error=The contents of the file are being processed, and the processing result cannot be queried
kl.npki.base.core.i18n_upload_file_not_found_fail=The upload file does not exist
kl.npki.base.core.i18n_org_not_found=The organizational structure does not exist.
kl.npki.base.core.i18n_org_parent_not_found=The parent institution does not exist
kl.npki.base.core.i18n_org_add_eq_error=The child organization code cannot be the same as the parent organization code
kl.npki.base.core.i18n_org_batch_delete_not_selected_all_sub_org_error=When you delete an organization, you cannot delete the organization if all suborganizations are unchecked
kl.npki.base.core.i18n_org_delete_error=Organization deletion failed
kl.npki.base.core.i18n_org_name_null_error=The organization name cannot be empty
kl.npki.base.core.i18n_org_code_null_error=The organization code cannot be empty
kl.npki.base.core.i18n_org_full_code_null_error=The full mechanism code cannot be empty
kl.npki.base.core.i18n_org_status_error=The status of the institution is incorrect
kl.npki.base.core.i18n_org_code_repeat_error=Duplicate agency codes
kl.npki.base.core.i18n_import_cert_chain_error=Import Certificate chain error
kl.npki.base.core.i18n_not_ca_cert=The certificate is not a CA certificate
kl.npki.base.core.i18n_cert_request_sign_error=The certificate request signature verification failed
kl.npki.base.core.i18n_pri_key_parse_failed=Private key parsing failed
kl.npki.base.core.i18n_enum_validator_definition_error=Enum validator definition error
kl.npki.base.core.i18n_login_type_null_error=Login authentication type is empty
kl.npki.base.core.i18n_login_type_not_support_error=Unsupported login authentication type
kl.npki.base.core.i18n_config_encryption_key_file_not_found_error=Cryptographic environment initialization failed
kl.npki.base.core.i18n_config_encryption_key_file_load_error=Cryptographic environment initialization failed

# Enum
kl.npki.base.core.i18n_table_sharding=Table Sharding Creation Scheduled Task
kl.npki.base.core.i18n_self_check=Service Self-Check Scheduled Task
kl.npki.base.core.i18n_home_page_data_update=Home Page Data Cache Update Scheduled Task
kl.npki.base.core.i18n_system_inspection=System Inspection Task
kl.npki.base.core.i18n_self_issue=Self-Issued
kl.npki.base.core.i18n_external_import=Externally Imported
kl.npki.base.core.i18n_recovered=Certificate Recovered
kl.npki.base.core.i18n_key_updated=Key Updated
kl.npki.base.core.i18n_extend=Certificate Extended
kl.npki.base.core.i18n_updated=Certificate Updated
kl.npki.base.core.i18n_revoked=Certificate Revoked
kl.npki.base.core.i18n_to_be_issued=Pending Issuance
kl.npki.base.core.i18n_issued=Issued
kl.npki.base.core.i18n_update_to_be_checked=Update Pending Review
kl.npki.base.core.i18n_to_be_updated=Pending Update
kl.npki.base.core.i18n_extend_to_be_checked=Extension Pending Review
kl.npki.base.core.i18n_to_be_extended=Pending Extension
kl.npki.base.core.i18n_frozen=Certificate Frozen
kl.npki.base.core.i18n_reissue_to_be_checked=Reissue Pending Review
kl.npki.base.core.i18n_to_be_recovered=Pending Recovery
kl.npki.base.core.i18n_to_be_reissued=Pending Reissue
kl.npki.base.core.i18n_freeze_to_be_checked=Freeze Pending Review
kl.npki.base.core.i18n_to_be_frozen=Pending Freeze
kl.npki.base.core.i18n_unfreeze_to_be_checked=Unfreeze Pending Review
kl.npki.base.core.i18n_to_be_unfrozen=Pending Unfreeze
kl.npki.base.core.i18n_key_update_to_be_checked=Key Update Pending Review
kl.npki.base.core.i18n_key_to_be_update=Pending Key Update
kl.npki.base.core.i18n_recovery_to_be_checked=Recovery Pending Review
kl.npki.base.core.i18n_revoke_to_be_checked=Revocation Pending Review
kl.npki.base.core.i18n_revoke_at_frozent_status_to_be_checked=Frozen Revocation Pending Review
kl.npki.base.core.i18n_to_be_revoked=Pending Revocation
kl.npki.base.core.i18n_to_be_revoked_at_frozent_status=Pending Revocation While Frozen
kl.npki.base.core.i18n_self_sign_cert=Self-Signed Certificate
kl.npki.base.core.i18n_user_sign_cert=User Signature Certificate
kl.npki.base.core.i18n_user_enc_cert=User Encryption Certificate
kl.npki.base.core.i18n_ssl_sign_server_cert=SSL Signature Server Certificate
kl.npki.base.core.i18n_ssl_enc_server_cert=SSL Encryption Server Certificate
kl.npki.base.core.i18n_ssl_sign_client_cert=SSL Signature Client Certificate
kl.npki.base.core.i18n_ssl_enc_client_cert=SSL Encryption Client Certificate
kl.npki.base.core.i18n_file_engine=File Cryptographic Engine
kl.npki.base.core.i18n_standard_engine=Standard Cryptographic Engine
kl.npki.base.core.i18n_emulator_engine=High-Performance Cryptographic Engine
kl.npki.base.core.i18n_koal_engine=KOAL Cryptographic Engine
kl.npki.base.core.i18n_china_core_ccp_907_t=China Core CCP-907-T Cryptographic Card
kl.npki.base.core.i18n_mucse_rsp_20=Mucse RSP-20 Cryptographic Card
kl.npki.base.core.i18n_mucse_rsp_20_cap=Mucse RSP-20-CAP Cryptographic Card
kl.npki.base.core.i18n_sansec=Sansec SJJ1012A Cryptographic Engine
kl.npki.base.core.i18n_sansec_fips=Sansec FIPS Cryptographic Engine
kl.npki.base.core.i18n_hongsi=Hongsi Cryptographic Card
kl.npki.base.core.i18n_hygon=Hygon Cryptographic Card
kl.npki.base.core.i18n_demo=Test Management Environment
kl.npki.base.core.i18n_default=Default Management Environment
kl.npki.base.core.i18n_pwd=Username and Password Login
kl.npki.base.core.i18n_sign=Certificate Signature Login
kl.npki.base.core.i18n_sign_pwd=Certificate Signature and Password Login
kl.npki.base.core.i18n_sso=SSO Login
kl.npki.base.core.i18n_multi_sign_2_of_3=2 out of 3 Certificate Signature Login
kl.npki.base.core.i18n_multi_sign_3_of_5=3 out of 5 Certificate Signature Login
kl.npki.base.core.i18n_api_log=Service Log
kl.npki.base.core.i18n_op_log=Operation Log
kl.npki.base.core.i18n_super=Superior Certificate
kl.npki.base.core.i18n_local=Local Certificate
kl.npki.base.core.i18n_manage=Management Certificate
kl.npki.base.core.i18n_ssl=SSL Certificate
kl.npki.base.core.i18n_identity=Identity Certificate
kl.npki.base.core.i18n_cancelled=Cancelled
kl.npki.base.core.i18n_register_to_be_checked=User Registration Pending Review
kl.npki.base.core.i18n_register_check_failed=Registration Review Failed
kl.npki.base.core.i18n_normal=Normal
kl.npki.base.core.i18n_cancel_to_be_checked=User Cancellation Pending Review
kl.npki.base.core.i18n_to_be_cancled=User Pending Cancellation
kl.npki.base.core.i18n_locked=Locked
kl.npki.base.core.i18n_sct_version=Version
kl.npki.base.core.i18n_sct_log_id=Log Server ID
kl.npki.base.core.i18n_sct_timestamp=Log Time
kl.npki.base.core.i18n_sct_extensions=Extensions
kl.npki.base.core.i18n_sct_hash_algorithm=Hash Algorithm
kl.npki.base.core.i18n_sct_signature_algorithm=Signature Algorithm
kl.npki.base.core.i18n_sct_signature=Signature
kl.npki.base.core.i18n_ca_issuers=CA Issuers
kl.npki.base.core.i18n_ocsp=OCSP Responder
kl.npki.base.core.i18n_is_ca=Is a Certificate Authority
kl.npki.base.core.i18n_is_not_ca=Is not a Certificate Authority
kl.npki.base.core.i18n_path_len_constraint=Maximum number of intermediate CAs
kl.npki.base.core.i18n_cabf_enhanced_validation_ssl_certificate_policy=Extended Validation SSL Certificate
kl.npki.base.core.i18n_cabf_ssl_certificate_minimum_required_policy=Baseline Requirements SSL Certificate
kl.npki.base.core.i18n_cabf_domain_name_validation_ssl_certificate_policy=Domain Validation SSL Certificate
kl.npki.base.core.i18n_cabf_organization_validation_ssl_certificate_policy=Organization Validation SSL Certificate
kl.npki.base.core.i18n_cabf_personal_validation_ssl_certificate_policy=Individual Validation SSL Certificate
kl.npki.base.core.i18n_cabf_enhanced_verification_code_signing_certificate_policy=Extended Validation Code Signing Certificate
kl.npki.base.core.i18n_cabf_code_signing_certificate_minimum_required_policy=Baseline Requirements Code Signing Certificate
kl.npki.base.core.i18n_cabf_code_signing_certificate_minimum_required_timestamp_policy=Baseline Requirements Timestamp Certificate
kl.npki.base.core.i18n_cabf_s_mime_certificate_minimum_required_policy=Baseline Requirements S/MIME Certificate
kl.npki.base.core.i18n_cabf_s_mime_email_legacy_certificate_policy=S/MIME Email Legacy Certificate
kl.npki.base.core.i18n_cabf_s_mime_email_multi_purpose_certificate_policy=S/MIME Email Multi Purpose Certificate
kl.npki.base.core.i18n_cabf_s_mime_email_strict_certificate_policy=S/MIME Email Strict Certificate
kl.npki.base.core.i18n_cabf_s_mime_organization_legacy_certificate_policy=S/MIME Organization Legacy Certificate
kl.npki.base.core.i18n_cabf_s_mime_organization_email_multi_purpose_certificate_policy=S/MIME Organization Multi Purpose Certificate
kl.npki.base.core.i18n_cabf_s_mime_organization_email_strict_certificate_policy=S/MIME Organization Strict Certificate
kl.npki.base.core.i18n_cabf_s_mime_sponsors_legacy_certificate_policy=S/MIME Sponsors Legacy Certificate
kl.npki.base.core.i18n_cabf_s_mime_sponsors_organization_email_multi_purpose_certificate_policy=S/MIME Sponsors Multi Purpose Certificate
kl.npki.base.core.i18n_cabf_s_mime_sponsors_organization_email_strict_certificate_policy=S/MIME Sponsors Strict Certificate
kl.npki.base.core.i18n_cabf_s_mime_personal_legacy_certificate_policy=S/MIME Individual Legacy Certificate
kl.npki.base.core.i18n_cabf_s_mime_personal_organization_email_multi_purpose_certificate_policy=S/MIME Individual Multi Purpose Certificate
kl.npki.base.core.i18n_cabf_s_mime_personal_organization_email_strict_certificate_policy=S/MIME Individual Strict Certificate
kl.npki.base.core.i18n_unspecified=Unspecified
kl.npki.base.core.i18n_key_compromise=Key Compromise
kl.npki.base.core.i18n_ca_compromise=CA Compromise
kl.npki.base.core.i18n_affiliation_changed=Affiliation Changed
kl.npki.base.core.i18n_superseded=Superseded
kl.npki.base.core.i18n_cessation_of_operation=Cessation Of Operation
kl.npki.base.core.i18n_certificate_hold=Certificate Hold
kl.npki.base.core.i18n_remove_from_crl=Remove From CRL
kl.npki.base.core.i18n_privilege_withdrawn=Privilege Withdrawn
kl.npki.base.core.i18n_aa_compromise=Attribute authority leakage
kl.npki.base.core.i18n_other_name=Other Name
kl.npki.base.core.i18n_rfc822_name=RFC822 Name
kl.npki.base.core.i18n_dns_name=DNS Name
kl.npki.base.core.i18n_x400_address=X400 Address
kl.npki.base.core.i18n_directory_name=Directory Name
kl.npki.base.core.i18n_edi_party_name=EDI Party Name
kl.npki.base.core.i18n_uniform_resource_identifier=URL
kl.npki.base.core.i18n_ip_address=IP Address
kl.npki.base.core.i18n_registered_id=Registered ID
kl.npki.base.core.i18n_digital_signature=Digital Signature
kl.npki.base.core.i18n_non_repudiation=Non-repudiation
kl.npki.base.core.i18n_key_encipherment=Key Encipherment
kl.npki.base.core.i18n_data_encipherment=Data Encipherment
kl.npki.base.core.i18n_key_agreement=Key Agreement
kl.npki.base.core.i18n_key_cert_sign=Key Certificate Sign
kl.npki.base.core.i18n_crl_sign=CRL Sign
kl.npki.base.core.i18n_encipher_only=Encipher only
kl.npki.base.core.i18n_decipher_only=Decipher only
kl.npki.base.core.i18n_ssl_client=SSL Client
kl.npki.base.core.i18n_ssl_server=SSL Server
kl.npki.base.core.i18n_smime=S/MIME
kl.npki.base.core.i18n_object_signing=Object Signing
kl.npki.base.core.i18n_reserved=Reserved
kl.npki.base.core.i18n_ssl_ca=SSL CA
kl.npki.base.core.i18n_smime_ca=S/MIME CA
kl.npki.base.core.i18n_object_signing_ca=Object Signing CA
kl.npki.base.core.i18n_der=DER encoded binary X.509
kl.npki.base.core.i18n_pem=Base64 encoding X.509
kl.npki.base.core.i18n_p7b=Cryptographic Message Syntax Standard-PKCS 7 Certificates
kl.npki.base.core.i18n_security_log_fileName=security_log
kl.npki.base.core.i18n_operation_log_fileName=operation_log
kl.npki.base.core.i18n_api_log_fileName=api_log

## 异常描述信息
kl.npki.base.core.i18n_unsupported_key_type_type_is=Unsupported key type, type is {0}
kl.npki.base.core.i18n_get_public_key_failed=Failed to get the public key of the encryption key pair
kl.npki.base.core.i18n_the_current_type_of_anti_quantum_encryption_key_used_is=The current type of anti-quantum encryption key used is {0}
kl.npki.base.core.i18n_trust_cert_public_key_convert_error=Trust certificate public key conversion failed
kl.npki.base.core.i18n_please_generate_cert_request=Please generate a certificate request first
kl.npki.base.core.i18n_the_validDays_is_less_than_1=Valid days is less than 1
kl.npki.base.core.i18n_the_cert_type_is_not_supported=Certificate type {0} is not supported
kl.npki.base.core.i18n_the_key_pair_is_null=Key pair is null
kl.npki.base.core.i18n_the_certificate_has_not_yet_taken_effect_with_an_effective_date_of=Certificate {0} has not yet taken effect, effective date is {1}
kl.npki.base.core.i18n_the_certificate_has_expired_with_an_expiration_date_of={0} certificate has expired,the expiration date is: {1}
kl.npki.base.core.i18n_certificate_validity_exceeds_issuer_validity=Certificate validity [{0}] exceeds issuer validity [{1}]
kl.npki.base.core.i18n_id_cert_public_key_convert_error=Identity certificate public key conversion failed
kl.npki.base.core.i18n_the_id_cert_issue_error=Failed to issue identity certificate
kl.npki.base.core.i18n_the_ssl_cert_issue_error=Failed to issue SSL communication certificate
kl.npki.base.core.i18n_the_trusted_certificate_list_is_empty=The trusted certificate chain is empty. Please issue or import the certificate chain first.
kl.npki.base.core.i18n_the_certificate_to_be_verified_cannot_be_empty=The certificate to be verified cannot be empty
kl.npki.base.core.i18n_trust_cert_id=Trust certificate ID {0}
kl.npki.base.core.i18n_the_root_cert_already_exist=Root certificate already exists
kl.npki.base.core.i18n_please_import_root_cert=Please import the root certificate first
kl.npki.base.core.i18n_issue_manage_root_cert_error=Failed to issue management root certificate
kl.npki.base.core.i18n_the_root_cert_not_exist=Root certificate does not exist
kl.npki.base.core.i18n_hexadecimal_certificate_serial_number=Hexadecimal certificate serial number {0}
kl.npki.base.core.i18n_cert_not_found=Certificate not found
kl.npki.base.core.i18n_cert_is_empty=Certificate is empty
kl.npki.base.core.i18n_the_check_item_is_not_registered=Check item {0} is not registered
kl.npki.base.core.i18n_file_config_entity_is_null=File configuration entity is null
kl.npki.base.core.i18n_file_name_is_empty=File name is empty
kl.npki.base.core.i18n_file_Path_is_empty=File path is empty
kl.npki.base.core.i18n_file_Content_Base64_is_empty=File content Base64 is empty
kl.npki.base.core.i18n_an_error_occurred_while_uploading_the_administrator_operation_log=Failed to upload administrator operation log
kl.npki.base.core.i18n_get_license_status_failed=Failed to get license status
kl.npki.base.core.i18n_license_content_is_invalid=License content is invalid
kl.npki.base.core.i18n_license_content_is_invalid_with_status=License content is invalid, status is {0}
kl.npki.base.core.i18n_contains_illegal_characters=Contains illegal characters
kl.npki.base.core.i18n_the_master_key_id_is=Master key ID is {0}
kl.npki.base.core.i18n_signature_verification_failed=Signature verification failed
kl.npki.base.core.i18n_signature_is_null=Signature is null
kl.npki.base.core.i18n_undefined_load_type=Undefined load type [{0}]
kl.npki.base.core.i18n_weight_configuration_is_null=Weight configuration is null
kl.npki.base.core.i18n_the_device_is_missing_weight_values=Device [{0}] is missing weight values
kl.npki.base.core.i18n_the_device_weight_value_is_less_than_0=Device [{0}] weight value is less than 0
kl.npki.base.core.i18n_the_total_weight_of_the_device_is_less_than_or_equal_to0=[{0}] Total device weight is less than or equal to 0
kl.npki.base.core.i18n_master_key_algorithm_conversion_failed=Master key algorithm conversion failed
kl.npki.base.core.i18n_login_type_is_null=Login type is null
kl.npki.base.core.i18n_system_language_cannot_be_modified_after_deployment=System language cannot be modified after deployment
kl.npki.base.core.i18n_the_original_text_is_empty={0} Original text is empty
kl.npki.base.core.i18n_hash_value_is_empty={0} Hash value is empty
kl.npki.base.core.i18n_certificate_data_format_abnormality=Certificate data format is abnormal
kl.npki.base.core.i18n_certificate_encoding_exception=Certificate encoding exception
kl.npki.base.core.i18n_encoding_or_decoding_failed_while_extracting_sans=Encoding or decoding failed while extracting SANs
kl.npki.base.core.i18n_trust_keystore_file_already_exists=Trust keystore file already exists [{0}]
kl.npki.base.core.i18n_error_generating_trust_keystore=Failed to generate trust keystore file
kl.npki.base.core.i18n_pfx_password_is_empty=PFX password is empty
kl.npki.base.core.i18n_failed_to_obtain_anti_quantum_protection_public_key_from_extension_extension_is_empty=Failed to obtain anti-quantum protection public key from extension, extension is empty
kl.npki.base.core.i18n_failed_to_obtain_anti_quantum_protection_public_key_from_extension_there_is_no_corresponding_extension=Failed to obtain anti-quantum protection public key from extension, there is no corresponding extension
kl.npki.base.core.i18n_failed_to_obtain_private_key=Failed to obtain private key
kl.npki.base.core.i18n_the_get_full_data_hash_method_of_the_data_object_must_be_public=The getFullDataHash method of the data object must be public
kl.npki.base.core.i18n_the_getFullDataHash_method_of_a_data_object_must_return_a_string_type=The getFullDataHash method of a data object must return a String type
kl.npki.base.core.i18n_the_data_object_must_have_a_parameter_free_method_called_getFullDataHash=The data object must have a parameter-free method called getFullDataHash
kl.npki.base.core.i18n_the_getFullDataHash_method_that_cannot_access_the_data_object=Cannot access the getFullDataHash method of the data object
kl.npki.base.core.i18n_an_error_occurred_while_calling_the_getFullDataHash_method_of_the_data_object=An error occurred while calling the getFullDataHash method of the data object
kl.npki.base.core.i18n_failed_to_obtain_field_name=Failed to obtain field name
kl.npki.base.core.i18n_could_not_create_keystore_directory=Could not create Keystore directory {0}
kl.npki.base.core.i18n_the_key_data_directory_does_not_exist=The key data directory for {0}[{1}] was not detected. Please use the encryption-fusion tool to generate a built-in key for it. When generating, specify the result path as: {2}. If already generated, copy the generated directory to: "{2}"
kl.npki.base.core.i18n_the_current_status_is_not_pending_approval=The current status is not pending approval
kl.npki.base.core.i18n_account_locking_failed_unknown_user_status_code=Account locking failed, unknown user status code [{0}]
kl.npki.base.core.i18n_the_current_state_is_not_locked=The current state is not locked
kl.npki.base.core.i18n_unlocking_account_failed_unknown_user_status_code=Unlocking account failed, unknown user status code [{0}]
kl.npki.base.core.i18n_cn_item_is_empty_unable_to_construct_certificate_dn=CN item is empty, unable to construct certificate DN
kl.npki.base.core.i18n_date_format=MMMM dd, yyyy hh:mm:ss a
kl.npki.base.core.i18n_database_not_configured=Database not configured
kl.npki.base.core.i18n_common_unknown=Unknown
kl.npki.base.core.i18n_illegal_enum_type=The value of field {0} is invalid. It must be one of {1}

# 常量
kl.npki.base.core.i18n_fingerprint=Fingerprint
kl.npki.base.core.i18n_signature_information=Signature
kl.npki.base.core.i18n_public_key=Public Key
kl.npki.base.core.i18n_expiration_time=Expiration Time
kl.npki.base.core.i18n_effective_time=Effective Time
kl.npki.base.core.i18n_signature_algorithm=Signature Algorithm
kl.npki.base.core.i18n_issuer=Issuer
kl.npki.base.core.i18n_subject=Subject
kl.npki.base.core.i18n_serial_number=Serial Number
kl.npki.base.core.i18n_version_number=Version
kl.npki.base.core.i18n_hexadecimal=Hexadecimal SN
kl.npki.base.core.i18n_decimal=Decimal SN
kl.npki.base.core.i18n_authorization_center_information_access_permissions=Authority Information Access
kl.npki.base.core.i18n_unrestricted=unlimited
kl.npki.base.core.i18n_unknown_certificate_policy=Unknown Certificate Policy
kl.npki.base.core.i18n_currently_only_v1_version_parsing_is_supported=Signed Certificate Timestamp List extension parse failed, currently only support V1
kl.npki.base.core.i18n_unknown_version=Unknown Version
kl.npki.base.core.i18n_crl_distribution_point_url=Distribution Point URL
kl.npki.base.core.i18n_crl_distribution_point=[{0}]CRL Distribution Point
kl.npki.base.core.i18n_crl_publisher=CRL Issuer
kl.npki.base.core.i18n_crl_revocation_reason=Revocation reason
kl.npki.base.core.i18n_crl_serial_number=CRL Number
kl.npki.base.core.i18n_crl_param_decimal=(decimal)
kl.npki.base.core.i18n_crl_param_hexadecimal=(hexadecimal)
kl.npki.base.core.i18n_incremental_crl=Delta CRL
kl.npki.base.core.i18n_strategic_constraints=Policy Constraints
kl.npki.base.core.i18n_skip_policy_check_certificate_quantity=Skip policy check certificate quantity
kl.npki.base.core.i18n_number_of_certificates_that_can_be_mapped_using_policies=Number of certificates that can be mapped using policies
kl.npki.base.core.i18n_strategy_mapping=Policy Mappings
kl.npki.base.core.i18n_issuer_domain_policy=Issuer domain policy
kl.npki.base.core.i18n_subject_domain_strategy=Subject domain strategy
kl.npki.base.core.i18n_name=Name
kl.npki.base.core.i18n_subject_name=Subject Name
kl.npki.base.core.i18n_entity_name=Entity Name
kl.npki.base.core.i18n_name_allocator=Name Allocator
kl.npki.base.core.i18n_server_authentication=Server Authentication
kl.npki.base.core.i18n_client_authentication=Client Authentication
kl.npki.base.core.i18n_code_signing=Code Signing
kl.npki.base.core.i18n_email_protection=Email Protection
kl.npki.base.core.i18n_timestamp_generation=Time Stamping
kl.npki.base.core.i18n_ocsp_response_signing=OCSP Signing
kl.npki.base.core.i18n_kdc_authentication=KDC Client Authentication
kl.npki.base.core.i18n_smart_card_login=MS Smart Card Login
kl.npki.base.core.i18n_file_system_encryption=MS Encrypted File System(EFS)
kl.npki.base.core.i18n_key_usage=Key Usage
kl.npki.base.core.i18n_extended_key_usage=Extended Key Usage
kl.npki.base.core.i18n_basic_constraints=Basic Constraints
kl.npki.base.core.i18n_subject_key_identifier=Subject Key Identifier
kl.npki.base.core.i18n_authority_key_identifier=Authority Key Identifier
kl.npki.base.core.i18n_subject_alternative_name=Subject Alternative Name
kl.npki.base.core.i18n_issuer_alternative_name=Issuer Alternative Name
kl.npki.base.core.i18n_crl_distribution_points=CRL Distribution Points
kl.npki.base.core.i18n_authority_information_access=Authority Information Access
kl.npki.base.core.i18n_certificate_policies=Certificate Policies
kl.npki.base.core.i18n_policy_mappings=Policy Mappings
kl.npki.base.core.i18n_policy_constraints=Policy Constraints
kl.npki.base.core.i18n_crl_number=CRL Number
kl.npki.base.core.i18n_delta_crl_indicator=Delta CRL Indicator
kl.npki.base.core.i18n_freshest_crl=Freshest CRL
kl.npki.base.core.i18n_certificate_transparency=Signed Certificate Timestamp List
kl.npki.base.core.i18n_netscape_certificate_type=Netscape Certificate Type
kl.npki.base.core.i18n_post_quantum_public_key=Post-Quantum Public Key
kl.npki.base.core.i18n_post_quantum_signature=Post-Quantum Signature
kl.npki.base.core.i18n_this_update=Effective date
kl.npki.base.core.i18n_next_update=Next update
kl.npki.base.core.i18n_ocsp_no_check=OCSP no check
kl.npki.base.core.i18n_smime_capabilities=SMIME capabilities
kl.npki.base.core.i18n_template_name=Microsoft Certificate template Name

# kl.npki.base.core.biz.check.model.Category
kl.npki.base.core.i18n_database=Database
kl.npki.base.core.i18n_cert=Cert
kl.npki.base.core.i18n_admin=Admin
kl.npki.base.core.i18n_api=API
kl.npki.base.core.i18n_cache=Cache
kl.npki.base.core.i18n_logging=Log
kl.npki.base.core.i18n_network=Network
kl.npki.base.core.i18n_security=Security
kl.npki.base.core.i18n_performance=Performance
kl.npki.base.core.i18n_configuration=Config
kl.npki.base.core.i18n_backup_recovery=Backup
kl.npki.base.core.i18n_timer_job=Task

# kl.npki.base.core.biz.check.model.SelfCheckItemEnum
kl.npki.base.core.i18n_database_connectivity_check=Database Connectivity Self-Check
kl.npki.base.core.i18n_admin_cert_validity_check=Admin Cert Validity Self-Check
kl.npki.base.core.i18n_mgt_root_cert_validity_check=MGMT Root Cert Validity Self-Check
kl.npki.base.core.i18n_identity_cert_validity_check=Identity Cert Validity Self-Check
kl.npki.base.core.i18n_ssl_cert_validity_check=SSL Cert Validity Self-Check
kl.npki.base.core.i18n_admin_issue_integrity_check=Admin Issue Integrity Self-Check
kl.npki.base.core.i18n_timer_job_check=Scheduled Task Self-Check
kl.npki.base.core.i18n_license_validity_check=License Validity Self-Check
kl.npki.base.core.i18n_license_quota_check=License Quota Self-Check
kl.npki.base.core.i18n_km_service_availability_check=KM Service Availability Self-Check
kl.npki.base.core.i18n_sks_service_availability_check=Threshold Service Availability Self-Check
kl.npki.base.core.i18n_ca_resource_check=CA Resource Availability Self-Check
kl.npki.base.core.i18n_em_engine_service_check=HSM Service Availability Self-Check
kl.npki.base.core.i18n_database_service_check=Database Service Availability Self-Check
kl.npki.base.core.i18n_disk_space_check=Disk Space Self-Check
kl.npki.base.core.i18n_jvm_memory_check=JVM Memory Self-Check

# kl.npki.base.core.constant.CaLevel
kl.npki.base.core.i18n_root=Root Cert
kl.npki.base.core.i18n_sub=Sub Cert

# kl.npki.base.core.common.trace.InvocationType
kl.npki.base.core.i18n_administrator_operation=Browser administrator operation
kl.npki.base.core.i18n_tcp_service_interface=TCP service interface
kl.npki.base.core.i18n_http_service_interface=HTTP service interface
kl.npki.base.core.i18n_system_scheduled_task=System scheduled tasks

# kl.npki.base.core.biz.check.model.Severity
kl.npki.base.core.i18n_none=None
kl.npki.base.core.i18n_low=Low
kl.npki.base.core.i18n_medium=Medium
kl.npki.base.core.i18n_high=High

# kl.npki.base.core.biz.check.model.Status
kl.npki.base.core.i18n_success=Success
kl.npki.base.core.i18n_warning=Warning
kl.npki.base.core.i18n_failure=Failure

# kl.npki.base.core.constant.RegionAndLanguageConstant
kl.npki.base.core.i18n_RegionAndLanguageConstant.cn=China
kl.npki.base.core.i18n_RegionAndLanguageConstant.dz=Algeria

# kl.npki.base.core.constant.LanguageConstant
kl.npki.base.core.i18n_LanguageConstant.zh=Chinese
kl.npki.base.core.i18n_LanguageConstant.en=English

# kl.security.license.core.LicenseStatus
kl.npki.base.core.i18n_license_status_i18n_key=License status
kl.npki.base.core.i18n_license_status_valid_i18n_key=Valid
kl.npki.base.core.i18n_license_status_wrong_serial_i18n_key=License hardware binding mismatch
kl.npki.base.core.i18n_license_status_expired_i18n_key=License not yet active or expired
kl.npki.base.core.i18n_license_status_wrong_format_i18n_key=Invalid license format
kl.npki.base.core.i18n_license_status_wrong_sign_i18n_key=Invalid license signature
kl.npki.base.core.i18n_license_status_not_imported_i18n_key=No valid license imported
kl.npki.base.core.i18n_license_status_other_i18n_key=License Abnormal
kl.npki.base.core.i18n_license_status_about_to_expire_i18n_key=About to expire
kl.npki.base.core.i18n_expires_in_few_days_i18n_key=Expires in {0} days
kl.npki.base.core.i18n_fail_to_check_license_validity_period_i18n_key=Fail to check license validity period

# kl.npki.base.core.constant.CaKeyResponseStandardVersionEnum
kl.npki.base.core.i18n_gmt00142023=GM/T 0014-2023 CA Key Service Response Packaging Format
kl.npki.base.core.i18n_gmt00142012=GM/T 0014-2012 CA Key Service Response Packaging Format

# 自检日志打印相关
kl.npki.base.core.i18n_self_check_success_log=Self-Check Item: [{}] - Category: [{}] - Environment ID: [{}], Self-Check Successful! Check Time: [{}], Duration: [{}]ms
kl.npki.base.core.i18n_self_check_warning_log=Self-Check Item: [{}] - Category: [{}] - Environment ID: [{}], Self-Check Warning! Check Time: [{}], Duration: [{}]ms, Warning Reason: {}, Detailed Information: {}
kl.npki.base.core.i18n_self_check_error_log=Self-Check Item: [{}] - Category: [{}] - Environment ID: [{}], Self-Check Failed! Check Time: [{}], Duration: [{}]ms, Failure Reason: {}, Detailed Information: {}

#CertificateTemplateEnum
kl.npki.base.core.i18n_microsoft_template_user=Microsoft User certificate template
kl.npki.base.core.i18n_microsoft_template_webserver=Microsoft Web Server Certificate template
kl.npki.base.core.i18n_microsoft_template_domaincontroller=Microsoft Domain controller Certificate template
kl.npki.base.core.i18n_microsoft_template_machine=Microsoft Computer Certificate Template

# EntityStatus
kl.npki.base.core.i18n_EntityStatus.normal=Normal
kl.npki.base.core.i18n_EntityStatus.to_be_audit=To be audit
kl.npki.base.core.i18n_EntityStatus.revoked=Revoked
kl.npki.base.core.i18n_EntityStatus.freeze=Freeze
kl.npki.base.core.i18n_EntityStatus.expired=Expired
kl.npki.base.core.i18n_EntityStatus.unknown=Unknown

# kl.npki.base.core.biz.inspection.InspectionItemTypeEnum
kl.npki.base.core.i18n_inspection_system_resource=System Resources Inspection Items
kl.npki.base.core.i18n_inspection_license_resource=System License Inspection Items
kl.npki.base.core.i18n_inspection_system_cert_resource=System Certificates Inspection Items
kl.npki.base.core.i18n_inspection_internal_server_status=Internal Server Inspection Items
kl.npki.base.core.i18n_inspection_external_server_status=External Server Inspection Items
kl.npki.base.core.i18n_inspection_business_data=System Business Data Inspection Items

# kl.npki.base.core.biz.check.SelfCheckManager
kl.npki.base.core.i18n_self_check_exception_occurred=Exception occurred during self-check，Please check the background log
kl.npki.base.core.i18n_self_check_exception_type=Self-check Exception Type
kl.npki.base.core.i18n_self_check_exception_message=Self-check Exception Message

# kl.npki.base.core.constant.AuditStatusEnum
kl.npki.base.core.i18n_AuditStatusEnum_no_audit=No Audit
kl.npki.base.core.i18n_AuditStatusEnum_pending=Pending Audit
kl.npki.base.core.i18n_AuditStatusEnum_success=Audit Successful
kl.npki.base.core.i18n_AuditStatusEnum_failed=Audit Failed

# kl.npki.base.core.constant.CertPurposeEnum
kl.npki.base.core.i18n_CertPurposeEnum_none=No purpose
kl.npki.base.core.i18n_CertPurposeEnum_sign=Signature certificate
kl.npki.base.core.i18n_CertPurposeEnum_encrypt=Encryption certificate
kl.npki.base.core.i18n_CertPurposeEnum_sign_and_encrypt=Signature and encryption certificate

# 日期格式化配置
kl.npki.base.core.i18n_day_pattern=dd/MM/yyyy
kl.npki.base.core.i18n_date_time_pattern=dd/MM/yyyy HH:mm:ss
kl.npki.base.core.i18n_date_time_ms_pattern=dd/MM/yyyy HH:mm:ss
kl.npki.base.core.i18n_file_date_time_pattern=ddMMyyyyHHmmss

# 系统日志国际化
kl.npki.base.core.i18n_OpLogInfo.traceId=Link ID
kl.npki.base.core.i18n_OpLogInfo.spanId=Span ID
kl.npki.base.core.i18n_OpLogInfo.logWho=Operator ID
kl.npki.base.core.i18n_OpLogInfo.username=Administrator Name
kl.npki.base.core.i18n_OpLogInfo.logDo=Operation Name
kl.npki.base.core.i18n_OpLogInfo.result=Operation Result
kl.npki.base.core.i18n_OpLogInfo.logWhat=Operation Details
kl.npki.base.core.i18n_OpLogInfo.auditStatus=Audit status
kl.npki.base.core.i18n_OpLogInfo.clientIp=Client lP
kl.npki.base.core.i18n_OpLogInfo.serverIp=Server IP
kl.npki.base.core.i18n_OpLogInfo.logWhen=Operation Time
kl.npki.base.core.i18n_OpLogInfo.logEnd=End Time
kl.npki.base.core.i18n_OpLogInfo.signData=Signature Value
kl.npki.base.core.i18n_OpLogInfo.request=Request
kl.npki.base.core.i18n_OpLogInfo.auditTime=Audit time
kl.npki.base.core.i18n_OpLogInfo.originDataDesensitize=Original signed text
kl.npki.base.core.i18n_OpLogInfo.elapsedTime=Time consuming
kl.npki.base.core.i18n_OpLogInfo.fullDataHash=Integrity protection summary value

kl.npki.base.core.i18n_ApiLogInfo.traceId=Link ID
kl.npki.base.core.i18n_ApiLogInfo.spanId=Span ID
kl.npki.base.core.i18n_ApiLogInfo.clientIp=Client lP
kl.npki.base.core.i18n_ApiLogInfo.logWhen=Request Time
kl.npki.base.core.i18n_ApiLogInfo.bizId=Business ID
kl.npki.base.core.i18n_ApiLogInfo.biz=Business Name
kl.npki.base.core.i18n_ApiLogInfo.detail=Operation details
kl.npki.base.core.i18n_ApiLogInfo.result=Request Result
kl.npki.base.core.i18n_ApiLogInfo.elapsedTime=Time consuming
kl.npki.base.core.i18n_ApiLogInfo.callerId=Caller ID
kl.npki.base.core.i18n_ApiLogInfo.callerName=Caller Name
kl.npki.base.core.i18n_ApiLogInfo.entityId=Business User Identification
kl.npki.base.core.i18n_ApiLogInfo.certId=Certificate Identification

#TrustCertEventEnum
kl.npki.base.core.i18n_import_trust_cert=Import trust cert
kl.npki.base.core.i18n_delete_trust_cert=Delete trust cert