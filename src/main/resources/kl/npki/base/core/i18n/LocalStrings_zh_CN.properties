# BaseDbError
kl.npki.base.core.i18n_db_query_time_out=数据库查询超时
# BaseInternalError
kl.npki.base.core.i18n_public_key_convert_error=证书公钥转换失败！
kl.npki.base.core.i18n_asym_algo_convert_error=公钥非对称算法转换失败！
kl.npki.base.core.i18n_signing_cert_data_error=生成证书签名错误
kl.npki.base.core.i18n_cert_issue_error=签发证书错误
kl.npki.base.core.i18n_root_cert_issuer_error=签发管理根错误
kl.npki.base.core.i18n_cert_req_gen_error=证书请求生成失败
kl.npki.base.core.i18n_root_cert_already_exist=根证书已存在
kl.npki.base.core.i18n_gen_pkcs10_request_error=生成P10请求失败
kl.npki.base.core.i18n_dn_build_error=DN构造失败
kl.npki.base.core.i18n_id_cert_already_exist=身份证书已存在
kl.npki.base.core.i18n_cert_encode_error=证书ASN结构编码失败
kl.npki.base.core.i18n_cert_decode_error=证书ASN结构解码失败
kl.npki.base.core.i18n_cert_parse_error=证书解析失败
kl.npki.base.core.i18n_certificate_parse_error_tips=证书格式不正确，请确认输入的是有效的Base64编码证书
kl.npki.base.core.i18n_root_not_exist=管理根证书不存在
kl.npki.base.core.i18n_pkcs10_request_encode_error=P10请求编码失败
kl.npki.base.core.i18n_cert_already_exist=证书已存在
kl.npki.base.core.i18n_cert_pub_key_not_matched=公钥信息不匹配
kl.npki.base.core.i18n_cert_req_not_gen=证书请求未生成
kl.npki.base.core.i18n_cert_publickey_ecode_error=证书公钥编码失败
kl.npki.base.core.i18n_keystore_already_exists=Keystore文件已存在
kl.npki.base.core.i18n_generate_keystore_error=生成Keystore文件失败
kl.npki.base.core.i18n_not_found_cert_from_keystore=从keystore文件中未找到目标证书
kl.npki.base.core.i18n_found_cert_from_keystore_failed=从keystore文件中查找证书出现异常
kl.npki.base.core.i18n_export_ssl_site_cert_error=导出SSL站点证书失败
kl.npki.base.core.i18n_root_cert_init_get_lock_error=管理根初始化获取锁失败
kl.npki.base.core.i18n_trust_cert_not_exist_error=删除证书失败，信任证书不存在
kl.npki.base.core.i18n_ssl_cert_init_get_lock_error=SSL证书初始化获取锁失败
kl.npki.base.core.i18n_id_cert_init_get_lock_error=身份证书初始化获取锁失败
kl.npki.base.core.i18n_local_trust_cert_deleter_error=管理根证书不容许删除
kl.npki.base.core.i18n_cert_request_parsing_error=证书请求解析失败
kl.npki.base.core.i18n_mainkey_import_error=主密钥导入失败
kl.npki.base.core.i18n_mainkey_export_error=主密钥导出失败
kl.npki.base.core.i18n_mainkey_not_found_error=对应ID的主密钥未找到
kl.npki.base.core.i18n_mainkey_config_null_error=主密钥ID配置为空
kl.npki.base.core.i18n_mainkey_init_error=主密钥缓存初始化失败
kl.npki.base.core.i18n_mainkey_init_get_lock_error=主密钥初始化获取锁失败
kl.npki.base.core.i18n_key_pair_get_error=获取密钥对错误
kl.npki.base.core.i18n_sign_alg_get_error=获取签名算法错误
kl.npki.base.core.i18n_keytype_not_support_error=密码服务不支持该密钥类型
kl.npki.base.core.i18n_engine_service_init_error=密码服务初始化失败
kl.npki.base.core.i18n_engine_service_not_find_error=加密服务加载失败
kl.npki.base.core.i18n_algorithm_not_support_error=密码服务不支持该算法
kl.npki.base.core.i18n_asymmetric_encrypt_error=非对称加密失败
kl.npki.base.core.i18n_asymmetric_decrypt_error=非对称解密失败
kl.npki.base.core.i18n_symmetric_encrypt_error=对称加密失败
kl.npki.base.core.i18n_symmetric_decrypt_error=对称解密失败
kl.npki.base.core.i18n_engine_init_error=加密机初始化失败
kl.npki.base.core.i18n_data_padding_error=数据填充失败
kl.npki.base.core.i18n_data_unpadded_error=数据去填充失败
kl.npki.base.core.i18n_engine_cluster_already_exist=该集群名已存在
kl.npki.base.core.i18n_engine_cluster_not_found=该集群名对应的加密机集群不存在
kl.npki.base.core.i18n_signed_verify_error=签名验签不通过
kl.npki.base.core.i18n_sign_error=签名失败！
kl.npki.base.core.i18n_engine_lb_config_error=密码设备负载策略配置有误
kl.npki.base.core.i18n_asymmetric_config_error=对称算法已配置无法变更，如需调整请重新部署系统
kl.npki.base.core.i18n_license_verify_error=License校验失败
kl.npki.base.core.i18n_license_import_error=License导入失败
kl.npki.base.core.i18n_license_is_null=License为空
kl.npki.base.core.i18n_date_parse_error=日期转换失败
kl.npki.base.core.i18n_long_parse_error=Long类型转换失败
kl.npki.base.core.i18n_login_type_conversion_error=登录类型转换错误！
kl.npki.base.core.i18n_get_public_key_error=获取公钥失败
kl.npki.base.core.i18n_get_private_key_error=获取私钥失败
kl.npki.base.core.i18n_build_sm2_cipher_blob_error=构建SM2密文失败
kl.npki.base.core.i18n_file_create_fail=文件创建失败
kl.npki.base.core.i18n_gen_full_data_ori_data_error=生成完整性原文值失败
kl.npki.base.core.i18n_class_init_method_empty=缺少默认的类初始化方法
kl.npki.base.core.i18n_lambda_column_name_error=字段名解析异常
kl.npki.base.core.i18n_system_language_error=系统语言配置错误
kl.npki.base.core.i18n_unsupported_language_error=不支持的语言
kl.npki.base.core.i18n_algorithm_identifier_encode_error=算法标识符编码失败
kl.npki.base.core.i18n_trust_cert_not_found=信任证书未找到
kl.npki.base.core.i18n_cert_p7b_error=生成P7B证书链异常
kl.npki.base.core.i18n_file_write_fail=文件写入失败

kl.npki.base.core.i18n_socket_connection_failed=Socket连接失败
kl.npki.base.core.i18n_socket_stop_failed=Socket关闭失败
kl.npki.base.core.i18n_audit_ip_reach_failed=审计服务器IP不可用
kl.npki.base.core.i18n_audit_connection_failed=审计服务连接失败
kl.npki.base.core.i18n_audit_switch_unknown_protocol_error=未知的协议类型
kl.npki.base.core.i18n_audit_switch_appender_not_found_error=当前不存在SyslogAppender
kl.npki.base.core.i18n_org_batch_import_excel_gen_error=机构导入结果生成失败
kl.npki.base.core.i18n_org_batch_import_file_operate_error=机构批量导入时结果文件操作错误
kl.npki.base.core.i18n_org_delete_binding_error=机构存在绑定关系，无法删除
kl.npki.base.core.i18n_org_not_exist_error=组织机构不存在
# BaseRemoteError
kl.npki.base.core.i18n_upload_log_to_kcsp_failed=上报日志到KCSP失败
kl.npki.base.core.i18n_crl_parse_error=CRL解析失败
kl.npki.base.core.i18n_microsoft_cert_template_parse_error=微软证书模版解析失败

# BaseValidationError
kl.npki.base.core.i18n_cert_not_yet_valid_error=证书未生效
kl.npki.base.core.i18n_cert_expired_error=证书已经过期
kl.npki.base.core.i18n_license_is_empty=授权信息为空
kl.npki.base.core.i18n_license_content_error=License内容错误
kl.npki.base.core.i18n_validity_gt_issue_cert=证书有效期超过签发者证书
kl.npki.base.core.i18n_issuer_authority_key_id_is_null=颁发者授权密钥为空
kl.npki.base.core.i18n_subject_key_id_is_null=使用者授权密钥为空
kl.npki.base.core.i18n_mgr_root_key_algorithm_mismatch=请求的密钥算法与管理根的密钥算法不匹配!
kl.npki.base.core.i18n_not_trust_cert_error=证书不被信任
kl.npki.base.core.i18n_trust_cert_is_empty=信任证书为空
kl.npki.base.core.i18n_export_cert_error=导出证书失败
kl.npki.base.core.i18n_pq_enc_key_type_is_null=抗量子加密证书密钥类型为空
kl.npki.base.core.i18n_pq_enc_key_type_purpose_error=抗量子加密证书密钥使用类型需要为【加密】
kl.npki.base.core.i18n_sans_parsing_error_from_ext=从证书扩展项中读取SAN列表失败
kl.npki.base.core.i18n_cert_parse_failed=证书解析失败
kl.npki.base.core.i18n_algorithm_not_supported=不支持的算法类型
kl.npki.base.core.i18n_incorrect_recipient_info_number=至少需要有一个 RecipientInfo
kl.npki.base.core.i18n_encrypted_content_info_is_null=EncryptedContentInfo 为空
kl.npki.base.core.i18n_incorrect_signer_info_number=至少需要有一个 SignerInfo
kl.npki.base.core.i18n_self_check_not_supported=不支持的自检类型
kl.npki.base.core.i18n_user_status_error=用户状态错误
kl.npki.base.core.i18n_user_cert_status_error=用户证书状态错误
kl.npki.base.core.i18n_file_config_entity_params_is_invalid=文件配置实体参数不合法
kl.npki.base.core.i18n_param_error=参数错误
kl.npki.base.core.i18n_unsupported_charset=不支持的字符集
kl.npki.base.core.i18n_integrity_verification_failed=数据完整性验证失败，可能存在篡改
kl.npki.base.core.i18n_general_verification_error=数据完整性验证过程中发生错误
kl.npki.base.core.i18n_upload_file_format_error=上传文件格式与接口不匹配
kl.npki.base.core.i18n_upload_file_header_is_incorrect=上传文件的列头格式不匹配
kl.npki.base.core.i18n_upload_file_check_data_empty_error=上传文件检查模板表头为空
kl.npki.base.core.i18n_upload_file_search_status_error=文件内容正在处理无法查询处理结果
kl.npki.base.core.i18n_upload_file_not_found_fail=上传文件不存在
kl.npki.base.core.i18n_org_not_found=组织机构不存在
kl.npki.base.core.i18n_org_parent_not_found=父机构不存在
kl.npki.base.core.i18n_org_add_eq_error=子机构编码不能与父机构编码相同
kl.npki.base.core.i18n_org_batch_delete_not_selected_all_sub_org_error=删除该机构时，未选中所有子机构，无法删除该机构
kl.npki.base.core.i18n_org_delete_error=机构删除失败
kl.npki.base.core.i18n_org_name_null_error=机构名称不能为空
kl.npki.base.core.i18n_org_code_null_error=机构编码不能为空
kl.npki.base.core.i18n_org_full_code_null_error=完整机构编码不能为空
kl.npki.base.core.i18n_org_status_error=机构状态不正确
kl.npki.base.core.i18n_org_code_repeat_error=机构编码重复
kl.npki.base.core.i18n_import_cert_chain_error=导入证书链错误
kl.npki.base.core.i18n_not_ca_cert=当前证书不是CA证书
kl.npki.base.core.i18n_cert_request_sign_error=证书请求签名验证失败
kl.npki.base.core.i18n_pri_key_parse_failed=私钥解析失败
kl.npki.base.core.i18n_enum_validator_definition_error=枚举校验器定义错误
kl.npki.base.core.i18n_login_type_null_error=登录认证类型为空
kl.npki.base.core.i18n_login_type_not_support_error=不支持的登录认证类型
kl.npki.base.core.i18n_config_encryption_key_file_not_found_error=加密环境初始化失败
kl.npki.base.core.i18n_config_encryption_key_file_load_error=加密环境初始化失败

# 枚举
kl.npki.base.core.i18n_table_sharding=分表创建定时任务
kl.npki.base.core.i18n_self_check=服务自检定时任务
kl.npki.base.core.i18n_home_page_data_update=首页数据缓存更新定时任务
kl.npki.base.core.i18n_system_inspection=系统巡检定时任务
kl.npki.base.core.i18n_self_issue=自签发
kl.npki.base.core.i18n_external_import=外部导入
kl.npki.base.core.i18n_recovered=证书被恢复
kl.npki.base.core.i18n_key_updated=密钥被更新
kl.npki.base.core.i18n_extend=证书被延期
kl.npki.base.core.i18n_updated=证书被更新
kl.npki.base.core.i18n_revoked=证书被废除
kl.npki.base.core.i18n_to_be_issued=待签发
kl.npki.base.core.i18n_issued=已签发
kl.npki.base.core.i18n_update_to_be_checked=更新待审核
kl.npki.base.core.i18n_to_be_updated=待更新
kl.npki.base.core.i18n_extend_to_be_checked=延期待审核
kl.npki.base.core.i18n_to_be_extended=待延期
kl.npki.base.core.i18n_frozen=证书被冻结
kl.npki.base.core.i18n_reissue_to_be_checked=重发待审核
kl.npki.base.core.i18n_to_be_recovered=待恢复
kl.npki.base.core.i18n_to_be_reissued=待重发
kl.npki.base.core.i18n_freeze_to_be_checked=冻结待审核
kl.npki.base.core.i18n_to_be_frozen=待冻结
kl.npki.base.core.i18n_unfreeze_to_be_checked=解冻待审核
kl.npki.base.core.i18n_to_be_unfrozen=待解冻
kl.npki.base.core.i18n_key_update_to_be_checked=密钥更新待审核
kl.npki.base.core.i18n_key_to_be_update=密钥待更新
kl.npki.base.core.i18n_recovery_to_be_checked=恢复待审核
kl.npki.base.core.i18n_revoke_to_be_checked=废除待审核
kl.npki.base.core.i18n_revoke_at_frozent_status_to_be_checked=冻结废除待审核
kl.npki.base.core.i18n_to_be_revoked=待废除
kl.npki.base.core.i18n_to_be_revoked_at_frozent_status=冻结待废除
kl.npki.base.core.i18n_self_sign_cert=自签发证书
kl.npki.base.core.i18n_user_sign_cert=用户签名证书
kl.npki.base.core.i18n_user_enc_cert=用户加密证书
kl.npki.base.core.i18n_ssl_sign_server_cert=SSL签名服务端证书
kl.npki.base.core.i18n_ssl_enc_server_cert=SSL加密服务端证书
kl.npki.base.core.i18n_ssl_sign_client_cert=SSL签名客户端证书
kl.npki.base.core.i18n_ssl_enc_client_cert=SSL加密客户端证书
kl.npki.base.core.i18n_file_engine=文件密码机
kl.npki.base.core.i18n_standard_engine=标准密码机
kl.npki.base.core.i18n_emulator_engine=高性能密码机
kl.npki.base.core.i18n_koal_engine=KOAL密码机
kl.npki.base.core.i18n_china_core_ccp_907_t=国芯密码卡CCP-907-T
kl.npki.base.core.i18n_mucse_rsp_20=沐创密码卡RSP-20
kl.npki.base.core.i18n_mucse_rsp_20_cap=沐创密码卡RSP-20-CAP
kl.npki.base.core.i18n_sansec=三未信安SJJ1012A密码机
kl.npki.base.core.i18n_sansec_fips=三未信安FIPS密码机
kl.npki.base.core.i18n_hongsi=宏思密码卡
kl.npki.base.core.i18n_hygon=海光密码卡
kl.npki.base.core.i18n_demo=测试管理环境
kl.npki.base.core.i18n_default=正式管理环境
kl.npki.base.core.i18n_pwd=用户名密码登录
kl.npki.base.core.i18n_sign=证书签名登录
kl.npki.base.core.i18n_sign_pwd=证书签名和密码登录
kl.npki.base.core.i18n_sso=SSO单点登录
kl.npki.base.core.i18n_multi_sign_2_of_3=3选2证书签名登录
kl.npki.base.core.i18n_multi_sign_3_of_5=5选3证书签名登录
kl.npki.base.core.i18n_api_log=服务日志
kl.npki.base.core.i18n_op_log=操作日志
kl.npki.base.core.i18n_super=上级证书
kl.npki.base.core.i18n_local=本级证书
kl.npki.base.core.i18n_manage=管理证书
kl.npki.base.core.i18n_ssl=SSL证书
kl.npki.base.core.i18n_identity=身份证书
kl.npki.base.core.i18n_cancelled=已注销
kl.npki.base.core.i18n_register_to_be_checked=用户注册待审核
kl.npki.base.core.i18n_register_check_failed=注册审核未通过
kl.npki.base.core.i18n_normal=正常
kl.npki.base.core.i18n_cancel_to_be_checked=用户注销待审核
kl.npki.base.core.i18n_to_be_cancled=用户待注销
kl.npki.base.core.i18n_locked=已锁定
kl.npki.base.core.i18n_sct_version=版本
kl.npki.base.core.i18n_sct_log_id=日志服务器标识
kl.npki.base.core.i18n_sct_timestamp=日志时间
kl.npki.base.core.i18n_sct_extensions=扩展项
kl.npki.base.core.i18n_sct_hash_algorithm=摘要算法
kl.npki.base.core.i18n_sct_signature_algorithm=签名算法
kl.npki.base.core.i18n_sct_signature=签名值
kl.npki.base.core.i18n_ca_issuers=证书机构颁发者
kl.npki.base.core.i18n_ocsp=联机证书状态协议
kl.npki.base.core.i18n_is_ca=是证书授权中心
kl.npki.base.core.i18n_is_not_ca=不是证书授权中心
kl.npki.base.core.i18n_path_len_constraint=中级CA证书数目的上限为
kl.npki.base.core.i18n_cabf_enhanced_validation_ssl_certificate_policy=增强验证SSL证书
kl.npki.base.core.i18n_cabf_ssl_certificate_minimum_required_policy=SSL证书最低要求策略
kl.npki.base.core.i18n_cabf_domain_name_validation_ssl_certificate_policy=域名验证SSL证书
kl.npki.base.core.i18n_cabf_organization_validation_ssl_certificate_policy=组织验证SSL证书
kl.npki.base.core.i18n_cabf_personal_validation_ssl_certificate_policy=个人验证SSL证书
kl.npki.base.core.i18n_cabf_enhanced_verification_code_signing_certificate_policy=增强验证代码签名证书
kl.npki.base.core.i18n_cabf_code_signing_certificate_minimum_required_policy=代码签名证书最低要求
kl.npki.base.core.i18n_cabf_code_signing_certificate_minimum_required_timestamp_policy=代码签名证书最低要求时间戳
kl.npki.base.core.i18n_cabf_s_mime_certificate_minimum_required_policy=S/MIME证书最低要求
kl.npki.base.core.i18n_cabf_s_mime_email_legacy_certificate_policy=S/MIME邮箱验证-遗留证书
kl.npki.base.core.i18n_cabf_s_mime_email_multi_purpose_certificate_policy=S/MIME邮箱验证-多用途证书
kl.npki.base.core.i18n_cabf_s_mime_email_strict_certificate_policy=S/MIME邮箱验证-严格证书
kl.npki.base.core.i18n_cabf_s_mime_organization_legacy_certificate_policy=S/MIME组织验证-遗留证书
kl.npki.base.core.i18n_cabf_s_mime_organization_email_multi_purpose_certificate_policy=S/MIME组织验证-多用途证书
kl.npki.base.core.i18n_cabf_s_mime_organization_email_strict_certificate_policy=S/MIME组织验证-严格证书
kl.npki.base.core.i18n_cabf_s_mime_sponsors_legacy_certificate_policy=S/MIME赞助商验证-遗留证书
kl.npki.base.core.i18n_cabf_s_mime_sponsors_organization_email_multi_purpose_certificate_policy=S/MIME赞助商验证-多用途证书
kl.npki.base.core.i18n_cabf_s_mime_sponsors_organization_email_strict_certificate_policy=S/MIME赞助商验证-严格证书
kl.npki.base.core.i18n_cabf_s_mime_personal_legacy_certificate_policy=S/MIME个人验证-遗留证书策略
kl.npki.base.core.i18n_cabf_s_mime_personal_organization_email_multi_purpose_certificate_policy=S/MIME个人验证-多用途证书
kl.npki.base.core.i18n_cabf_s_mime_personal_organization_email_strict_certificate_policy=S/MIME个人验证-严格证书
kl.npki.base.core.i18n_unspecified=未指定
kl.npki.base.core.i18n_key_compromise=密钥泄露
kl.npki.base.core.i18n_ca_compromise=CA密钥泄露
kl.npki.base.core.i18n_affiliation_changed=隶属关系变化
kl.npki.base.core.i18n_superseded=证书被替换
kl.npki.base.core.i18n_cessation_of_operation=操作终止
kl.npki.base.core.i18n_certificate_hold=证书冻结
kl.npki.base.core.i18n_remove_from_crl=从CRL中删除
kl.npki.base.core.i18n_privilege_withdrawn=权限被撤销
kl.npki.base.core.i18n_aa_compromise=属性授权机构泄露
kl.npki.base.core.i18n_other_name=其他名称
kl.npki.base.core.i18n_rfc822_name=电子邮件地址
kl.npki.base.core.i18n_dns_name=DNS 名称
kl.npki.base.core.i18n_x400_address=X.400 地址
kl.npki.base.core.i18n_directory_name=目录名称
kl.npki.base.core.i18n_edi_party_name=电子数据交换（EDI）实体名称
kl.npki.base.core.i18n_uniform_resource_identifier=URL
kl.npki.base.core.i18n_ip_address=IP 地址
kl.npki.base.core.i18n_registered_id=注册标识符
kl.npki.base.core.i18n_digital_signature=数字签名
kl.npki.base.core.i18n_non_repudiation=防止抵赖
kl.npki.base.core.i18n_key_encipherment=密钥加密
kl.npki.base.core.i18n_data_encipherment=数据加密
kl.npki.base.core.i18n_key_agreement=密钥协商
kl.npki.base.core.i18n_key_cert_sign=证书签发
kl.npki.base.core.i18n_crl_sign=CRL签发
kl.npki.base.core.i18n_encipher_only=仅能加密
kl.npki.base.core.i18n_decipher_only=仅能解密
kl.npki.base.core.i18n_ssl_client=SSL客户端认证
kl.npki.base.core.i18n_ssl_server=SSL服务器认证
kl.npki.base.core.i18n_smime=S/MIME证书
kl.npki.base.core.i18n_object_signing=对象签名
kl.npki.base.core.i18n_reserved=保留使用
kl.npki.base.core.i18n_ssl_ca=SSL证书颁发机构
kl.npki.base.core.i18n_smime_ca=S/MIME证书颁发机构
kl.npki.base.core.i18n_object_signing_ca=对象签名证书颁发机构
kl.npki.base.core.i18n_der=DER 编码二进制 X.509
kl.npki.base.core.i18n_pem=Base64 编码 X.509
kl.npki.base.core.i18n_p7b=加密消息语法标准-PKCS #7 证书
kl.npki.base.core.i18n_security_log_fileName=安全操作日志
kl.npki.base.core.i18n_operation_log_fileName=业务操作日志
kl.npki.base.core.i18n_api_log_fileName=系统服务日志

# 异常描述信息
kl.npki.base.core.i18n_unsupported_key_type_type_is=不支持的密钥类型，类型为{0}
kl.npki.base.core.i18n_get_public_key_failed=获取加密密钥对的公钥失败
kl.npki.base.core.i18n_the_current_type_of_anti_quantum_encryption_key_used_is=当前使用的抗量子加密密钥类型为{0}
kl.npki.base.core.i18n_trust_cert_public_key_convert_error=信任证书公钥转换失败
kl.npki.base.core.i18n_please_generate_cert_request=请先生成证书请求
kl.npki.base.core.i18n_the_validDays_is_less_than_1=有效天数小于1
kl.npki.base.core.i18n_the_cert_type_is_not_supported=证书类型{0}不支持
kl.npki.base.core.i18n_the_key_pair_is_null=密钥对为空
kl.npki.base.core.i18n_the_certificate_has_not_yet_taken_effect_with_an_effective_date_of=证书{0}尚未生效，生效时间为{1}
kl.npki.base.core.i18n_the_certificate_has_expired_with_an_expiration_date_of={0}证书已经失效,失效时间为: {1}
kl.npki.base.core.i18n_certificate_validity_exceeds_issuer_validity=待签发证书有效期[{0}]超出颁发者证书有效期[{1}]
kl.npki.base.core.i18n_id_cert_public_key_convert_error=身份证书公钥转换失败
kl.npki.base.core.i18n_the_id_cert_issue_error=身份证书签发失败
kl.npki.base.core.i18n_the_ssl_cert_issue_error=SSL通信证书签发失败
kl.npki.base.core.i18n_the_trusted_certificate_list_is_empty=可信证书链为空，请先签发或导入证书链
kl.npki.base.core.i18n_the_certificate_to_be_verified_cannot_be_empty=待验证证书不能为空
kl.npki.base.core.i18n_trust_cert_id=信任证书ID{0}
kl.npki.base.core.i18n_the_root_cert_already_exist=根证书已存在
kl.npki.base.core.i18n_please_import_root_cert=请先导入根证书
kl.npki.base.core.i18n_issue_manage_root_cert_error=签发管理根证书失败
kl.npki.base.core.i18n_the_root_cert_not_exist=根证书不存在
kl.npki.base.core.i18n_hexadecimal_certificate_serial_number=十六进制证书序列号{0}
kl.npki.base.core.i18n_cert_not_found=证书不存在
kl.npki.base.core.i18n_cert_is_empty=证书为空
kl.npki.base.core.i18n_the_check_item_is_not_registered=待审核项目{0}未注册
kl.npki.base.core.i18n_file_config_entity_is_null=文件配置实体为空
kl.npki.base.core.i18n_file_name_is_empty=文件名为空
kl.npki.base.core.i18n_file_Path_is_empty=文件路径为空
kl.npki.base.core.i18n_file_Content_Base64_is_empty=文件内容Base64为空
kl.npki.base.core.i18n_an_error_occurred_while_uploading_the_administrator_operation_log=管理员操作日志上传失败
kl.npki.base.core.i18n_get_license_status_failed=获取授权状态失败
kl.npki.base.core.i18n_license_content_is_invalid=授权内容无效
kl.npki.base.core.i18n_license_content_is_invalid_with_status=授权内容无效，状态为{0}
kl.npki.base.core.i18n_contains_illegal_characters=包含非法字符
kl.npki.base.core.i18n_the_master_key_id_is=主密钥ID为{0}
kl.npki.base.core.i18n_signature_verification_failed=签名验证失败
kl.npki.base.core.i18n_signature_is_null=签名值为空
kl.npki.base.core.i18n_undefined_load_type=未定义的加载类型[{0}]
kl.npki.base.core.i18n_weight_configuration_is_null=权重配置为空
kl.npki.base.core.i18n_the_device_is_missing_weight_values=设备[{0}]缺少权重值
kl.npki.base.core.i18n_the_device_weight_value_is_less_than_0=设备[{0}]权重值小于0
kl.npki.base.core.i18n_the_total_weight_of_the_device_is_less_than_or_equal_to0=[{0}]设备总权重小于等于0
kl.npki.base.core.i18n_master_key_algorithm_conversion_failed=主密钥算法转换失败
kl.npki.base.core.i18n_login_type_is_null=登录类型为空
kl.npki.base.core.i18n_system_language_cannot_be_modified_after_deployment=系统语言不能在部署后修改
kl.npki.base.core.i18n_the_original_text_is_empty={0}原文为空
kl.npki.base.core.i18n_hash_value_is_empty={0}哈希值为空
kl.npki.base.core.i18n_certificate_data_format_abnormality=证书数据格式异常
kl.npki.base.core.i18n_certificate_encoding_exception=证书编码异常
kl.npki.base.core.i18n_encoding_or_decoding_failed_while_extracting_sans=提取SANs时编码或解码失败
kl.npki.base.core.i18n_trust_keystore_file_already_exists=信任Keystore文件已存在[{0}]
kl.npki.base.core.i18n_error_generating_trust_keystore=生成信任Keystore文件失败
kl.npki.base.core.i18n_pfx_password_is_empty=PFX密码为空
kl.npki.base.core.i18n_failed_to_obtain_anti_quantum_protection_public_key_from_extension_extension_is_empty=从扩展中获取抗量子保护公钥失败，扩展为空
kl.npki.base.core.i18n_failed_to_obtain_anti_quantum_protection_public_key_from_extension_there_is_no_corresponding_extension=从扩展中获取抗量子保护公钥失败，没有对应的扩展
kl.npki.base.core.i18n_failed_to_obtain_private_key=获取私钥失败
kl.npki.base.core.i18n_the_get_full_data_hash_method_of_the_data_object_must_be_public=数据对象的getFullDataHash方法必须是public的
kl.npki.base.core.i18n_the_getFullDataHash_method_of_a_data_object_must_return_a_string_type=数据对象的getFullDataHash方法必须返回一个String类型
kl.npki.base.core.i18n_the_data_object_must_have_a_parameter_free_method_called_getFullDataHash=数据对象必须有一个没有参数的方法名为getFullDataHash
kl.npki.base.core.i18n_the_getFullDataHash_method_that_cannot_access_the_data_object= 无法访问数据对象的getFullDataHash方法
kl.npki.base.core.i18n_an_error_occurred_while_calling_the_getFullDataHash_method_of_the_data_object=调用数据对象的getFullDataHash方法时发生错误
kl.npki.base.core.i18n_failed_to_obtain_field_name=获取字段名失败
kl.npki.base.core.i18n_could_not_create_keystore_directory=无法创建Keystore目录{0}
kl.npki.base.core.i18n_the_key_data_directory_does_not_exist=未检测到 {0}[{1}]的密钥数据目录，请使用encryption-fusion工具为其生成内置密钥。生成时请将结果路径指定为：{2}，如已生成则将已生成的目录拷贝为：" {2}
kl.npki.base.core.i18n_the_current_status_is_not_pending_approval=当前状态不是待审核状态
kl.npki.base.core.i18n_account_locking_failed_unknown_user_status_code=锁定账户失败，未知用户状态码[{0}]
kl.npki.base.core.i18n_the_current_state_is_not_locked=当前状态不是锁定状态
kl.npki.base.core.i18n_unlocking_account_failed_unknown_user_status_code=解锁账户失败，未知用户状态码[{0}]
kl.npki.base.core.i18n_cn_item_is_empty_unable_to_construct_certificate_dn=CN项为空，无法构建证书DN
kl.npki.base.core.i18n_date_format=yyyy年MM月dd日 HH时mm分ss秒
kl.npki.base.core.i18n_database_not_configured=数据库未配置
kl.npki.base.core.i18n_common_unknown=未知
kl.npki.base.core.i18n_illegal_enum_type=字段{0}的值无效，必须是{1}中的一个

# 常量
kl.npki.base.core.i18n_fingerprint=指纹
kl.npki.base.core.i18n_signature_information=签名值
kl.npki.base.core.i18n_public_key=公钥
kl.npki.base.core.i18n_expiration_time=失效时间
kl.npki.base.core.i18n_effective_time=生效时间
kl.npki.base.core.i18n_signature_algorithm=签名算法
kl.npki.base.core.i18n_issuer=颁发者
kl.npki.base.core.i18n_subject=使用者
kl.npki.base.core.i18n_serial_number=序列号
kl.npki.base.core.i18n_version_number=版本号
kl.npki.base.core.i18n_hexadecimal=16进制序列号
kl.npki.base.core.i18n_decimal=10进制序列号
kl.npki.base.core.i18n_authorization_center_information_access_permissions=授权信息访问
kl.npki.base.core.i18n_unrestricted=无限制
kl.npki.base.core.i18n_unknown_certificate_policy=未知证书策略
kl.npki.base.core.i18n_currently_only_v1_version_parsing_is_supported=证书透明度扩展项解析失败,目前仅支持V1版本解析
kl.npki.base.core.i18n_unknown_version=未知版本
kl.npki.base.core.i18n_crl_distribution_point_url=分发点地址
kl.npki.base.core.i18n_crl_distribution_point=[{0}]CRL分发点
kl.npki.base.core.i18n_crl_publisher=CRL颁发者
kl.npki.base.core.i18n_crl_revocation_reason=吊销原因
kl.npki.base.core.i18n_crl_serial_number=CRL编号
kl.npki.base.core.i18n_crl_param_decimal=(10进制)
kl.npki.base.core.i18n_crl_param_hexadecimal=(16进制)
kl.npki.base.core.i18n_incremental_crl=增量CRL
kl.npki.base.core.i18n_strategic_constraints=策略约束
kl.npki.base.core.i18n_skip_policy_check_certificate_quantity=可跳过策略检查证书数量
kl.npki.base.core.i18n_number_of_certificates_that_can_be_mapped_using_policies=可使用策略映射证书数量
kl.npki.base.core.i18n_strategy_mapping=策略映射
kl.npki.base.core.i18n_issuer_domain_policy=颁发者域策略
kl.npki.base.core.i18n_subject_domain_strategy=主体域策略
kl.npki.base.core.i18n_name=名称
kl.npki.base.core.i18n_subject_name=主体名称
kl.npki.base.core.i18n_entity_name=实体名称
kl.npki.base.core.i18n_name_allocator=名称分配者
kl.npki.base.core.i18n_server_authentication=服务器认证
kl.npki.base.core.i18n_client_authentication=客户端认证
kl.npki.base.core.i18n_code_signing=代码签名
kl.npki.base.core.i18n_email_protection=Email保护
kl.npki.base.core.i18n_timestamp_generation=时间戳
kl.npki.base.core.i18n_ocsp_response_signing=OCSP应答签名
kl.npki.base.core.i18n_kdc_authentication=KDC身份认证
kl.npki.base.core.i18n_smart_card_login=微软智能卡登录
kl.npki.base.core.i18n_file_system_encryption=微软文件系统加密
kl.npki.base.core.i18n_key_usage=密钥用法
kl.npki.base.core.i18n_extended_key_usage=增强型密钥用法
kl.npki.base.core.i18n_basic_constraints=基本约束
kl.npki.base.core.i18n_subject_key_identifier=使用者密钥标识符
kl.npki.base.core.i18n_authority_key_identifier=授权者密钥标识符
kl.npki.base.core.i18n_subject_alternative_name=使用者备用名称
kl.npki.base.core.i18n_issuer_alternative_name=颁发者备用名称
kl.npki.base.core.i18n_crl_distribution_points=CRL分发点
kl.npki.base.core.i18n_authority_information_access=颁发机构信息访问
kl.npki.base.core.i18n_certificate_policies=证书策略
kl.npki.base.core.i18n_policy_mappings=策略映射
kl.npki.base.core.i18n_policy_constraints=策略限制
kl.npki.base.core.i18n_crl_number=CRL数字
kl.npki.base.core.i18n_delta_crl_indicator=增量CRL指示器
kl.npki.base.core.i18n_freshest_crl=最新CRL
kl.npki.base.core.i18n_certificate_transparency=SCT 列表
kl.npki.base.core.i18n_netscape_certificate_type=Netscape证书类型
kl.npki.base.core.i18n_post_quantum_public_key=后量子公钥
kl.npki.base.core.i18n_post_quantum_signature=后量子签名值
kl.npki.base.core.i18n_this_update=生效日期
kl.npki.base.core.i18n_next_update=下一次更新的时间
kl.npki.base.core.i18n_ocsp_no_check=OCSP不撤销检查
kl.npki.base.core.i18n_smime_capabilities=SMIME性能
kl.npki.base.core.i18n_template_name=微软证书模板名称

# kl.npki.base.core.biz.check.model.Category
kl.npki.base.core.i18n_database=数据库
kl.npki.base.core.i18n_cert=证书
kl.npki.base.core.i18n_admin=管理员
kl.npki.base.core.i18n_api=API接口
kl.npki.base.core.i18n_cache=缓存
kl.npki.base.core.i18n_logging=日志系统
kl.npki.base.core.i18n_network=网络
kl.npki.base.core.i18n_security=安全
kl.npki.base.core.i18n_performance=性能
kl.npki.base.core.i18n_configuration=配置
kl.npki.base.core.i18n_backup_recovery=备份与恢复
kl.npki.base.core.i18n_timer_job=定时任务

# kl.npki.base.core.biz.check.model.SelfCheckItemEnum
kl.npki.base.core.i18n_database_connectivity_check=数据库连通性自检
kl.npki.base.core.i18n_admin_cert_validity_check=管理员证书有效期自检
kl.npki.base.core.i18n_mgt_root_cert_validity_check=管理根证书有效期自检
kl.npki.base.core.i18n_identity_cert_validity_check=身份证书有效期自检
kl.npki.base.core.i18n_ssl_cert_validity_check=SSL站点证书有效期自检
kl.npki.base.core.i18n_admin_issue_integrity_check=管理员签发完整性自检
kl.npki.base.core.i18n_timer_job_check=定时任务自检
kl.npki.base.core.i18n_license_validity_check=License有效期自检
kl.npki.base.core.i18n_license_quota_check=License配额检查
kl.npki.base.core.i18n_km_service_availability_check=密钥服务接口可用性自检
kl.npki.base.core.i18n_sks_service_availability_check=协同服务接口可用性自检
kl.npki.base.core.i18n_ca_resource_check=CA密钥资源数量自检
kl.npki.base.core.i18n_em_engine_service_check=密码机服务自检
kl.npki.base.core.i18n_database_service_check=数据库服务自检
kl.npki.base.core.i18n_disk_space_check=磁盘空间自检
kl.npki.base.core.i18n_jvm_memory_check=JVM内存自检

# kl.npki.base.core.constant.CaLevel
kl.npki.base.core.i18n_root=根证书
kl.npki.base.core.i18n_sub=下级证书

# kl.npki.base.core.common.trace.InvocationType
kl.npki.base.core.i18n_administrator_operation=浏览器管理员操作
kl.npki.base.core.i18n_tcp_service_interface=TCP服务接口
kl.npki.base.core.i18n_http_service_interface=HTTP服务接口
kl.npki.base.core.i18n_system_scheduled_task=系统定时任务

# kl.npki.base.core.biz.check.model.Severity
kl.npki.base.core.i18n_none=无
kl.npki.base.core.i18n_low=低级
kl.npki.base.core.i18n_medium=中级
kl.npki.base.core.i18n_high=高级

# kl.npki.base.core.biz.check.model.Status
kl.npki.base.core.i18n_success=成功
kl.npki.base.core.i18n_warning=警告
kl.npki.base.core.i18n_failure=失败

# kl.npki.base.core.constant.RegionAndLanguageConstant
kl.npki.base.core.i18n_RegionAndLanguageConstant.cn=中国
kl.npki.base.core.i18n_RegionAndLanguageConstant.dz=阿尔及利亚

# kl.npki.base.core.constant.LanguageConstant
kl.npki.base.core.i18n_LanguageConstant.zh=中文
kl.npki.base.core.i18n_LanguageConstant.en=英文

# kl.security.license.core.LicenseStatus
kl.npki.base.core.i18n_license_status_i18n_key=授权许可状态
kl.npki.base.core.i18n_license_status_valid_i18n_key=有效
kl.npki.base.core.i18n_license_status_wrong_serial_i18n_key=授权许可中的硬件与本机硬件不匹配
kl.npki.base.core.i18n_license_status_expired_i18n_key=授权许可未生效或已过期
kl.npki.base.core.i18n_license_status_wrong_format_i18n_key=授权许可格式错误
kl.npki.base.core.i18n_license_status_wrong_sign_i18n_key=授权许可签名无效
kl.npki.base.core.i18n_license_status_not_imported_i18n_key=系统尚未导入授权信息
kl.npki.base.core.i18n_license_status_other_i18n_key=授权许可异常
kl.npki.base.core.i18n_license_status_about_to_expire_i18n_key=即将过期
kl.npki.base.core.i18n_expires_in_few_days_i18n_key=有效期剩余{0}天
kl.npki.base.core.i18n_fail_to_check_license_validity_period_i18n_key=检测License有效期失败

# kl.npki.base.core.constant.CaKeyResponseStandardVersionEnum
kl.npki.base.core.i18n_gmt00142023=GM/T 0014-2023 CA密钥服务响应封装格式
kl.npki.base.core.i18n_gmt00142012=GM/T 0014-2012 CA密钥服务响应封装格式

# 自检日志打印相关
kl.npki.base.core.i18n_self_check_success_log=自检项: [{}] - 分类: [{}] - 环境标识: [{}]，自检成功！检测时间: [{}]，耗时: [{}]毫秒
kl.npki.base.core.i18n_self_check_warning_log=自检项: [{}] - 分类: [{}] - 环境标识: [{}]，自检警告！检测时间: [{}]，耗时: [{}]毫秒，警告原因: {}，详细自检信息: {}
kl.npki.base.core.i18n_self_check_error_log=自检项: [{}] - 分类: [{}] - 环境标识: [{}]，自检失败！检测时间: [{}]，耗时: [{}]毫秒，失败原因: {}，详细自检信息: {}

#CertificateTemplateEnum
kl.npki.base.core.i18n_microsoft_template_user=微软用户证书模版
kl.npki.base.core.i18n_microsoft_template_webserver=微软Web服务器模版
kl.npki.base.core.i18n_microsoft_template_domaincontroller=微软控制器证书模板
kl.npki.base.core.i18n_microsoft_template_machine=微软计算机证书模板

# EntityStatus
kl.npki.base.core.i18n_EntityStatus.normal=正常
kl.npki.base.core.i18n_EntityStatus.to_be_audit=待审核
kl.npki.base.core.i18n_EntityStatus.revoked=已废除
kl.npki.base.core.i18n_EntityStatus.freeze=已冻结
kl.npki.base.core.i18n_EntityStatus.expired=已过期
kl.npki.base.core.i18n_EntityStatus.unknown=未知

# kl.npki.base.core.biz.inspection.InspectionItemTypeEnum
kl.npki.base.core.i18n_inspection_system_resource=系统资源巡检项
kl.npki.base.core.i18n_inspection_license_resource=系统授权巡检项
kl.npki.base.core.i18n_inspection_system_cert_resource=系统证书巡检项
kl.npki.base.core.i18n_inspection_internal_server_status=内部服务巡检项
kl.npki.base.core.i18n_inspection_external_server_status=外部服务巡检项
kl.npki.base.core.i18n_inspection_business_data=系统业务数据巡检项

# kl.npki.base.core.biz.check.SelfCheckManager
kl.npki.base.core.i18n_self_check_exception_occurred=自检过程中发生异常，请查看后台日志
kl.npki.base.core.i18n_self_check_exception_type=自检异常类型
kl.npki.base.core.i18n_self_check_exception_message=自检异常信息

# kl.npki.base.core.constant.AuditStatusEnum
kl.npki.base.core.i18n_AuditStatusEnum_no_audit=不审计
kl.npki.base.core.i18n_AuditStatusEnum_pending=待审计
kl.npki.base.core.i18n_AuditStatusEnum_success=审计成功
kl.npki.base.core.i18n_AuditStatusEnum_failed=审计失败

# kl.npki.base.core.constant.CertPurposeEnum
kl.npki.base.core.i18n_CertPurposeEnum_none=无用途
kl.npki.base.core.i18n_CertPurposeEnum_sign=签名证书
kl.npki.base.core.i18n_CertPurposeEnum_encrypt=加密证书
kl.npki.base.core.i18n_CertPurposeEnum_sign_and_encrypt=签名和加密证书

# 日期格式化配置
kl.npki.base.core.i18n_day_pattern=yyyy-MM-dd
kl.npki.base.core.i18n_date_time_pattern=yyyy-MM-dd HH:mm:ss
kl.npki.base.core.i18n_date_time_ms_pattern=yyyy-MM-dd HH:mm:ss
kl.npki.base.core.i18n_file_date_time_pattern=yyyyMMddHHmmss

# 系统日志国际化
kl.npki.base.core.i18n_OpLogInfo.traceId=链路ID
kl.npki.base.core.i18n_OpLogInfo.spanId=跨度ID
kl.npki.base.core.i18n_OpLogInfo.logWho=操作人员ID
kl.npki.base.core.i18n_OpLogInfo.username=操作人员用户名
kl.npki.base.core.i18n_OpLogInfo.logDo=操作名称
kl.npki.base.core.i18n_OpLogInfo.result=操作结果
kl.npki.base.core.i18n_OpLogInfo.logWhat=操作详情
kl.npki.base.core.i18n_OpLogInfo.auditStatus=审计状态
kl.npki.base.core.i18n_OpLogInfo.clientIp=客户端IP
kl.npki.base.core.i18n_OpLogInfo.serverIp=服务端IP
kl.npki.base.core.i18n_OpLogInfo.logWhen=操作时间
kl.npki.base.core.i18n_OpLogInfo.logEnd=结束时间
kl.npki.base.core.i18n_OpLogInfo.signData=签名值
kl.npki.base.core.i18n_OpLogInfo.request=请求
kl.npki.base.core.i18n_OpLogInfo.auditTime=审计时间
kl.npki.base.core.i18n_OpLogInfo.originDataDesensitize=签名原文
kl.npki.base.core.i18n_OpLogInfo.elapsedTime=耗时
kl.npki.base.core.i18n_OpLogInfo.fullDataHash=完整性保护摘要值

kl.npki.base.core.i18n_ApiLogInfo.traceId=链路ID
kl.npki.base.core.i18n_ApiLogInfo.spanId=跨度ID
kl.npki.base.core.i18n_ApiLogInfo.clientIp=客户端IP
kl.npki.base.core.i18n_ApiLogInfo.logWhen=何时请求
kl.npki.base.core.i18n_ApiLogInfo.bizId=业务ID
kl.npki.base.core.i18n_ApiLogInfo.biz=请求业务
kl.npki.base.core.i18n_ApiLogInfo.detail=操作详细内容
kl.npki.base.core.i18n_ApiLogInfo.result=请求结果
kl.npki.base.core.i18n_ApiLogInfo.elapsedTime=耗时
kl.npki.base.core.i18n_ApiLogInfo.callerId=调用者ID
kl.npki.base.core.i18n_ApiLogInfo.callerName=调用者名称
kl.npki.base.core.i18n_ApiLogInfo.entityId=业务用户标识
kl.npki.base.core.i18n_ApiLogInfo.certId=证书标识

#TrustCertEventEnum
kl.npki.base.core.i18n_import_trust_cert=导入证书链
kl.npki.base.core.i18n_delete_trust_cert=删除证书链