# ManagementInternalError
kl.npki.base.management.i18n_system_error=系统异常
kl.npki.base.management.i18n_db_insert_error=数据新增异常
kl.npki.base.management.i18n_db_delete_error=数据删除异常
kl.npki.base.management.i18n_db_update_error=数据修改异常
kl.npki.base.management.i18n_db_query_error=数据查询异常
kl.npki.base.management.i18n_verify_signed_error=验证签名失败
kl.npki.base.management.i18n_sign_key_not_found=签名密钥不存在
kl.npki.base.management.i18n_license_parse_error=License解析失败
kl.npki.base.management.i18n_file_download_fail=文件下载失败
kl.npki.base.management.i18n_file_create_fail=文件创建失败
kl.npki.base.management.i18n_system_cert_algorithm_not_config=系统证书密钥算法未配置
kl.npki.base.management.i18n_verify_full_data_hash_error=数据完整性验证失败
kl.npki.base.management.i18n_cert_preview_error=证书解析预览失败
kl.npki.base.management.i18n_cert_encode_error=证书ASN结构编码失败

# ManagementRemoteError
kl.npki.base.management.i18n_remote_service_error=远端服务错误

# ManagementValidationError
kl.npki.base.management.i18n_param_error=参数错误
kl.npki.base.management.i18n_biz_status_error=业务状态错误
kl.npki.base.management.i18n_admin_not_exists_error=无效的管理员
kl.npki.base.management.i18n_role_not_exists=角色不存在
kl.npki.base.management.i18n_role_not_exists_by_code=根据角色编码未找到角色
kl.npki.base.management.i18n_admin_info_error=管理员信息异常，请重新登录
kl.npki.base.management.i18n_sys_role_not_remove=系统角色不能删除
kl.npki.base.management.i18n_auth_ref_id_error=认证ID不能为空
kl.npki.base.management.i18n_invalid_auth_type=无效的认证类型
kl.npki.base.management.i18n_invalid_cookie_info=无效的cookie信息
kl.npki.base.management.i18n_ssl_login_fail=SSL登录验证失败
kl.npki.base.management.i18n_admin_username_or_pwd_error=管理员账号或密码错误
kl.npki.base.management.i18n_invalid_cert_sn=无效的证书序列号
kl.npki.base.management.i18n_admin_role_not_exists=管理员角色不存在
kl.npki.base.management.i18n_other_admin_role_inconsistent=其它管理员角色与主登录管理员角色不一致
kl.npki.base.management.i18n_login_status_expired=用户未登录或登录状态过期
kl.npki.base.management.i18n_login_status_has_been_replaced=该账号已在其他地方登录，您已被迫下线，如非本人操作请重新登录并及时修改登录信息
kl.npki.base.management.i18n_login_account_has_been_locked=账号已被锁定
kl.npki.base.management.i18n_other_admin_group_inconsistent=其它管理员与主登录管理员组不一致，备份管理和初始化管理员不属于同一组！
kl.npki.base.management.i18n_login_status_ip_error=登录信息IP地址不一致，请重新登录
kl.npki.base.management.i18n_kcsp_sso_fail=KCSP单点登录失败
kl.npki.base.management.i18n_unsupported_db_type=不支持的数据库类型
kl.npki.base.management.i18n_no_permission=没有权限
kl.npki.base.management.i18n_role_code_is_not_numeric=角色编码必须为数字
kl.npki.base.management.i18n_asym_algo_name_repeat_error=非对称算法名称重复
kl.npki.base.management.i18n_resource_code_already_exists=资源编码已存在
kl.npki.base.management.i18n_query_backup_admin_error=该角色不支持查看备份管理员列表
kl.npki.base.management.i18n_config_file_verify_hmac_failed=配置文件被篡改, 完整性校验失败！
kl.npki.base.management.i18n_demo_init_error=系统已部署
kl.npki.base.management.i18n_captcha_verify_fail=验证码错误
kl.npki.base.management.i18n_administrator_certificate_does_not_exist=管理员证书不存在
kl.npki.base.management.i18n_admin_is_cancel=管理员已被注销
kl.npki.base.management.i18n_management_root_cert_not_exist=管理根证书不存在
kl.npki.base.management.i18n_login_method_has_been_modified=登录方式已被修改
kl.npki.base.management.i18n_please_login_to_system_again=请重新登录系统
kl.npki.base.management.i18n_org_not_found=组织机构不存在
kl.npki.base.management.i18n_upload_file_name_error=无法获取文件名
kl.npki.base.management.i18n_upload_file_empty_error=上传文件内容为空
kl.npki.base.management.i18n_upload_file_not_found_error=文件不存在
kl.npki.base.management.i18n_upload_file_exist_unfinished_error=当前业务存在未完成的文件上传操作
kl.npki.base.management.i18n_upload_file_interface_not_exist_error=此业务不存在文件上传操作
kl.npki.base.management.upload_file_finished_error=上传文件已经完成处理
kl.npki.base.management.upload_file_processing_error=上传文件正在处理

# AdminMgrController
kl.npki.base.management.i18n_registration_administrator=注册管理员
kl.npki.base.management.i18n_query_the_list_of_operable_roles=查询可操作的角色列表
kl.npki.base.management.i18n_import_administrator_certificate=导入管理员证书
kl.npki.base.management.i18n_import_extended_administrator_certificate=导入延期管理员证书
kl.npki.base.management.i18n_request_to_revoke_certificate=请求废除证书
kl.npki.base.management.i18n_abolish_certificate=废除证书
kl.npki.base.management.i18n_abolish_super_administrators=废除顶级管理员
kl.npki.base.management.i18n_revocation_of_certificate_revocation_application=撤销证书废除申请
kl.npki.base.management.i18n_query_the_list_of_system_administrators=查询系统管理员列表
kl.npki.base.management.i18n_query_the_list_of_backup_administrators=查询备份管理员列表
kl.npki.base.management.i18n_system_administrator_information_details=系统管理员信息详情
kl.npki.base.management.i18n_system_administrator_certificate_sn=系统管理员证书SN
kl.npki.base.management.i18n_apply_for_cancellation_of_administrator=申请注销管理员
kl.npki.base.management.i18n_cancel_administrator=注销管理员
kl.npki.base.management.i18n_apply_for_extension_administrator=申请延期管理员
kl.npki.base.management.i18n_import_certificate_chain=导入证书链
kl.npki.base.management.i18n_during_deployment_retrieve_the_built_in_administrator_list=部署时，获取内置的管理员列表
kl.npki.base.management.i18n_change_password=修改密码
kl.npki.base.management.i18n_unlock_the_account=解除账号锁定
kl.npki.base.management.i18n_get_admin_user_status=获取管理员用户状态
kl.npki.base.management.i18n_get_admin_cert_status=获取管理员证书状态

# AuditController
kl.npki.base.management.i18n_audit_log_list_query=审计日志列表查询
kl.npki.base.management.i18n_log_audit=日志审计
kl.npki.base.management.i18n_audit_log_details_query=审计日志详情查询

# SecurityMgrController
kl.npki.base.management.i18n_pending_review_list=待审核列表
kl.npki.base.management.i18n_approved_by_review=审核通过
kl.npki.base.management.i18n_review_rejected=审核拒绝
kl.npki.base.management.i18n_view_audit_details=查看审核详情

# AuthenticateController
kl.npki.base.management.i18n_retrieve_the_original_login_signature_data=获取登录签名数据原文
kl.npki.base.management.i18n_obtain_the_required_number_of_administrators_for_login=获取登录时所需管理员数量
kl.npki.base.management.i18n_obtain_the_serial_number_of_the_administrator_signature_certificate=获取管理员签名证书序列号
kl.npki.base.management.i18n_verify_ssl_channel=验证SSL通道
kl.npki.base.management.i18n_administrator_login=管理员登录
kl.npki.base.management.i18n_log_out_of_login=注销登录
kl.npki.base.management.i18n_annotation.get_captcha=获取验证码图片Base64编码
kl.npki.base.management.i18n_sso_auth_addr=单点登录认证平台信息

# SystemBackupRestoreController
kl.npki.base.management.i18n_configure_backup=配置备份
kl.npki.base.management.i18n_configure_recovery=配置恢复

# CertController
kl.npki.base.management.i18n_certificate_preview_analysis=证书预览解析
kl.npki.base.management.i18n_parse_certificate=解析证书

# IdCertController
kl.npki.base.management.i18n_self_issued_identity_certificate=自签发身份证书
kl.npki.base.management.i18n_update_identity_certificate=更新身份证书
kl.npki.base.management.i18n_obtain_identity_certificate=获取身份证书

# SslServerCertController
kl.npki.base.management.i18n_self_issued_site_certificate=自签发站点证书
kl.npki.base.management.i18n_obtain_the_key_list_for_issuing_site_certificates=获取签发站点证书的密钥列表
kl.npki.base.management.i18n_view_detailed_information_of_server_site_certificate=查看服务器站点证书详细信息
kl.npki.base.management.i18n_view_detailed_information_of_server_id_certificate_info=查看身份证书请求信息
kl.npki.base.management.i18n_view_detailed_information_of_server_ssl_certificate_info=查看ssl证书请求信息
kl.npki.base.management.i18n_view_detailed_information_of_server_root_certificate_info=查看根证书请求信息

# TrustCertController
kl.npki.base.management.i18n_query_management_root_certificate=查询管理根证书
kl.npki.base.management.i18n_query_management_root_certificate_algorithm=查询管理根证书算法
kl.npki.base.management.i18n_self_signed_hair_root_certificate=自签发根证书
kl.npki.base.management.i18n_re_sign_the_hair_root_certificate=重新签发根证书
kl.npki.base.management.i18n_import_trusted_certificate=导入可信证书
kl.npki.base.management.i18n_delete_trusted_certificate_chain=删除可信证书链
kl.npki.base.management.i18n_import_management_root_certificate=导入管理根证书
kl.npki.base.management.i18n_update_import_management_root_certificate=更新导入管理根证书
kl.npki.base.management.i18n_generate_certificate_request=生成证书请求
kl.npki.base.management.i18n_update_certificate_request=更新证书请求
kl.npki.base.management.i18n_export_certificate_request=导出证书请求
kl.npki.base.management.i18n_export_certificate=导出证书
kl.npki.base.management.i18n_query_the_list_of_trusted_certificates=查询可信证书列表

# CacheConfigController
kl.npki.base.management.i18n_query_cache_configuration=查询缓存配置
kl.npki.base.management.i18n_save_cache_configuration=保存缓存配置
kl.npki.base.management.i18n_test_cache_connection=测试缓存连接

# CryptoAlgoConfigController
kl.npki.base.management.i18n_query_the_password_algorithm_configuration_supported_by_the_password_machine=查询密码机支持的密码算法配置
kl.npki.base.management.i18n_query_the_password_algorithm_configuration_of_the_system_configuration=查询系统配置的密码算法配置
kl.npki.base.management.i18n_modify_password_algorithm_configuration=修改密码算法配置

# DbConfigController
kl.npki.base.management.i18n_query_database_configuration=查询数据库配置
kl.npki.base.management.i18n_save_database_configuration=保存数据库配置
kl.npki.base.management.i18n_save_database_passwd_configuration=保存数据库密码配置
kl.npki.base.management.i18n_test_database_connection=测试数据库连接
kl.npki.base.management.i18n_query_configuration_status=查询配置状态

# EngineConfigController
kl.npki.base.management.i18n_query_encryption_machine_configuration=查询加密机配置
kl.npki.base.management.i18n_save_encryption_machine_configuration=保存加密机配置
kl.npki.base.management.i18n_query_the_list_of_encryption_machine_types=查询加密机类型列表
kl.npki.base.management.i18n_test_encryption_machine_connection=测试加密机连接
kl.npki.base.management.i18n_query_the_list_of_asymmetric_key_types_supported_by_the_current_configured_encryption_machine=查询当前配置加密机支持的非对称密钥类型列表
kl.npki.base.management.i18n_query_the_list_of_anti_quantum_encryption_key_types_supported_by_the_current_configured_encryption_machine=查询当前配置加密机支持的抗量子加密密钥类型列表
kl.npki.base.management.i18n_query_the_list_of_supported_asymmetric_key_types_based_on_the_encryption_machine_type=根据加密机类型查询支持的非对称密钥类型列表
kl.npki.base.management.i18n_query_key_index_list=查询密钥索引列表
kl.npki.base.management.i18n_check_if_the_configuration_is_complete=查询配置是否完成

# LogConfigController
kl.npki.base.management.i18n_query_log_configuration=查询日志配置
kl.npki.base.management.i18n_save_log_configuration=保存日志配置
kl.npki.base.management.i18n_get_log_output_type=获取日志输出类型
kl.npki.base.management.i18n_get_log_level_type_dropdown_options=获取日志级别类型下拉选项

# LoginTypeConfigController
kl.npki.base.management.i18n_save_login_mode=保存登录模式
kl.npki.base.management.i18n_get_login_mode=获取登录模式
kl.npki.base.management.i18n_get_login_mode_enumeration_list=获取登录模式枚举列表

# PortConfigController
kl.npki.base.management.i18n_get_port_configuration=获取端口配置
kl.npki.base.management.i18n_modify_port_configuration=修改端口配置

# SystemConfigController
kl.npki.base.management.i18n_obtain_system_configuration_template=获取系统配置模板
kl.npki.base.management.i18n_query_the_current_environment_of_the_system=查询系统当前环境
kl.npki.base.management.i18n_complete_system_initialization=完成系统初始化
kl.npki.base.management.i18n_demo_environment_initialization=演示环境初始化
kl.npki.base.management.i18n_query_the_overall_configuration_status_and_progress_of_the_system=查询系统整体配置状态与进度
kl.npki.base.management.i18n_query_system_general_configuration=查询系统通用配置
kl.npki.base.management.i18n_save_system_general_configuration=保存系统通用配置

# BaseHomePageController
kl.npki.base.management.i18n_service_self_inspection_statistics_results=服务自检统计结果

# LicenseController
kl.npki.base.management.i18n_import_license=导入License
kl.npki.base.management.i18n_query_license=查询License
kl.npki.base.management.i18n_parse_license=解析License

# ApiLogController
kl.npki.base.management.i18n_query_service_log_list=查询服务日志列表
kl.npki.base.management.i18n_query_service_log_caller_list=查看调用者下拉框列表
kl.npki.base.management.i18n_query_detailed_information_of_service_logs=查询服务日志详细信息
kl.npki.base.management.i18n_query_excel_electronic_signature_information=查询Excel电子签名信息
kl.npki.base.management.i18n_export_signed_file_of_service_logs_i18n_key=导出带签名日志
kl.npki.base.management.i18n_export_file_of_service_logs_i18n_key=导出日志
kl.npki.base.management.i18n_export_file_of_view_logs_i18n_key=查看日志文件
kl.npki.base.management.i18n_export_file_of_delete_logs_i18n_key=删除日志文件
kl.npki.base.management.i18n_export_file_of_download_logs_i18n_key=下载日志文件
kl.npki.base.management.i18n_init=初始化

# OpLogController
kl.npki.base.management.i18n_query_operation_log_list=查询操作日志列表
kl.npki.base.management.i18n_query_operation_log_detailed_information=查询操作日志详细信息

# RunLogController
kl.npki.base.management.i18n_view_the_list_of_running_logs=运行日志列表查看
kl.npki.base.management.i18n_view_operation_logs=运行日志查看
kl.npki.base.management.i18n_download_running_logs=运行日志下载

# RoleController
kl.npki.base.management.i18n_add_new_role=新增角色
kl.npki.base.management.i18n_disable_role=禁用角色
kl.npki.base.management.i18n_role_list_query=角色列表查询
kl.npki.base.management.i18n_initialize_role_permission_tree=初始化角色权限树
kl.npki.base.management.i18n_save_role_permission_tree=保存角色权限树
kl.npki.base.management.i18n_get_role_unified_resources=获取角色的统一资源权限
kl.npki.base.management.i18n_update_role_unified_resources=更新角色的统一资源权限

# Role Cache Related
kl.npki.base.management.i18n_role_cache_clear_success=角色缓存清理成功
kl.npki.base.management.i18n_role_cache_clear_failed=角色缓存清理失败
kl.npki.base.management.i18n_role_cache_put_success=角色缓存更新成功
kl.npki.base.management.i18n_role_cache_put_failed=角色缓存更新失败
kl.npki.base.management.i18n_role_cache_get_success=角色缓存获取成功
kl.npki.base.management.i18n_role_cache_get_failed=角色缓存获取失败
kl.npki.base.management.i18n_role_cache_not_found=角色缓存未找到
kl.npki.base.management.i18n_role_cache_expired=角色缓存已过期

# SelfCheckController
kl.npki.base.management.i18n_execute_all_service_self_check_items=执行所有服务自检项
kl.npki.base.management.i18n_execute_specified_service_self_test_items=执行指定的服务自检项
kl.npki.base.management.i18n_query_all_self_checking_data=查询所有自检数据
kl.npki.base.management.i18n_query_alarm_self_test_data=查询告警自检数据

# SysHelpController
kl.npki.base.management.i18n_obtain_operation_guidance_information=获取操作引导信息
kl.npki.base.management.i18n_modify_operation_guidance_information=修改操作引导信息

# SysInfoController
kl.npki.base.management.i18n_obtain_system_information_related_configurations=获取系统信息相关配置
kl.npki.base.management.i18n_update_system_information_configuration=更新系统信息配置
kl.npki.base.management.i18n_check_if_the_user_agrees_to_use_the_license_agreement=查询用户是否同意使用许可协议
kl.npki.base.management.i18n_the_user_agrees_to_use_the_license_agreement=用户同意使用许可协议
kl.npki.base.management.i18n_current_language_query_in_the_system=系统当前语言查询
kl.npki.base.management.i18n_the_system_supports_language_list_query=系统支持语言列表查询
kl.npki.base.management.i18n_system_language_settings=系统语言设置
kl.npki.base.management.i18n_system_date_time_settings=系统日期格式化设置

# InspectionController
kl.npki.base.management.i18n_execute_inspection=执行系统巡检
kl.npki.base.management.i18n_query_inspection_record_list=查询巡检列表
kl.npki.base.management.i18n_query_inspection_record_details=查询巡检结果详情
kl.npki.base.management.i18n_delete_inspection_record=删除巡检记录
kl.npki.base.management.i18n_export_inspection_record=导出巡检报告
kl.npki.base.management.i18n_query_inspection_item_list=查询巡检项列表项

# OrgManagementController
kl.npki.base.management.i18n_annotation.query_institution_information_list=查询机构信息列表
kl.npki.base.management.i18n_annotation.query_institution_information_tree=查询机构信息树
kl.npki.base.management.i18n_annotation.search_all_institutions=查询所有机构
kl.npki.base.management.i18n_annotation.query_the_root_institution=查询根机构
kl.npki.base.management.i18n_annotation.query_sub_institutions=查询子机构
kl.npki.base.management.i18n_annotation.query_information_details=查询信息详情
kl.npki.base.management.i18n_annotation.query_information_details_based_on_institutional_code=根据机构编码查询信息详情
kl.npki.base.management.i18n_annotation.newly_added_institutions=新增机构
kl.npki.base.management.i18n_annotation.institutional_update=机构更新
kl.npki.base.management.i18n_annotation.abolish_institutions=废除机构
kl.npki.base.management.i18n_annotation.delete_institution=删除机构
kl.npki.base.management.i18n_annotation.batch_deletion_of_institutions=批量删除机构
kl.npki.base.management.i18n_annotation.batch_export_of_institutions=批量导出机构
kl.npki.base.management.i18n_annotation.obtain_the_historical_trajectory_of_the_institution=获取机构历史轨迹

# UploadFileController
kl.npki.base.management.i18n_annotation.upload_files=上传文件
kl.npki.base.management.i18n_annotation.process_uploaded_files=处理上传的文件
kl.npki.base.management.i18n_annotation.delete_uploaded_files=删除上传文件
kl.npki.base.management.i18n_annotation.check_if_there_are_any_unfinished_files_in_the_current_interface=查询当前接口是否存在未完成的文件
kl.npki.base.management.i18n_annotation.file_upload_list=文件上传列表
kl.npki.base.management.i18n_annotation.download_source_files=下载源文件
kl.npki.base.management.i18n_annotation.download_result_file=下载结果文件
kl.npki.base.management.i18n_annotation.download_template=下载模板
kl.npki.base.management.i18n_annotation.all_support_batch_operation_interface_names=所有支持批量操作接口名称

# OrgAddRequest
kl.npki.base.management.i18n_annotation.institution_name=机构名称
kl.npki.base.management.i18n_annotation.the_length_of_the_institution_name_cannot_exceed_256=机构名称长度不能大于256字符
kl.npki.base.management.i18n_annotation.the_length_of_the_full_name_of_the_institution_cannot_exceed_256=机构全称长度不能大于256字符
kl.npki.base.management.i18n_annotation.the_pinyin_of_the_institution_name_cannot_exceed_256=机构名称拼音不能大于256字符
kl.npki.base.management.i18n_annotation.institution_code=机构编码
kl.npki.base.management.i18n_annotation.institution_code_cannot_exceed_256=机构编码不能大于256字符
kl.npki.base.management.i18n_annotation.superior_institution_code=上级机构编码
kl.npki.base.management.i18n_annotation.the_superior_organization_code_cannot_exceed_256=上级机构编码不能大于256字符
kl.npki.base.management.i18n_annotation.contact_person_cannot_exceed_256=联系人不能大于256字符
kl.npki.base.management.i18n_annotation.the_contact_phone_number_cannot_exceed_256=联系电话不能大于256字符
kl.npki.base.management.i18n_annotation.the_postal_code_cannot_exceed_256=邮政编码不能大于256字符
kl.npki.base.management.i18n_annotation.the_contact_address_cannot_exceed_256=联系地址不能大于256字符
kl.npki.base.management.i18n_annotation.institution_description_cannot_exceed_256=机构描述不能大于256字符

# OrgUpdateRequest
kl.npki.base.management.i18n_annotation.the_complete_institutional_code_cannot_exceed_256=机构完整编码不能大于256字符
kl.npki.base.management.i18n_annotation.the_institutional_level_cannot_be_less_than_0=机构级别不能小于0

# AdminUpdatePwdRequest
kl.npki.base.management.i18n_administrator_id_cannot_be_empty=管理员id不能为空
kl.npki.base.management.i18n_old_password_cannot_be_empty=旧密码不能为空
kl.npki.base.management.i18n_the_length_of_the_old_password_digest_cannot_exceed_128_characters=旧密码摘要长度不能大于128字符
kl.npki.base.management.i18n_old_password_digest_cannot_contain_spaces_20_starting_or_ending_with_0a_or_00=旧密码摘要不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_the_new_password_cannot_be_empty=新密码不能为空
kl.npki.base.management.i18n_new_password_digest_format_error=新密码摘要格式错误

# ExtendAdminCertRequest
kl.npki.base.management.i18n_the_administrator_id_should_not_be_empty=管理员id不应为空
kl.npki.base.management.i18n_the_certificate_extension_period_should_not_be_less_than_1_day=证书延期天数不应小于1天

# ImportAdminCertRequest
kl.npki.base.management.i18n_role_information_cannot_be_empty=角色信息不能为空
kl.npki.base.management.i18n_the_length_of_the_character_code_cannot_exceed_128_characters=角色编码长度不能大于128字符
kl.npki.base.management.i18n_character_encoding_cannot_use_spaces_or_20_starting_or_ending_with_0a_or_00=角色编码不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_password_information_cannot_be_empty=密码信息不能为空
kl.npki.base.management.i18n_password_hash_length_cannot_exceed_128_characters=密码hash长度不能大于128字符
kl.npki.base.management.i18n_password_hash_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=密码hash不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_the_value_of_the_signature_certificate_cannot_be_empty=签名证书值不能为空
kl.npki.base.management.i18n_the_length_of_the_signature_certificate_value_cannot_exceed_4096_characters=签名证书值长度不能大于4096字符
kl.npki.base.management.i18n_the_certificate_value_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=证书值不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_the_length_of_the_encryption_certificate_value_cannot_exceed_4096_characters=加密证书值长度不能大于4096字符
kl.npki.base.management.i18n_the_encryption_certificate_value_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=加密证书值不能以空格、%20、%0a、%00开头或结尾

# ImportExtendAdminCertRequest

# IssueAdminCertForDeployRequest
kl.npki.base.management.i18n_certificate_issuance_request_should_not_be_empty=证书签发请求不应为空
kl.npki.base.management.i18n_the_password_for_pfx_cannot_be_empty=pfx的密码不能为空
kl.npki.base.management.i18n_the_password_for_pfx_format_error=pfx密码需为8至30位字符，必须同时包含大小写字母、数字及特殊字符

# IssueAdminCertRequest
kl.npki.base.management.i18n_user_id_should_not_be_empty=用户id不应为空

# LoginRequest
kl.npki.base.management.i18n_authentication_type_cannot_be_empty=认证类型不能为空
kl.npki.base.management.i18n_the_authentication_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=认证类型不能以空格、%20、%0a、%00开头或结尾

# RegisterAdminRequest
kl.npki.base.management.i18n_role_information_should_not_be_empty=角色信息不应为空
kl.npki.base.management.i18n_the_length_of_character_information_cannot_exceed_128_characters=角色信息长度不能大于128字符
kl.npki.base.management.i18n_administrator_name_should_not_be_empty=管理员姓名不应为空
kl.npki.base.management.i18n_the_length_of_the_administrator_name_cannot_exceed_128_characters=管理员姓名长度不能大于128字符
kl.npki.base.management.i18n_the_name_of_the_corner_administrator_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=角管理员姓名不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_password_cannot_be_empty=密码不能为空
kl.npki.base.management.i18n_password_digest_format_error=密码摘要格式错误
kl.npki.base.management.i18n_the_data_length_of_organizational_information_cannot_exceed_128_characters=组织信息的数据长度不能大于128字符
kl.npki.base.management.i18n_the_data_length_of_institutional_information_cannot_exceed_128_characters=机构信息的数据长度不能大于128字符
kl.npki.base.management.i18n_the_data_length_of_province_information_cannot_exceed_128_characters=省份信息的数据长度不能大于128字符
kl.npki.base.management.i18n_the_data_length_of_city_information_cannot_exceed_128_characters=城市信息的数据长度不能大于128字符
kl.npki.base.management.i18n_email_error=邮箱错误

kl.npki.base.management.i18n_parsing_type_accept_values=解析类型的值只允许为pubKey

# UpdateLoginTypeRequest
kl.npki.base.management.i18n_login_mode_cannot_be_empty=登录模式不能为空

# AlgoConfigRequest
kl.npki.base.management.i18n_symmetric_key_algorithm_cannot_be_empty=对称密钥算法不能为空
kl.npki.base.management.i18n_symmetric_key_algorithm_cannot_exceed_128_characters=对称密钥算法不能大于128字符
kl.npki.base.management.i18n_symmetric_key_algorithm_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=对称密钥算法不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_system_cert_algorithm_cannot_be_empty=系统证书算法不能为空
kl.npki.base.management.i18n_system_cert_algorithm_cannot_exceed_128_characters=系统证书算法不能大于128字符

# CacheConfigRequest
kl.npki.base.management.i18n_cache_type_cannot_be_empty=缓存类型不能为空
kl.npki.base.management.i18n_the_length_of_the_database_type_cannot_exceed_20_characters=数据库类型长度不能大于20字符
kl.npki.base.management.i18n_cache_operation_mode_cannot_be_empty=缓存运行模式不能为空
kl.npki.base.management.i18n_cache_operation_mode_cannot_exceed_20_characters=缓存运行模式不能大于20字符
kl.npki.base.management.i18n_cache_operation_mode_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=缓存运行模式不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_ssl_switch_cannot_be_empty=SSL开关不能为空
kl.npki.base.management.i18n_redis_node_configuration_cannot_be_empty=redis节点配置不能为空

# CertSignRequest
kl.npki.base.management.i18n_the_certificate_universal_name_cannot_be_empty=证书通用名不能为空！
kl.npki.base.management.i18n_the_length_of_the_certificate_generic_name_cannot_exceed_32_characters=证书通用名长度不能大于32字符
kl.npki.base.management.i18n_the_certificate_common_name=证书通用名
kl.npki.base.management.i18n_the_data_length_of_unit_information_cannot_exceed_128_characters=单位信息的数据长度不能大于128字符
kl.npki.base.management.i18n_the_validity_period_of_the_certificate_cannot_be_empty=证书有效天数不能为空
kl.npki.base.management.i18n_the_length_of_the_key_type_cannot_exceed_128_characters=密钥类型长度不能大于128字符
kl.npki.base.management.i18n_the_key_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=密钥类型不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_the_encryption_key_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=加密密钥类型不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_the_keystore_password_cannot_be_greater_than_128_characters=keyStore密码不能大于128字符
kl.npki.base.management.i18n_the_unit_name=组织名称
kl.npki.base.management.i18n_the_province_name=省份名称
kl.npki.base.management.i18n_the_city_name=市名称
kl.npki.base.management.i18n_the_organization_name=机构名称
kl.npki.base.management.i18n_the_admin_name=管理员名称

# ExportCertRequest
kl.npki.base.management.i18n_the_certificate_export_format=证书导出格式只能是DER、PEM、P7B_NO_CHAIN、P7B_WITH_CHAIN

# ExportKeyRequest
kl.npki.base.management.i18n_the_key_export_format=私钥导出格式只能是PFX、GMT0009_2012、GMT0009_2023、GMT0016_2023、P8

# ImportCertChainRequest
kl.npki.base.management.i18n_certificate_cannot_be_empty=证书不能为空

# ImportTrustCertRequest
kl.npki.base.management.i18n_trusted_certificate_cannot_be_empty=可信证书不能为空

# DbConfigRequest
kl.npki.base.management.i18n_the_database_type_cannot_be_empty=数据库类型不能为空
kl.npki.base.management.i18n_the_length_of_the_database_type_cannot_exceed_128_characters=数据库类型长度不能大于128字符
kl.npki.base.management.i18n_the_database_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=数据库类型不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_the_maximum_number_of_connections_cannot_be_empty=最大连接数不能为空
kl.npki.base.management.i18n_whether_to_enable_readwrite_separation_configuration_cannot_be_empty=是否启用读写分离配置不能为空

# DbConfigResponse

# AsymKeyIndexInfo
kl.npki.base.management.i18n_asymmetric_algorithm_name_cannot_be_empty=非对称算法名称不能为空

# EngineConfigRequest
kl.npki.base.management.i18n_the_encryption_machine_type_cannot_be_empty=加密机类型不能为空
kl.npki.base.management.i18n_the_length_of_the_encryption_machine_type_cannot_exceed_128_characters=加密机类型长度不能大于128字符
kl.npki.base.management.i18n_encryption_machine_type_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=加密机类型不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_asymmetric_key_index_information_cannot_be_empty=非对称密钥索引信息不能为空

# GroupEngineConfigRequest
kl.npki.base.management.i18n_the_configuration_of_encryption_machines_within_the_group_cannot_be_empty=组内加密机配置不能为空

# LogConfigRequest
kl.npki.base.management.i18n_the_system_log_level_cannot_be_empty=系统日志级别不能为空
kl.npki.base.management.i18n_the_length_of_the_system_log_level_cannot_exceed_128_characters=系统日志级别长度不能大于128字符
kl.npki.base.management.i18n_the_system_log_level_cannot_be_filled_with_spaces_or_20_starting_or_ending_with_0a_or_00=系统日志级别不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_the_maximum_capacity_of_system_logs_cannot_be_empty=系统日志最大容量不能为空
kl.npki.base.management.i18n_the_maximum_length_of_the_log_capacity_value_cannot_exceed_32_characters=日志最大容量值长度不能大于32字符
kl.npki.base.management.i18n_the_retention_period_of_logs_cannot_be_less_than_1_day=日志保留天数不能小于1
kl.npki.base.management.i18n_the_retention_period_of_logs_cannot_exceed_1000_days=日志保留天数不能大于1000
kl.npki.base.management.i18n_whether_the_audit_service_is_enabled_cannot_be_empty=审计服务是否启用不可为空
kl.npki.base.management.i18n_audit_service_ip_address_is_out_of_range_or_formatted_incorrectly=审计服务IP地址超出范围或格式有误
kl.npki.base.management.i18n_audit_service_port_number_is_out_of_range_or_formatted_incorrectly=审计服务端口号超出范围或格式有误
kl.npki.base.management.i18n_audit_service_protocol_is_incorrect=审计服务协议输入有误

# AddRoleRequest
kl.npki.base.management.i18n_the_role_name_cannot_be_empty=角色名称不能为空
kl.npki.base.management.i18n_the_length_of_the_character_name_cannot_exceed_128_characters=角色名称长度不能大于128字符
kl.npki.base.management.i18n_the_role_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=角色名称不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_role_code_cannot_be_empty=角色编码不能为空
kl.npki.base.management.i18n_the_length_of_the_role_note_cannot_exceed_128_characters=角色备注长度不能大于128字符
kl.npki.base.management.i18n_parent_role_code_cannot_be_empty=父角色编码不能为空
kl.npki.base.management.i18n_the_length_of_the_parent_role_code_cannot_exceed_128_characters=父角色编码长度不能大于128字符
kl.npki.base.management.i18n_parent_role_code_cannot_use_spaces_or_20_starting_or_ending_with_0a_or_00=父角色编码不能以空格、%20、%0a、%00开头或结尾

# PermissionTreeSaveRequest
kl.npki.base.management.i18n_the_role_name_should_not_be_empty=角色名不应为空

# PortConfigRequest
kl.npki.base.management.i18n_the_management_service_port_number_cannot_be_less_than_1=管理服务端口号不可小于1
kl.npki.base.management.i18n_the_management_service_port_number_cannot_exceed_65535=管理服务端口号不可大于65535
kl.npki.base.management.i18n_the_management_service_ssl_switch_cannot_be_empty=管理服务SSL开关不可为空
kl.npki.base.management.i18n_the_https_management_service_port_number_cannot_be_less_than_1=HTTPS管理服务端口号不可小于1
kl.npki.base.management.i18n_the_https_management_service_port_number_cannot_exceed_65535=HTTPS管理服务端口号不可大于65535
kl.npki.base.management.i18n_the_http_business_service_switch_cannot_be_empty=HTTP业务服务开关不可为空
kl.npki.base.management.i18n_the_http_business_service_port_number_cannot_be_less_than_1=HTTP业务服务端口号不可小于1
kl.npki.base.management.i18n_the_http_business_service_port_number_cannot_exceed_65535=HTTP业务服务端口号不可大于65535
kl.npki.base.management.i18n_the_ssl_switch_for_http_business_services_cannot_be_empty=HTTP业务服务SSL开关不可为空
kl.npki.base.management.i18n_tcp_service_switch_cannot_be_empty=TCP业务服务开关不可为空
kl.npki.base.management.i18n_the_tcp_service_port_number_cannot_be_less_than_1=TCP业务服务端口号不可小于1
kl.npki.base.management.i18n_the_tcp_service_port_number_cannot_exceed_65535=TCP业务服务端口号不可大于65535
kl.npki.base.management.i18n_tcp_business_service_ssl_switch_cannot_be_empty=TCP业务服务SSL开关不可为空

# SysManagerRequest
kl.npki.base.management.i18n_the_system_name_cannot_be_empty=系统名称不能为空
kl.npki.base.management.i18n_the_length_of_the_system_name_cannot_exceed_128_characters=系统名称长度不能大于128字符
kl.npki.base.management.i18n_the_system_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=系统名称不能以空格、%20、%0a、%00开头或结尾
kl.npki.base.management.i18n_the_system_logo_format_is_incorrect=系统LOGO格式有误

# RegionAndLanguageRequest
kl.npki.base.management.i18n_the_region_cannot_be_empty=区域不能为空
kl.npki.base.management.i18n_the_region_param_error=区域参数有误
kl.npki.base.management.i18n_the_language_cannot_be_empty=语言不能为空
kl.npki.base.management.i18n_the_language_param_error=语言参数有误

# PortConfigResponse
kl.npki.base.management.i18n_the_management_service_port_number_cannot_be_less_than_0=管理服务端口号不可小于0
kl.npki.base.management.i18n_the_https_management_service_port_number_cannot_be_less_than_0=HTTPS管理服务端口号不可小于0
kl.npki.base.management.i18n_the_http_business_service_port_number_cannot_be_less_than_0=HTTP业务服务端口号不可小于0
kl.npki.base.management.i18n_the_tcp_service_port_number_cannot_be_less_than_0=TCP业务服务端口号不可小于0

# SysConfigRequest
kl.npki.base.management.i18n_login_token_lifecycle_range_message=登录Token有效期范围：10000至86400000毫秒
kl.npki.base.management.i18n_log_file_limit_size_range_message=历史文件最大数量范围：1至100
kl.npki.base.management.i18n_log_file_excel_limit_size_range_message=单个文件最大行数范围：10至1000000
kl.npki.base.management.i18n_transaction_timeout_seconds_range_message=数据库事务超时时间范围：-1至86400秒

# Enum
kl.npki.base.management.i18n_not_deployed=未部署
kl.npki.base.management.i18n_deployed=已部署

# ServiceStatusEnum
kl.npki.base.management.i18n_start=服务启动
kl.npki.base.management.i18n_stop=服务关闭

# 异常描述
kl.npki.base.management.i18n_could_not_create_keystore_directory=创建Keystore目录失败{0}
kl.npki.base.management.i18n_administrator_certificate_information_integrity_verification_failed=管理员证书信息完整性校验失败,用户名：{0}
kl.npki.base.management.i18n_character_information_integrity_verification_failed=角色信息完整性校验失败,角色名：{0}
kl.npki.base.management.i18n_the_session_id_does_not_exist=会话ID不存在
kl.npki.base.management.i18n_remote_call_to_kcsp_user_information_interface_authentication_failed=远程调用KCSP用户信息接口认证失败,错误详情: {0}; HTTP 状态码: {1}; 响应体: {2}
kl.npki.base.management.i18n_unknown_error_occurred_while_processing_kcsp_user_authentication=处理KCSP用户认证时发生未知错误,错误详情: {0}
kl.npki.base.management.i18n_the_user_status_is_abnormal_please_contact_the_administrator=用户状态异常，请联系管理员
kl.npki.base.management.i18n_failed_to_obtain_valid_user_information=获取有效用户信息失败
kl.npki.base.management.i18n_no_access_token_provided_user_not_logged_in_or_session_expired=未提供AccessToken，用户未登录或会话已过期
kl.npki.base.management.i18n_the_file_size_exceeds_the_limit=文件大小超出限制:{0}
kl.npki.base.management.i18n_file_is_not_a_dat_file=文件不是dat文件:{0}
kl.npki.base.management.i18n_license_file_read_exception=读取授权文件异常
kl.npki.base.management.i18n_no_permission_to_register_as_an_administrator_for_this_type=没有注册该类型管理员的权限
kl.npki.base.management.i18n_no_permission_to_import_this_type_of_administrator=没有导入该类型管理员的权限
kl.npki.base.management.i18n_no_permission_to_request_revocation_of_this_type_of_administrator_certificate=没有请求废除该类型管理员证书的权限
kl.npki.base.management.i18n_do_not_allow_administrators_to_abolish_themselves=不容许管理员废除自己
kl.npki.base.management.i18n_do_not_have_the_authority_to_revoke_the_administrator_certificate_revocation_application_for_this_type=没有撤销该类型管理员证书废除申请的权限
kl.npki.base.management.i18n_do_not_have_permission_to_log_out_of_this_type_of_administrator=没有注销该类型管理员的权限
kl.npki.base.management.i18n_no_extension_of_administrator_privileges_for_this_type=没有延期该类型管理员的权限
kl.npki.base.management.i18n_database_configuration_information_cannot_be_empty=数据库配置信息不能为空
kl.npki.base.management.i18n_h2_database_configuration_is_not_allowed=不允许配置H2数据库
kl.npki.base.management.i18n_data_is_empty=数据为空
kl.npki.base.management.i18n_please_assign_file_must_not_be_empty_or_null=请指定文件，不能为空或null
kl.npki.base.management.i18n_invalid_log_path=无效的日志文件名:{0}
kl.npki.base.management.i18n_file_download_failed=文件[{0}]下载失败
kl.npki.base.management.i18n_the_role_not_exist=角色不存在
kl.npki.base.management.i18n_inconsistent_identity_credentials_for_multi_factor_authentication=多因子认证身份认证信息{0}和{1}不一致
kl.npki.base.management.i18n_port_used_error=端口已被使用

# SQL中的资源国际化
kl.npki.base.management.i18n_home_page=首页
kl.npki.base.management.i18n_system_management=系统管理
kl.npki.base.management.i18n_system_configuration=系统配置
kl.npki.base.management.i18n_system_information=系统信息
kl.npki.base.management.i18n_system_logs=系统日志
kl.npki.base.management.i18n_admin_management=管理员管理
kl.npki.base.management.i18n_personnel_management=人员管理
kl.npki.base.management.i18n_import_admin_certificate=导入管理员证书
kl.npki.base.management.i18n_audit_management=审计管理
kl.npki.base.management.i18n_review_management=审核管理
kl.npki.base.management.i18n_role_management=角色管理
kl.npki.base.management.i18n_system_operation=系统维护
kl.npki.base.management.i18n_backup_admin=备份管理员
kl.npki.base.management.i18n_system_self_check=系统自检
kl.npki.base.management.i18n_super_admin_remark=负责管理业务管理员的生命周期
kl.npki.base.management.i18n_security_admin_remark=负责审批管理员生命周期相关的操作、为各类管理员分配操作权限
kl.npki.base.management.i18n_audit_admin_remark=负责管理审计员的生命周期
kl.npki.base.management.i18n_biz_admin_remark=负责管理业务操作员的生命周期
kl.npki.base.management.i18n_audit_oper_remark=负责查询系统安全相关日志并对日志进行审计
kl.npki.base.management.i18n_biz_oper_remark=负责配置系统参数、执行业务操作、查看系统日志等
kl.npki.base.management.i18n_law_admin_remark=可根据法规将用户的私钥恢复到司法专用载体中
kl.npki.base.management.i18n_deploy_operator_remark=负责部署系统
kl.npki.base.management.i18n_system_license=系统许可
kl.npki.base.management.i18n_system_inspection=系统巡检
kl.npki.base.management.i18n_advanced_settings=高级配置
kl.npki.base.management.i18n_org_management=机构管理
kl.npki.base.management.i18n_org_detail=机构详情
kl.npki.base.management.i18n_org_batch_import=机构批量导入
kl.npki.base.management.i18n_resource_mgmt=权限管理

# ApiLogExportRequest
kl.npki.base.management.i18n_signer_certificate_chain_cannot_be_empty=签名者证书链不能为空
kl.npki.base.management.i18n_list_of_fields_specified_for_export_cannot_be_empty=指定用于导出的字段列表不能为空

# LogExcelFileRequest
kl.npki.base.management.i18n_signature_time_cannot_be_empty=签名时间不能为空
kl.npki.base.management.i18n_file_id_cannot_be_empty=文件ID不能为空
kl.npki.base.management.i18n_signature_cannot_be_empty=签名不能为空

# RegionAndLanguageConfigController
kl.npki.base.management.i18n_RegionAndLanguage_SAVE_REGION_LANGUAGE=保存区域与语言
kl.npki.base.management.i18n_RegionAndLanguage_GET_REGION_LANGUAGE=获取区域与语言
kl.npki.base.management.i18n_RegionAndLanguage_LIST_REGION_LANGUAGE=获取区域与语言枚举列表

# UtilController
kl.npki.base.management.i18n_annotation.get_env_value=获取配置信息
kl.npki.base.management.i18n_annotation.pinyin_conversion=拼音转化
kl.npki.base.management.i18n_annotation.set_language=设置语言

# BaseConstantController
kl.npki.base.management.i18n_annotation.constant_asym_algo=查询非对称算法常量
kl.npki.base.management.i18n_annotation.constant_cert_status=查询证书状态常量
kl.npki.base.management.i18n_annotation.constant_user_status=查询用户状态常量
kl.npki.base.management.i18n_annotation.constant_ca_level=查询CA等级常量
kl.npki.base.management.i18n_annotation.constant_entity_status=查询实体状态常量
kl.npki.base.management.i18n_annotation.constant_http_method=查询HTTP请求类型列表
kl.npki.base.management.i18n_annotation.constant_resource_types=查询统一资源类型常量

# API资源权限管理
kl.npki.base.management.i18n_query_api_resource_permissions_tree=查询API资源权限列表（树形结构）
kl.npki.base.management.i18n_query_api_resource_permission_details=查询API资源权限详情
kl.npki.base.management.i18n_create_api_resource_permission=新增API资源权限
kl.npki.base.management.i18n_update_api_resource_permission=更新API资源权限
kl.npki.base.management.i18n_delete_api_resource_permission=删除API资源权限
kl.npki.base.management.i18n_list_api_resources=查询API资源权限列表
kl.npki.base.management.i18n_create_api_resource=新增API资源
kl.npki.base.management.i18n_update_api_resource=更新API资源
kl.npki.base.management.i18n_delete_api_resource=删除API资源
kl.npki.base.management.i18n_get_role_api_resources=获取角色的API资源权限
kl.npki.base.management.i18n_update_role_api_resources=更新角色的API资源权限

# API资源权限参数校验
kl.npki.base.management.i18n_api_resource_name_should_not_be_empty=API资源名称不能为空
kl.npki.base.management.i18n_url_should_not_be_empty=URL不能为空
kl.npki.base.management.i18n_request_method_should_not_be_empty=请求方法不能为空

# KcspSvcSyncController
kl.npki.base.management.i18n_annotation.kcsp_sync_service=KCSP平台服务同步（新增、更新、删除平台公共服务）

# 统一资源管理
kl.npki.base.management.i18n_query_unified_resources_tree=查询统一资源列表（树形结构）
kl.npki.base.management.i18n_create_unified_resource=新增统一资源
kl.npki.base.management.i18n_update_unified_resource=更新统一资源
kl.npki.base.management.i18n_delete_unified_resource=删除统一资源
kl.npki.base.management.i18n_batch_delete_unified_resources=批量删除统一资源

# 管理员信息异常描述
kl.npki.base.management.i18n_admin_id_error=管理员ID “{0}” 不存在
kl.npki.base.management.i18n_admin_name_error=管理员名称 “{0}” 不存在
kl.npki.base.management.i18n_admin_name_id_no_match_error=管理员ID “{0}” 和管理员名称 “{1}” 不匹配

# 统一资源参数校验
kl.npki.base.management.i18n_resource_name_should_not_be_empty=资源名称不能为空
kl.npki.base.management.i18n_resource_name_length_exceed_limit=资源名称长度不能超过255字符
kl.npki.base.management.i18n_resource_code_should_not_be_empty=资源编码不能为空
kl.npki.base.management.i18n_resource_code_length_exceed_limit=资源编码长度不能超过255字符
kl.npki.base.management.i18n_resource_type_should_not_be_empty=资源类型不能为空
kl.npki.base.management.i18n_menu_icon_length_exceed_limit=菜单图标长度不能超过255字符
kl.npki.base.management.i18n_page_path_length_exceed_limit=页面路径长度不能超过500字符
kl.npki.base.management.i18n_url_length_exceed_limit=URL长度不能超过500字符
kl.npki.base.management.i18n_request_method_length_exceed_limit=请求方法长度不能超过20字符
kl.npki.base.management.i18n_remark_length_exceed_limit=备注长度不能超过500字符
kl.npki.base.management.i18n_resource_code_cannot_equals_parent_code=资源编码不能等于父级资源编码
kl.npki.base.management.i18n_role_code_cannot_equals_parent_code=角色编码不能等于父级角色编码

# 资源管理相关错误信息
kl.npki.base.management.i18n_resource_has_child_resources_cannot_delete=该资源下存在子资源，无法删除
kl.npki.base.management.i18n_batch_delete_has_child_resources_not_selected=无法删除，所选资源下存在未被一同删除的子资源