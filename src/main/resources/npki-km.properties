# key
kl.km.key.cacheCheckInterval = 60
kl.km.key.cacheSize = 100
kl.km.key.cacheAutoSupply = true
kl.km.key.cacheSupplyThreshold = 0.5
kl.km.key.keyUniquenessCheck = false
kl.km.key.keyLimits[0].enable = true
kl.km.key.keyLimits[0].keyType = RSA_2048
kl.km.key.keyLimits[0].limit = 1000
kl.km.key.keyLimits[0].keyGenCronExpression = 0 0 0/1 * * ?
kl.km.key.keyLimits[1].enable = true
kl.km.key.keyLimits[1].keyType = SM2
kl.km.key.keyLimits[1].limit = 1000
kl.km.key.keyLimits[1].keyGenCronExpression = 0 0 0/1 * * ?

# archive
kl.km.archive.enable = true
kl.km.archive.lateArchiveTime = 365
kl.km.archive.archivePageThreshold = 200
kl.km.archive.archiveCronExpression = 0 0 0 * * ?

# statistic
kl.km.statistic.statisticResultGenCronExpression=0 0 0/1 * * ?
kl.km.statistic.enable=true

# kmConfig
kl.km.conf.signResponse= true
kl.km.conf.checkSnExist= true
kl.km.conf.restoreCA= true
kl.km.conf.sksKeyValidityYears= 2
kl.km.conf.reduceRealTime= true
kl.km.conf.reduceExecuteInterval= 15
kl.km.conf.verifyRequest= true
kl.km.conf.testCenter= false
kl.km.conf.sksKeyCacheEnable = false
kl.km.conf.sksKeyCacheTimeToLive = 3600000
kl.km.conf.enableCaFastApi=true

