-- 共有资源数据脚本

-- 管理员角色表
INSERT INTO t_role (id, tenant_id, role_name, role_name_i18n_key, role_code, parent_role_code, remark, is_custom, is_delete) VALUES (1, null, '超级管理员', 'kl.npki.management.core.i18n_super_admin', '1', null, 'kl.npki.base.management.i18n_super_admin_remark', 0, 0);
INSERT INTO t_role (id, tenant_id, role_name, role_name_i18n_key, role_code, parent_role_code, remark, is_custom, is_delete) VALUES (2, null, '安全管理员', 'kl.npki.management.core.i18n_security_admin', '2', null, 'kl.npki.base.management.i18n_security_admin_remark', 0, 0);
INSERT INTO t_role (id, tenant_id, role_name, role_name_i18n_key, role_code, parent_role_code, remark, is_custom, is_delete) VALUES (3, null, '审计管理员', 'kl.npki.management.core.i18n_audit_admin', '4', null, 'kl.npki.base.management.i18n_audit_admin_remark', 0, 0);
INSERT INTO t_role (id, tenant_id, role_name, role_name_i18n_key, role_code, parent_role_code, remark, is_custom, is_delete) VALUES (4, null, '业务管理员', 'kl.npki.management.core.i18n_biz_admin', '8', '1', 'kl.npki.base.management.i18n_biz_admin_remark', 0, 0);
INSERT INTO t_role (id, tenant_id, role_name, role_name_i18n_key, role_code, parent_role_code, remark, is_custom, is_delete) VALUES (5, null, '审计员', 'kl.npki.management.core.i18n_audit_oper', '16', '4', 'kl.npki.base.management.i18n_audit_oper_remark', 0, 0);
INSERT INTO t_role (id, tenant_id, role_name, role_name_i18n_key, role_code, parent_role_code, remark, is_custom, is_delete) VALUES (6, null, '业务操作员', 'kl.npki.management.core.i18n_biz_oper', '32', '8', 'kl.npki.base.management.i18n_biz_oper_remark', 0, 0);


-- 资源表
-- base的资源id范围为 1~100
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (1, '首页', 'kl.npki.base.management.i18n_home_page', '000', 'HOME', null, 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (2, '系统管理', 'kl.npki.base.management.i18n_system_management', '100', 'SYSTEM_MANAGE', null, 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (3, '系统配置', 'kl.npki.base.management.i18n_system_configuration', '100_100', 'SYSTEM_MANAGE_SYSCONFIG', '100', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (4, '系统信息', 'kl.npki.base.management.i18n_system_information', '100_200', 'SYSTEM_MANAGE_INFORMATION', '100', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (5, '系统日志', 'kl.npki.base.management.i18n_system_logs', '100_400', 'SYSTEM_MANAGE_SYSLOG', '100', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (7, '管理员管理', 'kl.npki.base.management.i18n_admin_management', '200', 'ADMIN_MANAGE', null, 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (8, '人员管理', 'kl.npki.base.management.i18n_personnel_management', '200_100', 'ADMIN_MANAGE_LIST', '200', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (10, '审计管理', 'kl.npki.base.management.i18n_audit_management', '300', 'ADMIN_MANAGE_AUDIT', null, 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (11, '审核管理', 'kl.npki.base.management.i18n_review_management', '400', 'ADMIN_MANAGE_CHECK', null, 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (12, '角色管理', 'kl.npki.base.management.i18n_role_management', '500', 'ADMIN_MANAGE_ROLE', null, 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (13, '系统维护', 'kl.npki.base.management.i18n_system_operation', '100_600', 'SYSTEM_BACKUP_RESTORE', '100', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (14, '备份管理员', 'kl.npki.base.management.i18n_backup_admin', '200_200', 'ADMIN_BACKUP', '200', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (15, '系统自检', 'kl.npki.base.management.i18n_system_self_check', '100_1000', 'SYSTEM_SELF_CHECK', '100', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (16, '系统许可', 'kl.npki.base.management.i18n_system_license', '100_500', 'SYSTEM_MANAGE_LICENSE', '100', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (17, '系统巡检', 'kl.npki.base.management.i18n_system_inspection', '100_2100', 'SYSTEM_INSPECTION', '100', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (18, '高级配置', 'kl.npki.base.management.i18n_advanced_settings', '100_900', 'ADVANCED_SETTINGS', '100', 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (170, '权限管理', 'kl.npki.base.management.i18n_resource_mgmt', '170', 'SYSTEM_MANAGE_RESOURCE_MANAGE', null, 2, null, null, 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210950, '更新统一资源', 'kl.npki.base.management.i18n_update_unified_resource', '402567276064210950', null, '170', 4, '/unifiedResource/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210951, '删除统一资源', 'kl.npki.base.management.i18n_delete_unified_resource', '402567276064210951', null, '170', 4, '/unifiedResource/*', 'DELETE', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210952, '重新签发根证书', 'kl.npki.base.management.i18n_re_sign_the_hair_root_certificate', '402567276064210952', null, '100_100', 4, '/trustCert/reissue/manage', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210953, '生成证书请求', 'kl.npki.base.management.i18n_generate_certificate_request', '402567276064210953', null, '100_100', 4, '/trustCert/p10', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210954, '更新证书请求', 'kl.npki.base.management.i18n_update_certificate_request', '402567276064210954', null, '100_100', 4, '/trustCert/p10', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210955, '导入管理根证书', 'kl.npki.base.management.i18n_import_management_root_certificate', '402567276064210955', null, '100_100', 4, '/trustCert/import/manage', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210956, '更新导入管理根证书', 'kl.npki.base.management.i18n_update_import_management_root_certificate', '402567276064210956', null, '100_100', 4, '/trustCert/import/manage', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210957, '获取端口配置', 'kl.npki.base.management.i18n_get_port_configuration', '402567276064210957', null, '100_100', 4, '/sysConfig/port', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210958, '修改端口配置', 'kl.npki.base.management.i18n_modify_port_configuration', '402567276064210958', null, '100_100', 4, '/sysConfig/port', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210959, '审核通过', 'kl.npki.base.management.i18n_approved_by_review', '402567276064210959', null, '400', 4, '/security/checkPass/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210960, '审核拒绝', 'kl.npki.base.management.i18n_review_rejected', '402567276064210960', null, '400', 4, '/security/checkFail/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210964, '执行系统巡检', 'kl.npki.base.management.i18n_execute_inspection', '402567276064210964', null, '100_2100', 4, '/inspection/execute', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210976, '查询系统配置的密码算法配置', 'kl.npki.base.management.i18n_query_the_password_algorithm_configuration_of_the_system_configuration', '402567276064210976', null, '100_100', 4, '/algoConfig/systemAlgorithms', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210977, '修改密码算法配置', 'kl.npki.base.management.i18n_modify_password_algorithm_configuration', '402567276064210977', null, '100_100', 4, '/algoConfig/systemAlgorithms', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210978, '解除账号锁定', 'kl.npki.base.management.i18n_unlock_the_account', '402567276064210978', null, '200_100', 4, '/admin/unlock/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210979, '废除证书', 'kl.npki.base.management.i18n_abolish_certificate', '402567276064210979', null, '200_100', 4, '/admin/revokeCert/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210980, '请求废除证书', 'kl.npki.base.management.i18n_request_to_revoke_certificate', '402567276064210980', null, '200_100', 4, '/admin/requestRevokeCert/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210981, '申请延期管理员', 'kl.npki.base.management.i18n_apply_for_extension_administrator', '402567276064210981', null, '200_100', 4, '/admin/requestExtendAdmin/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210982, '申请注销管理员', 'kl.npki.base.management.i18n_apply_for_cancellation_of_administrator', '402567276064210982', null, '200_100', 4, '/admin/requestCancelAdmin/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210983, '废除顶级管理员', 'kl.npki.base.management.i18n_abolish_super_administrators', '402567276064210983', null, '100_100', 4, '/admin/cancelSuper/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210984, '撤销证书废除申请', 'kl.npki.base.management.i18n_revocation_of_certificate_revocation_application', '402567276064210984', null, '200_100', 4, '/admin/cancelCertRevoke/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210985, '验证SSL通道', 'kl.npki.base.management.i18n_verify_ssl_channel', '402567276064210985', null, null, 4, '/verifySSl', 'POST', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210989, '新增统一资源', 'kl.npki.base.management.i18n_create_unified_resource', '402567276064210989', null, '170', 4, '/unifiedResource', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210990, '自签发根证书', 'kl.npki.base.management.i18n_self_signed_hair_root_certificate', '402567276064210990', null, '100_100', 4, '/trustCert/issue/manage', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210991, '导入可信证书', 'kl.npki.base.management.i18n_import_trusted_certificate', '402567276064210991', null, '100_100', 4, '/trustCert/import/trust', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210992, '导出证书', 'kl.npki.base.management.i18n_export_certificate', '402567276064210992', null, '100_100', 4, '/trustCert/export/cert', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210993, '配置恢复', 'kl.npki.base.management.i18n_configure_recovery', '402567276064210993', null, '100_600', 4, '/system/restore', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210994, '配置备份', 'kl.npki.base.management.i18n_configure_backup', '402567276064210994', null, '100_600', 4, '/system/backup', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210995, '更新系统信息配置', 'kl.npki.base.management.i18n_update_system_information_configuration', '402567276064210995', null, '100_200', 4, '/sysInfo/update', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210996, '查询用户是否同意使用许可协议', 'kl.npki.base.management.i18n_check_if_the_user_agrees_to_use_the_license_agreement', '402567276064210996', null, null, 4, '/sysInfo/license/agreement', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210997, '用户同意使用许可协议', 'kl.npki.base.management.i18n_the_user_agrees_to_use_the_license_agreement', '402567276064210997', null, null, 4, '/sysInfo/license/agreement', 'POST', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210998, '获取操作引导信息', 'kl.npki.base.management.i18n_obtain_operation_guidance_information', '402567276064210998', null, null, 4, '/sysHelp/*', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064210999, '修改操作引导信息', 'kl.npki.base.management.i18n_modify_operation_guidance_information', '402567276064210999', null, null, 4, '/sysHelp/*', 'POST', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211000, '保存系统通用配置', 'kl.npki.base.management.i18n_save_system_general_configuration', '402567276064211000', null, '100_900', 4, '/sysConfig/save', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211001, '演示环境初始化', 'kl.npki.base.management.i18n_demo_environment_initialization', '402567276064211001', null, null, 4, '/sysConfig/doDemoSystemInit', 'POST', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211002, '完成系统初始化', 'kl.npki.base.management.i18n_complete_system_initialization', '402567276064211002', null, '100_100', 4, '/sysConfig/doCompleteSystemInit', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211003, '自签发站点证书', 'kl.npki.base.management.i18n_self_issued_site_certificate', '402567276064211003', null, '100_100', 4, '/sslServerCert/signSSLCert', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211006, '执行所有服务自检项', 'kl.npki.base.management.i18n_execute_all_service_self_check_items', '402567276064211006', null, '100_1000', 4, '/selfCheck', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211007, '执行指定的服务自检项', 'kl.npki.base.management.i18n_execute_specified_service_self_test_items', '402567276064211007', null, '100_1000', 4, '/selfCheck/*', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211008, '保存角色权限树', 'kl.npki.base.management.i18n_save_role_permission_tree', '402567276064211008', null, '500', 4, '/role/savePermissionTree', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211009, '新增角色', 'kl.npki.base.management.i18n_add_new_role', '402567276064211009', null, '500', 4, '/role/addRole', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211010, '保存区域与语言', 'kl.npki.base.management.i18n_RegionAndLanguage_SAVE_REGION_LANGUAGE', '402567276064211010', null, '100_100', 4, '/regionAndLanguage/save', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211013, '导出带签名日志', 'kl.npki.base.management.i18n_export_signed_file_of_service_logs_i18n_key', '402567276064211013', null, '100_400', 4, '/opLog/export/signedFile', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211014, '查询Excel电子签名信息', 'kl.npki.base.management.i18n_query_excel_electronic_signature_information', '402567276064211014', null, '100_400', 4, '/opLog/export/info', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211015, '管理员登录', 'kl.npki.base.management.i18n_administrator_login', '402567276064211015', null, null, 4, '/login', 'POST', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211016, '保存登录模式', 'kl.npki.base.management.i18n_save_login_mode', '402567276064211016', null, '100_100', 4, '/loginType/save', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211017, '保存日志配置', 'kl.npki.base.management.i18n_save_log_configuration', '402567276064211017', null, '100_100', 4, '/logConfig/save', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211018, '解析License', 'kl.npki.base.management.i18n_parse_license', '402567276064211018', null, '100_500', 4, '/license/parse', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211019, '导入License', 'kl.npki.base.management.i18n_import_license', '402567276064211019', null, '100_500', 4, '/license/import', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211025, '更新身份证书', 'kl.npki.base.management.i18n_update_identity_certificate', '402567276064211025', null, '100_100', 4, '/idCert/update', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211026, '自签发身份证书', 'kl.npki.base.management.i18n_self_issued_identity_certificate', '402567276064211026', null, '100_100', 4, '/idCert/issue', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211027, '测试加密机连接', 'kl.npki.base.management.i18n_test_encryption_machine_connection', '402567276064211027', null, '100_100', 4, '/engineConfig/valid', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211028, '保存加密机配置', 'kl.npki.base.management.i18n_save_encryption_machine_configuration', '402567276064211028', null, '100_100', 4, '/engineConfig/save', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211029, '测试数据库连接', 'kl.npki.base.management.i18n_test_database_connection', '402567276064211029', null, '100_100', 4, '/dbConfig/test', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211030, '保存数据库配置', 'kl.npki.base.management.i18n_save_database_configuration', '402567276064211030', null, '100_100', 4, '/dbConfig/save', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211031, '查询缓存配置', 'kl.npki.base.management.i18n_query_cache_configuration', '402567276064211031', null, '100_100', 4, '/config/cache', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211032, '保存缓存配置', 'kl.npki.base.management.i18n_save_cache_configuration', '402567276064211032', null, '100_100', 4, '/config/cache', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211033, '测试缓存连接', 'kl.npki.base.management.i18n_test_cache_connection', '402567276064211033', null, '100_100', 4, '/config/cache/test', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211034, 'KCSP平台服务同步（新增、更新、删除平台公共服务）', 'kl.npki.base.management.i18n_annotation.kcsp_sync_service', '402567276064211034', null, null, 4, '/cipher/v1/service/sync/thrid', 'POST', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211035, '证书预览解析', 'kl.npki.base.management.i18n_certificate_preview_analysis', '402567276064211035', null, '100_100', 4, '/cert/preview', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211036, '解析证书', 'kl.npki.base.management.i18n_parse_certificate', '402567276064211036', null, '100_100', 4, '/cert/parse', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211039, '日志审计', 'kl.npki.base.management.i18n_log_audit', '402567276064211039', null, '300', 4, '/audit/audit/*', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211040, '导出带签名日志', 'kl.npki.base.management.i18n_export_signed_file_of_service_logs_i18n_key', '402567276064211040', null, '100_400', 4, '/apiLog/export/signedFile', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211041, '查询Excel电子签名信息', 'kl.npki.base.management.i18n_query_excel_electronic_signature_information', '402567276064211041', null, '100_400', 4, '/apiLog/export/info', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211042, '修改密码', 'kl.npki.base.management.i18n_change_password', '402567276064211042', null, '000', 4, '/admin/updatePwd', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211043, '注册管理员', 'kl.npki.base.management.i18n_registration_administrator', '402567276064211043', null, '200_100', 4, '/admin/registerAdmin', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211044, '导入延期管理员证书', 'kl.npki.base.management.i18n_import_extended_administrator_certificate', '402567276064211044', null, '200_100', 4, '/admin/importExtendCert', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211045, '导入管理员证书', 'kl.npki.base.management.i18n_import_administrator_certificate', '402567276064211045', null, '200_100', 4, '/admin/importCert', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211046, '导入证书链', 'kl.npki.base.management.i18n_import_certificate_chain', '402567276064211046', null, '100_100', 4, '/admin/importCertChain', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211057, '查询统一资源列表（树形结构）', 'kl.npki.base.management.i18n_query_unified_resources_tree', '402567276064211057', null, '170', 4, '/unifiedResource/tree', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211058, '查询管理根证书', 'kl.npki.base.management.i18n_query_management_root_certificate', '402567276064211058', null, '100_100', 4, '/trustCert/query/manage', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211059, '查询可信证书列表', 'kl.npki.base.management.i18n_query_the_list_of_trusted_certificates', '402567276064211059', null, '100_100', 4, '/trustCert/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211060, '查看根证书请求信息', 'kl.npki.base.management.i18n_view_detailed_information_of_server_root_certificate_info', '402567276064211060', null, '100_100', 4, '/trustCert/getRootCertRequestInfo', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211061, '导出证书请求', 'kl.npki.base.management.i18n_export_certificate_request', '402567276064211061', null, '100_100', 4, '/trustCert/export/p10', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211062, '导出证书', 'kl.npki.base.management.i18n_export_certificate', '402567276064211062', null, '100_100', 4, '/trustCert/export/cert/*', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211063, '查询管理根证书算法', 'kl.npki.base.management.i18n_query_management_root_certificate_algorithm', '402567276064211063', null, '100_100', 4, '/trustCert/caAsymAlgo/*', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211064, '查询管理根证书算法', 'kl.npki.base.management.i18n_query_management_root_certificate_algorithm', '402567276064211064', null, '100_100', 4, '/trustCert/asymAlgo', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211065, '获取系统信息相关配置', 'kl.npki.base.management.i18n_obtain_system_information_related_configurations', '402567276064211065', null, null, 4, '/sysInfo/sysManager', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211066, '系统日期格式化设置', 'kl.npki.base.management.i18n_system_date_time_settings', '402567276064211066', null, null, 4, '/sysInfo/dataTime', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211067, '查询系统整体配置状态与进度', 'kl.npki.base.management.i18n_query_the_overall_configuration_status_and_progress_of_the_system', '402567276064211067', null, '100_100', 4, '/sysConfig/status', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211068, '获取系统配置模板', 'kl.npki.base.management.i18n_obtain_system_configuration_template', '402567276064211068', null, '100_100', 4, '/sysConfig/queryConfTemplate', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211069, '查询系统当前环境', 'kl.npki.base.management.i18n_query_the_current_environment_of_the_system', '402567276064211069', null, null, 4, '/sysConfig/isCompletedSystemInit', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211070, '查询系统通用配置', 'kl.npki.base.management.i18n_query_system_general_configuration', '402567276064211070', null, '100_900', 4, '/sysConfig/info', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211079, '查看服务器站点证书详细信息', 'kl.npki.base.management.i18n_view_detailed_information_of_server_site_certificate', '402567276064211079', null, '100_100', 4, '/sslServerCert/getSslServerCert', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211080, '查看ssl证书请求信息', 'kl.npki.base.management.i18n_view_detailed_information_of_server_ssl_certificate_info', '402567276064211080', null, '100_100', 4, '/sslServerCert/getSslCertRequestInfo', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211081, '获取签发站点证书的密钥列表', 'kl.npki.base.management.i18n_obtain_the_key_list_for_issuing_site_certificates', '402567276064211081', null, '100_100', 4, '/sslServerCert/getRootCertKeyList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211082, '获取签发站点证书的密钥列表', 'kl.npki.base.management.i18n_obtain_the_key_list_for_issuing_site_certificates', '402567276064211082', null, '100_100', 4, '/sslServerCert/getCaCertKeyList/*', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211084, '查询告警自检数据', 'kl.npki.base.management.i18n_query_alarm_self_test_data', '402567276064211084', null, '000', 4, '/selfCheck/warnings', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211085, '查询所有自检数据', 'kl.npki.base.management.i18n_query_all_self_checking_data', '402567276064211085', null, '100_1000', 4, '/selfCheck/all', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211086, '待审核列表', 'kl.npki.base.management.i18n_pending_review_list', '402567276064211086', null, '400', 4, '/security/checkList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211087, '查看审核详情', 'kl.npki.base.management.i18n_view_audit_details', '402567276064211087', null, '400', 4, '/security/checkDetail', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211088, '运行日志查看', 'kl.npki.base.management.i18n_view_operation_logs', '402567276064211088', null, '100_400', 4, '/runLog/viewLogs', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211089, '运行日志列表查看', 'kl.npki.base.management.i18n_view_the_list_of_running_logs', '402567276064211089', null, '100_400', 4, '/runLog/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211090, '运行日志下载', 'kl.npki.base.management.i18n_download_running_logs', '402567276064211090', null, '100_400', 4, '/runLog/downloadRunLog', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211091, '获取角色的统一资源权限', 'kl.npki.base.management.i18n_get_role_unified_resources', '402567276064211091', null, '500', 4, '/role/*/unifiedResources', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211092, '角色列表查询', 'kl.npki.base.management.i18n_role_list_query', '402567276064211092', null, '500', 4, '/role/roleList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211093, '获取区域与语言枚举列表', 'kl.npki.base.management.i18n_RegionAndLanguage_LIST_REGION_LANGUAGE', '402567276064211093', null, '100_100', 4, '/regionAndLanguage/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211094, '获取区域与语言', 'kl.npki.base.management.i18n_RegionAndLanguage_GET_REGION_LANGUAGE', '402567276064211094', null, '100_100', 4, '/regionAndLanguage/get', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211103, '查询操作日志列表', 'kl.npki.base.management.i18n_query_operation_log_list', '402567276064211103', null, '100_400', 4, '/opLog/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211104, '导出日志', 'kl.npki.base.management.i18n_export_file_of_service_logs_i18n_key', '402567276064211104', null, '100_400', 4, '/opLog/export/file', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211105, '查询操作日志详细信息', 'kl.npki.base.management.i18n_query_operation_log_detailed_information', '402567276064211105', null, '100_400', 4, '/opLog/detail/*', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211106, '注销登录', 'kl.npki.base.management.i18n_log_out_of_login', '402567276064211106', null, '000', 4, '/logout', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211107, '获取登录模式枚举列表', 'kl.npki.base.management.i18n_get_login_mode_enumeration_list', '402567276064211107', null, '100_100', 4, '/loginType/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211108, '获取登录模式', 'kl.npki.base.management.i18n_get_login_mode', '402567276064211108', null, null, 4, '/loginType/get', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211109, '获取日志输出类型', 'kl.npki.base.management.i18n_get_log_output_type', '402567276064211109', null, '100_100', 4, '/logConfig/logOutType/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211110, '获取日志级别类型下拉选项', 'kl.npki.base.management.i18n_get_log_level_type_dropdown_options', '402567276064211110', null, '100_100', 4, '/logConfig/level/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211111, '查询日志配置', 'kl.npki.base.management.i18n_query_log_configuration', '402567276064211111', null, '100_100', 4, '/logConfig/info', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211112, '查询License', 'kl.npki.base.management.i18n_query_license', '402567276064211112', null, '100_500', 4, '/license', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211121, '查询巡检列表', 'kl.npki.base.management.i18n_query_inspection_record_list', '402567276064211121', null, '100_2100', 4, '/inspection/record/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211122, '导出巡检报告', 'kl.npki.base.management.i18n_export_inspection_record', '402567276064211122', null, '100_2100', 4, '/inspection/record/export/*', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211123, '查询巡检结果详情', 'kl.npki.base.management.i18n_query_inspection_record_details', '402567276064211123', null, '100_2100', 4, '/inspection/record/detail/*', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211124, '查询巡检项列表', 'kl.npki.base.management.i18n_query_inspection_item_list', '402567276064211124', null, '100_2100', 4, '/inspection/items', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211125, '查看身份证书请求信息', 'kl.npki.base.management.i18n_view_detailed_information_of_server_id_certificate_info', '402567276064211125', null, '100_100', 4, '/idCert/getIdCertRequestInfo', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211126, '获取身份证书', 'kl.npki.base.management.i18n_obtain_identity_certificate', '402567276064211126', null, '100_100', 4, '/idCert/cert', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211127, '服务自检统计结果', 'kl.npki.base.management.i18n_service_self_inspection_statistics_results', '402567276064211127', null, '000', 4, '/home/<USER>', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211132, '获取管理员签名证书序列号', 'kl.npki.base.management.i18n_obtain_the_serial_number_of_the_administrator_signature_certificate', '402567276064211132', null, null, 4, '/getSignCertSnList', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211133, '获取登录签名数据原文', 'kl.npki.base.management.i18n_retrieve_the_original_login_signature_data', '402567276064211133', null, null, 4, '/getRandom', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211134, '获取登录时所需管理员数量', 'kl.npki.base.management.i18n_obtain_the_required_number_of_administrators_for_login', '402567276064211134', null, null, 4, '/getLoginAdminNum', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211135, '获取验证码图片Base64编码', 'kl.npki.base.management.i18n_i18n_annotation.get_captcha', '402567276064211135', null, null, 4, '/getCaptcha', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211136, '查询密钥索引列表', 'kl.npki.base.management.i18n_query_key_index_list', '402567276064211136', null, '100_100', 4, '/engineConfig/*/keyIndex', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211137, '根据加密机类型查询支持的非对称密钥类型列表', 'kl.npki.base.management.i18n_query_the_list_of_supported_asymmetric_key_types_based_on_the_encryption_machine_type', '402567276064211137', null, '100_100', 4, '/engineConfig/*/keyType/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211138, '查询加密机类型列表', 'kl.npki.base.management.i18n_query_the_list_of_encryption_machine_types', '402567276064211138', null, '100_100', 4, '/engineConfig/type/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211139, '查询配置是否完成', 'kl.npki.base.management.i18n_check_if_the_configuration_is_complete', '402567276064211139', null, '100_100', 4, '/engineConfig/status', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211140, '查询当前配置加密机支持的非对称密钥类型列表', 'kl.npki.base.management.i18n_query_the_list_of_asymmetric_key_types_supported_by_the_current_configured_encryption_machine', '402567276064211140', null, '100_100', 4, '/engineConfig/keyType', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211141, '查询当前配置加密机支持的抗量子加密密钥类型列表', 'kl.npki.base.management.i18n_query_the_list_of_anti_quantum_encryption_key_types_supported_by_the_current_configured_encryption_machine', '402567276064211141', null, '100_100', 4, '/engineConfig/keyType/pqc', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211142, '查询加密机配置', 'kl.npki.base.management.i18n_query_encryption_machine_configuration', '402567276064211142', null, '100_100', 4, '/engineConfig/info', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211143, '根据加密机类型查询支持的非对称密钥类型列表', 'kl.npki.base.management.i18n_query_the_list_of_supported_asymmetric_key_types_based_on_the_encryption_machine_type', '402567276064211143', null, '100_100', 4, '/engineConfig/backup/keyType/list', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211144, '查询配置状态', 'kl.npki.base.management.i18n_query_configuration_status', '402567276064211144', null, '100_100', 4, '/dbConfig/status', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211145, '查询数据库配置', 'kl.npki.base.management.i18n_query_database_configuration', '402567276064211145', null, '100_100', 4, '/dbConfig/info', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211147, '查询统一资源类型常量', 'kl.npki.base.management.i18n_annotation.constant_resource_types', '402567276064211147', null, '170', 4, '/constant/resourceTypes', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211148, '查询HTTP请求类型列表', 'kl.npki.base.management.i18n_annotation.constant_http_method', '402567276064211148', null, '170', 4, '/constant/httpMethod', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211160, '审计日志列表查询', 'kl.npki.base.management.i18n_audit_log_list_query', '402567276064211160', null, '300', 4, '/audit/auditLogList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211161, '审计日志详情查询', 'kl.npki.base.management.i18n_audit_log_details_query', '402567276064211161', null, '300', 4, '/audit/auditLogDetail/*', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211162, '查询服务日志列表', 'kl.npki.base.management.i18n_query_service_log_list', '402567276064211162', null, '100_400', 4, '/apiLog/list/**', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211163, '导出日志', 'kl.npki.base.management.i18n_export_file_of_service_logs_i18n_key', '402567276064211163', null, '100_400', 4, '/apiLog/export/file', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211164, '查询服务日志详细信息', 'kl.npki.base.management.i18n_query_detailed_information_of_service_logs', '402567276064211164', null, '100_400', 4, '/apiLog/detail/*', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211165, '查询密码机支持的密码算法配置', 'kl.npki.base.management.i18n_query_the_password_algorithm_configuration_supported_by_the_password_machine', '402567276064211165', null, '100_100', 4, '/algoConfig/engineAlgorithms', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211166, '获取管理员用户状态', 'kl.npki.base.management.i18n_get_admin_user_status', '402567276064211166', null, '200_100', 4, '/admin/userStatus', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211167, '查询可操作的角色列表', 'kl.npki.base.management.i18n_query_the_list_of_operable_roles', '402567276064211167', null, '200_100', 4, '/admin/operableRoleList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211168, '部署时，获取内置的管理员列表', 'kl.npki.base.management.i18n_during_deployment_retrieve_the_built_in_administrator_list', '402567276064211168', null, '100_100', 4, '/admin/initRoleList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211169, '获取管理员证书状态', 'kl.npki.base.management.i18n_get_admin_cert_status', '402567276064211169', null, '200_100', 4, '/admin/certStatus', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211170, '查询备份管理员列表', 'kl.npki.base.management.i18n_query_the_list_of_backup_administrators', '402567276064211170', null, '200_200', 4, '/admin/backup/adminList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211171, '查询系统管理员列表', 'kl.npki.base.management.i18n_query_the_list_of_system_administrators', '402567276064211171', null, '200_100', 4, '/admin/adminList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211172, '系统管理员信息详情', 'kl.npki.base.management.i18n_system_administrator_information_details', '402567276064211172', null, '200_100', 4, '/admin/adminDetail', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211173, '系统管理员证书SN', 'kl.npki.base.management.i18n_system_administrator_certificate_sn', '402567276064211173', null, null, 4, '/admin/adminCertSn', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211174, '获取监控指标', 'kl.npki.base.service.i18n_annotation.metrics', '402567276064211174', null, null, 4, '/metrics', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211175, '获取健康监控指标', 'kl.npki.base.service.i18n_annotation.metrics_health', '402567276064211175', null, null, 4, '/metrics/health', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211178, '批量删除统一资源', 'kl.npki.base.management.i18n_batch_delete_unified_resources', '402567276064211178', null, '170', 4, '/unifiedResource/batch', 'DELETE', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211179, '删除可信证书链', 'kl.npki.base.management.i18n_delete_trusted_certificate_chain', '402567276064211179', null, '100_100', 4, '/trustCert/chain/*', 'DELETE', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211180, '禁用角色', 'kl.npki.base.management.i18n_disable_role', '402567276064211180', null, '500', 4, '/role/deleteRole/*', 'DELETE', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211183, '删除巡检记录', 'kl.npki.base.management.i18n_delete_inspection_record', '402567276064211183', null, '100_2100', 4, '/inspection/record/delete', 'DELETE', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211184, '注销管理员', 'kl.npki.base.management.i18n_cancel_administrator', '402567276064211184', null, '200_100', 4, '/admin/cancelAdmin/*', 'DELETE', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064211189, '查询统一资源列表（树形结构）', 'kl.npki.base.management.i18n_query_unified_resources_tree', '402567276064221057', null, '500', 4, '/unifiedResource/tree', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402587625619259426, '查询License', 'kl.npki.base.management.i18n_query_license', '402567276064212112', null, '100_100', 4, '/license', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402617286663405785, '查询系统管理员列表', 'kl.npki.base.management.i18n_query_the_list_of_system_administrators', '402617286663405784', null, '100_100', 4, '/admin/adminList', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402617286663405801, '废除顶级管理员', 'kl.npki.base.management.i18n_abolish_super_administrators', '402617286663405800', null, '100_100', 4, '/admin/cancelSuper/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402617286663405812, '解析License', 'kl.npki.base.management.i18n_parse_license', '402617286663405811', null, '100_100', 4, '/license/parse', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402617286663405815, '导入License', 'kl.npki.base.management.i18n_import_license', '402617286663405814', null, '100_100', 4, '/license/import', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402617286663405894, '废除顶级管理员', 'kl.npki.base.management.i18n_abolish_super_administrators', '402617286663405893', null, '200_200', 4, '/admin/cancelSuper/*', 'PUT', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402761211017494529, '获取管理员用户状态', 'kl.npki.base.management.i18n_get_admin_user_status', '402761211017494528', null, '200_200', 4, '/admin/userStatus', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402761211017494532, '获取管理员证书状态', 'kl.npki.base.management.i18n_get_admin_cert_status', '402761211017494531', null, '200_200', 4, '/admin/certStatus', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402761211017494536, '系统管理员信息详情', 'kl.npki.base.management.i18n_system_administrator_information_details', '402761211017494535', null, '200_200', 4, '/admin/adminDetail', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402761211017494539, '注销管理员', 'kl.npki.base.management.i18n_cancel_administrator', '402761211017494538', null, '200_200', 4, '/admin/cancelAdmin/*', 'DELETE', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402774087329448338, '执行所有服务自检项', 'kl.npki.base.management.i18n_execute_all_service_self_check_items', '402774087329448337', null, '000', 4, '/selfCheck', 'POST', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402774087329448363, '查询系统整体配置状态与进度', 'kl.npki.base.management.i18n_query_the_overall_configuration_status_and_progress_of_the_system', '402774087329448362', null, '000', 4, '/sysConfig/status', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402774087329448440, '查询管理根证书算法', 'kl.npki.base.management.i18n_query_management_root_certificate_algorithm', '402774087329448439', null, '200_100', 4, '/trustCert/asymAlgo', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402774087329448443, '查询管理根证书算法', 'kl.npki.base.management.i18n_query_management_root_certificate_algorithm', '402774087329448442', null, '200_200', 4, '/trustCert/asymAlgo', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402774087329448446, '查询当前配置加密机支持的抗量子加密密钥类型列表', 'kl.npki.base.management.i18n_query_the_list_of_anti_quantum_encryption_key_types_supported_by_the_current_configured_encryption_machine', '402774087329448445', null, '200_100', 4, '/engineConfig/keyType/pqc', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402774087329448449, '查询当前配置加密机支持的抗量子加密密钥类型列表', 'kl.npki.base.management.i18n_query_the_list_of_anti_quantum_encryption_key_types_supported_by_the_current_configured_encryption_machine', '402774087329448448', null, '200_200', 4, '/engineConfig/keyType/pqc', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (406306534721650959, '单点登录认证平台信息', 'kl.npki.base.management.i18n_sso_auth_addr', '406306534721650959', null, null, 4, '/getPreSsoAuthConf', 'GET', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (406306534721651917, '系统管理员信息详情', 'kl.npki.base.management.i18n_system_administrator_information_details', '406306534721651916', null, '100_100', 4, '/admin/adminDetail', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (406306534721651918, '保存数据库密码', 'kl.npki.base.management.i18n_save_database_passwd_configuration', '406306534721651918', null, '100_100', 4, '/dbConfig/save/dbPasswd', 'POST', 1, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064321170, '查看日志文件', 'kl.npki.base.management.i18n_export_file_of_view_logs_i18n_key', '402567276064321170', null, '100_400', 4, '/opLog/export/file/temp', 'GET', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064321171, '删除日志文件', 'kl.npki.base.management.i18n_export_file_of_delete_logs_i18n_key', '402567276064321171', null, '100_400', 4, '/opLog/export/file/temp/*', 'DELETE', 0, 0);
INSERT INTO t_resource (id, resource_name, resource_name_i18n_key, resource_code, remark, parent_resource_code, resource_type, resource_url, request_method, is_anonymous, is_delete) VALUES (402567276064321172, '下载日志文件', 'kl.npki.base.management.i18n_export_file_of_download_logs_i18n_key', '402567276064321172', null, '100_400', 4, '/opLog/export/file/temp/download', 'GET', 0, 0);

-- 角色资源中间表初始化sql添加
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447941, '1', '000', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447942, '1', '402567276064211042', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447943, '1', '402567276064211084', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447944, '1', '402567276064211106', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447945, '1', '402567276064211127', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447946, '1', '200', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447947, '1', '200_100', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447948, '1', '402567276064210978', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447949, '1', '402567276064210979', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447950, '1', '402567276064210980', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447951, '1', '402567276064210981', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447952, '1', '402567276064210982', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447953, '1', '402567276064210984', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447954, '1', '402567276064211043', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447955, '1', '402567276064211044', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447956, '1', '402567276064211045', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447957, '1', '402567276064211166', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447958, '1', '402567276064211167', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447959, '1', '402567276064211169', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447960, '1', '402567276064211171', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447961, '1', '402567276064211172', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447962, '1', '402567276064211184', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447963, '1', '200_200', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447964, '1', '402567276064211170', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447965, '1', '402617286663405893', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447966, '1', '402761211017494528', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447967, '1', '402761211017494531', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447968, '1', '402761211017494535', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447969, '1', '402761211017494538', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447971, '2', '000', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447972, '2', '402567276064211042', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447973, '2', '402567276064211084', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447974, '2', '402567276064211106', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447975, '2', '402567276064211127', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447976, '2', '200_200', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447977, '2', '402567276064211170', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447978, '2', '402617286663405893', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447979, '2', '402761211017494528', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447980, '2', '402761211017494531', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447981, '2', '402761211017494535', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447982, '2', '402761211017494538', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447983, '2', '400', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447984, '2', '402567276064210959', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447985, '2', '402567276064210960', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447986, '2', '402567276064211086', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447987, '2', '402567276064211087', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447988, '2', '500', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447989, '2', '402567276064211008', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447990, '2', '402567276064211009', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447991, '2', '402567276064211091', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447992, '2', '402567276064211092', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447993, '2', '402567276064211180', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447994, '2', '402567276064221057', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447995, '2', '170', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447996, '2', '402567276064210950', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447997, '2', '402567276064210951', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447998, '2', '402567276064210989', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329447999, '2', '402567276064211057', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448000, '2', '402567276064211147', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448001, '2', '402567276064211148', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448002, '2', '402567276064211178', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448003, '2', '200', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448005, '4', '000', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448006, '4', '402567276064211042', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448007, '4', '402567276064211084', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448008, '4', '402567276064211106', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448009, '4', '402567276064211127', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448010, '4', '200', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448011, '4', '200_100', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448012, '4', '402567276064210978', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448013, '4', '402567276064210979', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448014, '4', '402567276064210980', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448015, '4', '402567276064210981', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448016, '4', '402567276064210982', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448017, '4', '402567276064210984', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448018, '4', '402567276064211043', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448019, '4', '402567276064211044', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448020, '4', '402567276064211045', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448021, '4', '402567276064211166', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448022, '4', '402567276064211167', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448023, '4', '402567276064211169', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448024, '4', '402567276064211171', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448025, '4', '402567276064211172', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448026, '4', '402567276064211184', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448027, '4', '200_200', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448028, '4', '402567276064211170', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448029, '4', '402617286663405893', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448030, '4', '402761211017494528', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448031, '4', '402761211017494531', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448032, '4', '402761211017494535', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448033, '4', '402761211017494538', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448035, '8', '000', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448036, '8', '402567276064211042', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448037, '8', '402567276064211084', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448038, '8', '402567276064211106', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448039, '8', '402567276064211127', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448040, '8', '200_100', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448041, '8', '402567276064210978', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448042, '8', '402567276064210979', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448043, '8', '402567276064210980', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448044, '8', '402567276064210981', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448045, '8', '402567276064210982', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448046, '8', '402567276064210984', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448047, '8', '402567276064211043', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448048, '8', '402567276064211044', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448049, '8', '402567276064211045', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448050, '8', '402567276064211166', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448051, '8', '402567276064211167', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448052, '8', '402567276064211169', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448053, '8', '402567276064211171', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448054, '8', '402567276064211172', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448055, '8', '402567276064211184', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448056, '8', '200', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448058, '16', '000', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448059, '16', '402567276064211042', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448060, '16', '402567276064211084', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448061, '16', '402567276064211106', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448062, '16', '402567276064211127', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448063, '16', '300', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448064, '16', '402567276064211039', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448065, '16', '402567276064211160', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448066, '16', '402567276064211161', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448068, '32', '000', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448069, '32', '402567276064211042', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448070, '32', '402567276064211084', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448071, '32', '402567276064211106', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448072, '32', '402567276064211127', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448073, '32', '100', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448074, '32', '100_100', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448075, '32', '402567276064210952', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448076, '32', '402567276064210953', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448077, '32', '402567276064210954', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448078, '32', '402567276064210955', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448079, '32', '402567276064210956', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448080, '32', '402567276064210957', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448081, '32', '402567276064210958', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448082, '32', '402567276064210976', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448083, '32', '402567276064210977', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448084, '32', '402567276064210983', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448085, '32', '402567276064210990', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448086, '32', '402567276064210991', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448087, '32', '402567276064210992', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448088, '32', '402567276064211000', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448089, '32', '402567276064211002', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448090, '32', '402567276064211003', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448091, '32', '402567276064211010', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448092, '32', '402567276064211016', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448093, '32', '402567276064211017', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448094, '32', '402567276064211025', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448095, '32', '402567276064211026', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448096, '32', '402567276064211027', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448097, '32', '402567276064211028', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448098, '32', '402567276064211029', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448099, '32', '402567276064211030', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448100, '32', '402567276064211031', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448101, '32', '402567276064211032', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448102, '32', '402567276064211033', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448103, '32', '402567276064211035', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448104, '32', '402567276064211036', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448105, '32', '402567276064211046', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448106, '32', '402567276064211058', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448107, '32', '402567276064211059', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448108, '32', '402567276064211060', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448109, '32', '402567276064211061', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448110, '32', '402567276064211062', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448111, '32', '402567276064211063', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448112, '32', '402567276064211064', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448113, '32', '402567276064211067', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448114, '32', '402567276064211068', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448115, '32', '402567276064211079', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448116, '32', '402567276064211080', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448117, '32', '402567276064211081', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448118, '32', '402567276064211082', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448119, '32', '402567276064211093', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448120, '32', '402567276064211094', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448121, '32', '402567276064211107', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448122, '32', '402567276064211109', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448123, '32', '402567276064211110', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448124, '32', '402567276064211111', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448125, '32', '402567276064211125', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448126, '32', '402567276064211126', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448127, '32', '402567276064211136', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448128, '32', '402567276064211137', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448129, '32', '402567276064211138', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448130, '32', '402567276064211139', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448131, '32', '402567276064211140', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448132, '32', '402567276064211141', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448133, '32', '402567276064211142', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448134, '32', '402567276064211143', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448135, '32', '402567276064211144', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448136, '32', '402567276064211145', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448137, '32', '402567276064211165', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448138, '32', '402567276064211168', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448139, '32', '402567276064211179', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448140, '32', '402567276064212112', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448141, '32', '402617286663405784', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448142, '32', '402617286663405800', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448143, '32', '402617286663405811', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448144, '32', '402617286663405814', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448145, '32', '100_200', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448146, '32', '402567276064210995', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448147, '32', '100_400', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448148, '32', '402567276064211013', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448149, '32', '402567276064211014', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448150, '32', '402567276064211040', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448151, '32', '402567276064211041', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448152, '32', '402567276064211088', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448153, '32', '402567276064211089', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448154, '32', '402567276064211090', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448155, '32', '402567276064211103', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448156, '32', '402567276064211104', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448157, '32', '402567276064211105', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448158, '32', '402567276064211162', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448159, '32', '402567276064211163', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448160, '32', '402567276064211164', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448161, '32', '100_600', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448162, '32', '402567276064210993', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448163, '32', '402567276064210994', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448164, '32', '100_1000', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448165, '32', '402567276064211006', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448166, '32', '402567276064211007', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448167, '32', '402567276064211085', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448168, '32', '100_500', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448169, '32', '402567276064211018', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448170, '32', '402567276064211019', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448171, '32', '402567276064211112', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448172, '32', '100_2100', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448173, '32', '402567276064210964', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448174, '32', '402567276064211121', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448175, '32', '402567276064211122', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448176, '32', '402567276064211123', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448177, '32', '402567276064211124', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448178, '32', '402567276064211183', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448365, '1', '402774087329448362', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448367, '2', '402774087329448362', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448369, '4', '402774087329448362', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448371, '8', '402774087329448362', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448373, '16', '402774087329448362', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448375, '32', '402774087329448362', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448451, '1', '402774087329448439', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448452, '1', '402774087329448445', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448453, '1', '402774087329448442', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448454, '1', '402774087329448448', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448456, '2', '402774087329448442', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448457, '2', '402774087329448448', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448459, '4', '402774087329448439', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448460, '4', '402774087329448445', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448461, '4', '402774087329448442', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448462, '4', '402774087329448448', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448464, '8', '402774087329448439', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402774087329448465, '8', '402774087329448445', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (406306534721651919, '32', '406306534721651916', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (406306534721651920, '32', '406306534721651918', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402567276064421180, '32', '402567276064321170', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402567276064421181, '32', '402567276064321171', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402567276064421182, '32', '402567276064321172', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402567276064421183, '32', '100_900', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402567276064421184, '32', '402567276064211070', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402567276064421185, '1', '402567276064211092', 0);
INSERT INTO t_role_resource_link (id, role_code, resource_code, is_delete) VALUES (402567276064421186, '4', '402567276064211092', 0);

-- 默认根机构
INSERT INTO t_org_mgr(id, org_name, full_name, name_pinyin, org_status, org_code, parent_id, parent_code, org_type,full_code, full_id, order_index, order_full_index, org_level, linkman, telephone, postcode, address,org_desc,create_time, update_time, full_data_hash, is_delete, tenant_id)
VALUES (1000, '顶层机构', '顶层机构', NULL, 0, 1000, -1, -1, 0, 1000, 1000, 0,NULL, 0, NULL, NULL, NULL, NULL,'顶层机构', NULL, NULL,NULL, 0, NULL);



