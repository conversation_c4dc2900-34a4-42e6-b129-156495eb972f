# 配置登录模式，目前取值有pwd, sign, sign_pwd, kcsp_sso, multi_sign_2_of_3, multi_sign_3_of_5
kl.base.login.loginModel=sign

# database
kl.base.datasource.slotName=default
kl.base.datasource.datasource[0].name=
kl.base.datasource.datasource[0].username=
kl.base.datasource.datasource[0].url=
kl.base.datasource.datasource[0].password=

kl.base.datasource.druid.driverClassName=
# 最新版的sharding-jdbc暂不支持此注入方式，参考https://github.com/apache/shardingsphere/issues/21211
#kl.base.datasource.druid.filters=stat,slf4j
kl.base.datasource.druid.initialSize=16
kl.base.datasource.druid.logSlowSql=true
kl.base.datasource.druid.maxActive=32
kl.base.datasource.druid.maxWait=10000
kl.base.datasource.druid.minEvictableIdleTimeMillis=3000000
kl.base.datasource.druid.minIdle=16
kl.base.datasource.druid.poolPreparedStatements=false
kl.base.datasource.druid.slowSqlMillis=3000
kl.base.datasource.druid.testOnBorrow=false
kl.base.datasource.druid.testOnReturn=false
kl.base.datasource.druid.testWhileIdle=true
kl.base.datasource.druid.timeBetweenEvictionRunsMillis=60000
kl.base.datasource.druid.validationQuery=SELECT 1 FROM DUAL
kl.base.datasource.druid.maxEvictableIdleTimeMillis=25200000
kl.base.datasource.druid.useUnfairLock=true
kl.base.datasource.druid.keepAlive=true
kl.base.datasource.druid.maxOpenPreparedStatements=0
# 数据库事务超时时间, -1时永不过时, 默认30分钟
kl.base.transaction.timeoutSeconds=1800

# 系统支持的算法
kl.base.algo.asymmetric=SM2,RSA_2048
kl.base.algo.symmetric=SM4/GCM/NoPadding
kl.base.algo.hash=SM3,SHA-256
kl.base.algo.systemCertAlgo=SM2
kl.base.algo.postQuantum=AIGIS_SIG2_R1,HSS_SM3_W8_H10_R1,ML_DSA_65,CNTR_768_R1,ML_KEM_768
kl.base.algo.support.asymmetric=SM2,RSA_2048,RSA_3072,RSA_4096,PRIME_256_V1,SEC_P384_R1,SEC_P521_R1
kl.base.algo.support.symmetric=AES-128/ECB/PKCS5Padding,AES-192/ECB/PKCS5Padding,AES-256/ECB/PKCS5Padding,SM4/ECB/PKCS5Padding,SM1/ECB/PKCS5Padding,SM4/GCM/NoPadding
kl.base.algo.support.hash=SM3,SHA-1,SHA-224,SHA-256,SHA-384,SHA-512
kl.base.algo.support.systemCertAlgo=SM2,RSA_2048
kl.base.algo.support.postQuantum=ML_DSA,SLH_DSA,ML_KEM,AIGIS,HSS_SM3,CNTR,CTRU

# 日期格式化配置
kl.base.dateFormat.dayPattern=yyyy-MM-dd
kl.base.dateFormat.dateTimePattern=yyyy-MM-dd HH:mm:ss
kl.base.dateFormat.dateTimeMsPattern=yyyy-MM-dd HH:mm:ss.SSS
kl.base.dateFormat.fileDateTimePattern=yyyyMMddHHmmss

# cache
kl.base.cache.enabled=true
# 缓存类型，支持ehcache、redis、caffeine
kl.base.cache.type=caffeine
# 缓存默认过期时间，小于等于0表示永久生效
kl.base.cache.defaultTimeToLiveMills=0
# caffeine缓存堆内存最大条目，条目达到最大会自动淘汰
kl.base.cache.caffeine.maximumSize=10000
# Ehcache缓存堆内存最大条目，条目达到最大会自动扩容
kl.base.cache.ehcache.heapEntries=10000
# Redis运行模式  single:单机模式，cluster:集群模式 sentinel:哨兵模式
kl.base.cache.redis.runMode=single
# Redis服务地址多个地址,分割
kl.base.cache.redis.nodes=
# Redis服务连接超时时间，单位毫秒
kl.base.cache.redis.connectTimeoutMills=10000
# Redis服务读取超时时间，单位毫秒
kl.base.cache.redis.readTimeoutMills=3000
# Redis服务认证用户名
kl.base.cache.redis.username=
# Redis服务认证密码
kl.base.cache.redis.password=
# Redis连接的数据库索引
kl.base.cache.redis.database=0
# 是否启用SSL
kl.base.cache.redis.ssl=false
# Redis哨兵模式配置（可选）
kl.base.cache.redis.sentinel.master=
kl.base.cache.redis.sentinel.password=
# Redis集群模式配置（可选）
kl.base.cache.redis.cluster.dynamicRefreshSources=true
# 首页数据缓存定时任务配置
kl.base.home.enabled=true
kl.base.home.cronExpression=0 0 */1 * * ?
# 资源控制开关，打开时隐藏对应资源
kl.base.resource.controlHideEnable=false
# 需要控制的资源编码集合
kl.base.resource.controlList=

# 系统证书配置
# 管理根证书和CRL下载地址，不设置实体证书不生成CDP和AIA地址，示例：http://127.0.0.1:8801
kl.base.cert.mgrRootDownloadAddress=
# 管理根证书和CRL资源路径，不设置实体证书不生成CDP和AIA地址，示例：download
kl.base.cert.mgrRootDownloadPath=download
# 是否使用PEM格式下载证书和CRL文件
kl.base.cert.enablePemDownloadFormat=true

# 是否启用客户端ip白名单校验
kl.base.web.clientIpWhitelistEnabled=true
# 默认允许所有ipv4的客户端 0.0.0.0/0
kl.base.web.clientIpv4whitelist=0.0.0.0/0
# 默认允许所有ipv6的客户端 ::/0
kl.base.web.clientIpv6whitelist=::/0
# 设置客户端与服务端最大时间差，超出次设置不允许访问系统，默认最大时间差为10分钟
kl.base.web.maxAllowedTimeDriftMs=600000

#字段验证
#校验对象字段名
kl.base.field.validation.rules[0].target=subjectCn,cn,country,org,orgUnit,state,location,province,city,unit,organization,organizationUnit
#规则
kl.base.field.validation.rules[0].rule=^(?!\\s)(?:(?![<>(){}:\\/+]|＜|＞|（|）|｛|｝|：|＼|／|＋]).)+(?<!\\s)$
#中文描述
kl.base.field.validation.rules[0].zhMsg=前后不能有空格,不能包含中英文特殊字符：尖括号、括号、花括号、斜杠、冒号、加号
#英文描述
kl.base.field.validation.rules[0].enMsg=the sequence cannot have any spaces and should not contain special characters such as angle brackets, parentheses, braces, slashes, colons, or plus signs

#校验对象字段名
kl.base.field.validation.rules[1].target=subjectCn,cn,country,org,orgUnit,state,location,province,city,unit,organization,organizationUnit
#规则
kl.base.field.validation.rules[1].rule=^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$
#中文描述
kl.base.field.validation.rules[1].zhMsg=不能在开头或结尾添加以下字符：空格、%20（空格编码）、%0a（换行符编码）、%00（结束符编码）
#英文描述
kl.base.field.validation.rules[1].enMsg=do not add the following characters at the beginning or end of the input:space (including full-width and half-width), %20 (space encoding), %0a (newline encoding), %00 (null byte encoding)

